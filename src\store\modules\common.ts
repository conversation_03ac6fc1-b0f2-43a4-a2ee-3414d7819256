/**
 * commonStore主要用于维护整个系统，公共业务使用到的数据
 */
import store from "@/store"
import { defineStore } from "pinia"
import { getAreaTreeApi, getNowAreaTreeApi } from "@/api/book"
import { getRegionsTreeApi } from "@/api/user"

export const useCommonStore = defineStore("common", {
  state: () => ({

    //记录知识点组卷选中的树形节点
    treeNodeKey: [],
    //地区数据
    areaArr: [] as IArea[],
    nowareaArr: {} as IArea,
  }),
  actions: {
    async getAreaData() {
      const res: any = await getRegionsTreeApi()
      this.areaArr = res.data
    },
    async getNowAreaData() {
      const res: IApiResponseData<IArea> = await getNowAreaTreeApi()
      this.nowareaArr = res.data
    },
    
  },
  persist: true // 这个store需要持久化存储，
})
/** 在 setup 外使用 */
export function useCommonStoreHook() {
  return useCommonStore(store)
}
