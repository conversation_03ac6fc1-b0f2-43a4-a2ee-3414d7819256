<!--手机端测评报告页面-->
    <template>
        <div class="mobile-report-main">
          <!-- 顶部导航栏 -->
          <div class="mobile-header">
            <div class="header-back" @click="goBack">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M15 18L9 12L15 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="header-title">测评报告</div>
            <div class="header-placeholder"></div>
            <div class="test-date-tag">
                测评日期: {{reportData?.createTime ||  "2025-06-30 17:50:44"}}
            </div>
          </div>
      
          <!-- 滚动内容区 -->
          <div class="mobile-content" ref="mobileContentRef">
            <!-- 用户信息卡片 -->
            <div class="user-card">
              <div class="user-info">
                <div class="user-avatar">
                  <img :src="learnNow.avatar || '/api/placeholder/50/50'" alt="头像" class="avatar-img" />
                </div>
                <div class="user-details">
                  <div class="user-name">{{ learnNow.nickName || '用户' }}<span class="semester">{{learnUsers[0]?.gradeName}}{{ learnUsers[0]?.termId == 1 ? '上学期' : '下学期' }}</span></div>
                </div>
              </div>
              
              <!-- 大分数显示 -->
              <!-- <div class="score-display">
                <div class="score-circle">
                  <div class="score-number">{{ formatScore(reportData.correctRate*1) }}</div>
                  <div class="score-unit">分</div>
                </div>
              </div> -->
              <div class="score-fs">
                  <div class="score-number">{{ formatScore(reportData.correctRate*1) }}</div>
                  <div class="score-unit">分</div>
              </div>
              
              <!-- 测评内容 -->
              <div class="test-content">
                <span class="content-label">测评内容:</span>
                <span class="content-text">{{reportData?.title || "北师大版八年级上学期 第1章 勾股定理"}}</span>
              </div>
              
              <!-- 评级显示 -->
               <div>根据测评得分、测评用时、试题难度等综合评估</div>
              <div class="grade-display">
                <div class="grade-text">当前测评等级为：</div>
                <img class="grade-image" :src="getScoreImages(reportData.correctRate)" alt="评级" />
              </div>
              <img class="not-img" style="margin-top: 30px;" width="360px" height="160px" :src="getScoreImage(reportData.correctRate)" alt="">
            </div>
      
            <!-- 统计卡片 -->
            <div class="stats-card">
              <div class="stats-title">
                <span class="title-number">1.</span>
                <span class="title-text">总体情况</span>
              </div>
              
              <div class="stats-grid">
                <!-- 答题数 -->
                <div class="stat-item">
                  <div class="stat-header">
                    <img width="30px" height="30px" src="@/assets/img/entranceAssessment/dts.png" alt="">
                    <span class="stat-label">答题数:{{ reportData.quesCount }}题</span>
                  </div>
                  <div class="stat-chart" style="margin-left: auto;">
                    <div ref="mobileChart1" class="chart-container"></div>
                  </div>
                  <!-- <div class="stat-value">{{ reportData.quesCount }}题</div> -->
                </div>
                
                <!-- 得分 -->
                <div class="stat-item">
                  <div class="stat-header">
                    <img width="30px" height="30px" src="@/assets/img/entranceAssessment/df.png" alt="">
                    <span class="stat-label">得分:{{ formatScore(reportData.correctRate*1) }}分</span>
                  </div>
                  <div class="stat-chart" style="margin-left: auto;">
                    <div ref="mobileChart2" class="chart-container"></div>
                  </div>
                  <div class="stat-value"></div>
                </div>
              </div>
              
              <!-- 详细统计 -->
              <div class="detailed-stats">
                <div class="stats-row">
                  <div class="stats-item">
                    <img width="30px" height="30px" src="@/assets/img/entranceAssessment/zql.png" alt="">
                    <span class="stats-label">正确率</span>
                    <span class="stats-number">{{ formatPercentage(reportData.correctRate) }}%</span>
                  </div>
      
                  <div class="stats-circles">
                        <div class="circle-stat">
                            <div class="circle-label">
                                <div class="dot red" style="background: #007fe9;"></div>
                                <span class=" total" style="color: #007fe9;padding-right: 10px;">答题总数</span>
                                <span style="font-size: 16px;">{{ formatScore(reportData?.quesCount*1) }}</span>
                            </div>
                        </div>
                        <div class="circle-stat">
                            <div class="circle-label">
                                <div class="dot red" style="background: #23cb89;"></div>
                                <span class=" correct" style="color: #23cb89;padding-right: 10px;">答对题数</span>
                                <span style="font-size: 16px;">{{ formatScore(reportData?.correct*1) }}</span>   
                            </div>
                        </div>
                        <div class="circle-stat">
                            <div class="circle-label">
                                <div class="dot red" style="background: #ff2055;"></div>
                                <span class=" wrong" style="color: #ff2055;padding-right: 10px;">答错题数</span>
                                <span style="font-size: 16px;">{{ formatScore(reportData?.mistake*1) }}</span>
                            </div>
                        </div>
                    </div>              
                </div>                       
              <!-- 答题时长柱状图 -->
              <div class="time-chart-section">
                <div class="time-chart-header">
                  <img width="30px" height="30px" src="@/assets/img/entranceAssessment/dtsc.png" alt="">
                  <span class="time-chart-title">答题时长: {{ formattedTrainTime.value}}{{ formattedTrainTime.unit}} (规定{{ reportData.times ? reportData.times : 25}}分钟)</span>
                </div>
                <div class="time-chart-content">
                  <div ref="timeChart" class="time-chart-container"></div>
                  <div class="chart-unit">单位: min</div>
                </div>
                <div v-if="isOvertime" class="overtime-warning">
                  您超出规定答题时间{{ getOvertimeText() }}，评级在得分基础上下调一级
                </div>
              </div>
              </div>
            </div>
      
            <!-- 知识点掌握情况 -->
            <div class="knowledge-card">
              <div class="card-title" style="width: 200px;">
                <span class="title-number">2.</span>
                <span class="title-text">知识点掌握情况分析</span>
              </div>
              <div style="border-bottom: 1px solid #f1f1f1;margin: 20px;padding: 10px 0 30px 0;">
                绿色为强项知识点，黄色为需加强知识点
              </div>
              
              <div class="knowledge-list">
                <div 
                  v-for="(item, index) in reportData?.reportJson?.pointJson" 
                  :key="index"
                  class="knowledge-item"
                >
                  <div class="knowledge-name">{{ item.pointName }}</div>
                  <div class="knowledge-progress">
                    <div class="progress-bar">
                      <div 
                        class="progress-fill" 
                        :style="{ width: formatPercentage(item.accuracy*100) + '%', backgroundColor: getKnowledgeColor(formatPercentage(item.accuracy*100)) }"
                      ></div>
                    </div>
                    <span class="progress-text">{{ formatPercentage(item.accuracy*100) }}%</span>
                  </div>
                </div>
              </div>
            </div>
      
            <!-- 薄弱知识点分析 -->
            <div class="weakness-card">
              <div class="card-title" style="width: 200px;">
                <span class="title-number">3.</span>
                <span class="title-text">薄弱知识点分析</span>
              </div>
              
              <div class="weakness-table">
                <div class="table-header">
                  <div class="header-cell">掌握情况</div>
                  <div class="header-cell">知识点</div>
                  <div class="header-cell">类型</div>
                  <div class="header-cell">分值</div>
                </div>
                <div 
                  v-for="(item, index) in reportData?.reportJson?.pointJson" 
                  :key="index"
                  class="table-row"
                >
                  <div class="table-cell">
                    <div class="status-tag" :class="getStatusClass(item.status)">
                      {{ getStatus(item.status) }}
                    </div>
                  </div>
                  <div class="table-cell point-name">{{ item.pointName }}</div>
                  <div class="table-cell">{{ item.promote || '-' }}</div>
                  <div class="table-cell">{{ item.score || '-' }}</div>
                </div>
              </div>
            </div>
      
            <!-- 学习推荐 -->
            <div class="suggestion-card">
              <div class="card-title">
                <span class="title-number">4.</span>
                <span class="title-text">学习推荐</span>
              </div>

              <div style="color: #333;margin:0 10px; border-bottom: 1px solid #f1f1f1;padding-bottom: 20px;">推荐学习以下名师知识点课程，你定能提高不少</div>
              
              <div class="suggestion-content">
                <!-- <div class="suggestion-header">
                  <img style="width: 20px; height: 20px; margin-right: 8px;" src="@/assets/img/entranceAssessment/xx.png" />
                  <img style="width: 80px; height: 20px;" src="@/assets/img/entranceAssessment/xxtj.png" />
                </div> -->
                
                <!-- <div class="suggestion-desc">
                  学习一下面几个知识点再次测评，你的成绩肯定以提高高不少哦。
                </div> -->
                
                <div class="suggestion-tags">
                  <div 
                    v-for="(point, index) in reportData?.reportJson?.pointJson" 
                    :key="index"
                    class="suggestion-tag"
                    @click="onGovideo(point)"
                  >
                    <img style="width: 14px; height: 14px; margin-right: 4px;" src="@/assets/img/note/wkplay.svg" />
                    {{ point.pointName }}
                  </div>
                </div>
                
                <!-- <div class="suggestion-buttons">
                  <button class="btn-record" @click="answerRecord()">答题记录</button>
                  <button class="btn-correct" @click="continueStudy">订正错题</button>
                </div> -->
              </div>
            </div>
      
            <!-- 二维码分享 -->
            <div class="qr-card">
              <div class="qr-title">扫二维码分享报告</div>
              <div class="qr-container" ref="mobileQrContainer">
                <div v-if="screenshotLoading" class="qr-loading">
                  <div class="loading-spinner"></div>
                  <div class="loading-text">生成中...</div>
                </div>
                <canvas v-else ref="mobileQrCanvas" width="120" height="120"></canvas>
              </div>
            </div>
          </div>
        </div>
      </template>
      
      <script lang="ts" setup>
      import { ref, reactive, onMounted, onUnmounted, nextTick, computed } from 'vue'
      import { useRouter, useRoute } from 'vue-router'
      import { ElMessage } from 'element-plus'
      import QRCode from 'qrcode'
      import * as echarts from 'echarts'
      import { useUserStore } from '@/store/modules/user'
      import { getDetailssApi } from "@/api/training"
      import { uploadApi } from "@/api/user"
      import html2canvas from 'html2canvas'
      import { dataDecrypt, dataEncrypt } from "@/utils/secret"
      
      // 导入评分图片
      import smasImg from '@/assets/img/entranceAssessment/as.png'
      import smaImg from '@/assets/img/entranceAssessment/a.png'
      import smbsImg from '@/assets/img/entranceAssessment/bs.png'
      import smbImg from '@/assets/img/entranceAssessment/b.png'
      import smcImg from '@/assets/img/entranceAssessment/c.png'

      import asImg from '@/assets/img/entranceAssessment/pyramidas.png'
      import aImg from '@/assets/img/entranceAssessment/pyramida.png'
      import bsImg from '@/assets/img/entranceAssessment/pyramidbs.png'
      import bImg from '@/assets/img/entranceAssessment/pyramidb.png'
      import cImg from '@/assets/img/entranceAssessment/pyramidc.png'
      
      const router = useRouter()
      const route = useRoute()
      const userStore = useUserStore()
      
      // 响应式数据
      const learnUsers = JSON.parse(localStorage.learnUsers || "[]") || []
      const learnNow = computed(() => userStore.learnNow || {})
      // const queryData = route.query.data ? dataDecrypt(route.query.data) : {}
      const queryData = route.query 
      
      // 模板引用
      const mobileContentRef = ref<HTMLDivElement>()
      const mobileChart1 = ref<HTMLDivElement>()
      const mobileChart2 = ref<HTMLDivElement>()
      const timeChart = ref<HTMLDivElement>()
      const mobileQrCanvas = ref<HTMLCanvasElement>()
      const mobileQrContainer = ref<HTMLDivElement>()
      
      // 状态
      const loading = ref(false)
      const screenshotLoading = ref(false)
      const uploadedImageUrl = ref<string>('')
      
      // 清理函数存储
      const chartCleanupFunctions = ref<(() => void)[]>([])
      
      // 测试报告数据
      const reportData:any = reactive({
        "trainingId" : "",
        "sourceId" : "",
        "bookId" : "",
        "subject" : 0,
        "source" : 0,
        "title" : "",
        "correct" : "",
        "mistake" : "",
        "correctRate" : "",
        "quesCount" : 0,
        "degree" : null,
        "score" : "0.0",
        "status" : 0,
        "spanTime" : "",
        "createTime" : "",
        "times" : null,
        "trainTime" : "0",
        "reportJson" : {
          "pointJson" :[],
          "quesDegree" :[],
          "pointVos" : null
        },
        "items" : [],
        "reviseCount" : 0,
        "chapterTrainType" : 0,
        "content" : null,
        "pointIds" : [],
        "integral" : null,
        "hierarchy" : 0
      })
      
      // 格式化后的时间
      const formattedTrainTime = computed(() => formatTime(reportData?.trainTime * 1))
      
      // 是否超时
      const isOvertime = computed(() => {
        const actualMinutes = parseInt(formattedTrainTime.value.value) || 0
        const limitMinutes = reportData.times || 25
        return actualMinutes > limitMinutes
      })
      
      // 获取超时文本
      const getOvertimeText = () => {
        const actualMinutes = parseInt(formattedTrainTime.value.value) || 0
        const limitMinutes = reportData.times || 25
        const overtime = actualMinutes - limitMinutes
        return `${overtime}分钟`
      }
      // 根据分数获取对应的图片
      const getScoreImage = (score: number) => {
        if (score >= 90) return asImg    // A+ 图片
        if (score >= 80) return aImg     // A 图片
        if (score >= 70) return bsImg    // B+ 图片
        if (score >= 60) return bImg     // B 图片
        return cImg                      // C 图片（60分以下）
      }
      // 工具函数
      const getScoreImages = (score: number) => {
        if (score >= 90) return smasImg
        if (score >= 80) return smaImg
        if (score >= 70) return smbsImg
        if (score >= 60) return smbImg
        return smcImg
      }
      
      const formatScore = (score: number): string => {
        if (typeof score !== 'number') return '0'
        const fixed = Number(score.toFixed(1))
        if (fixed % 1 === 0) {
          return fixed.toString()
        }
        return fixed.toFixed(1)
      }
      
      const formatPercentage = (value: number): number => {
        value = value * 1
        if (typeof value !== 'number' || isNaN(value)) return 0
        return Math.min(Math.max(Math.round(value), 0), 100)
      }
      
      const getKnowledgeColor = (accuracy: number): string => {
        if (accuracy >= 90) return '#00D4AA'
        if (accuracy >= 80) return '#FFA726' 
        if (accuracy >= 70) return '#FF7043'
        return '#F44336'
      }
      
      const formatTime = (milliseconds: number): { value: string, unit: string } => {
        if (!milliseconds || typeof milliseconds !== 'number') return { value: '0', unit: '秒' }
        
        const totalSeconds = milliseconds / 1000
        
        if (totalSeconds < 60) {
          if (totalSeconds % 1 === 0) {
            return { value: totalSeconds.toString(), unit: '秒' }
          }
          return { value: totalSeconds.toFixed(1), unit: '秒' }
        }
        
        if (totalSeconds < 3600) {
          const minutes = totalSeconds / 60
          if (minutes % 1 === 0) {
            return { value: minutes.toString(), unit: '分钟' }
          }
          return { value: minutes.toFixed(1), unit: '分钟' }
        }
        
        const hours = totalSeconds / 3600
        if (hours % 1 === 0) {
          return { value: hours.toString(), unit: '小时' }
        }
        return { value: hours.toFixed(1), unit: '小时' }
      }
      
      const getStatus = (status) => { 
        if (status == 1) { 
          return '已掌握'
        }else  if (status == 2) { 
          return '不过关'
        }else  if (status == 3) { 
          return '未掌握'
        }
      }
      
      const getStatusClass = (status) => { 
        if (status == 1) { 
          return 'mastered'
        }else  if (status == 2) { 
          return 'not-mastered'
        }else  if (status == 3) { 
          return 'average'
        }
      }
      
      // 图表相关
      const createMobileChart1 = () => {
        nextTick(() => {
          if (!mobileChart1.value) return
          
          const chart = echarts.init(mobileChart1.value)
          const option = {
            animation: true,
            animationDuration: 1000,
            series: [
              {
                type: 'pie',
                radius: ['60%', '80%'],
                center: ['50%', '50%'],
                startAngle: 90,
                data: [
                  {
                    value: reportData?.correct,
                    name: '答对',
                    itemStyle: {
                      color: '#00D4AA',
                      borderRadius: [0, 0, 0, 0]
                    }
                  },
                  {
                    value: reportData?.mistake,
                    name: '答错',
                    itemStyle: {
                      color: '#E5E5E5',
                      borderRadius: [0, 0, 0, 0]
                    }
                  }
                ],
                label: {
                  show: false
                },
                labelLine: {
                  show: false
                },
                silent: true,
                emphasis: {
                  disabled: true
                }
              }
            ],
            graphic: [
              {
                type: 'text',
                left: 'center',
                top: 'center',
                style: {
                  text: `${reportData?.correct*1}/${reportData?.quesCount}`,
                  textAlign: 'center',
                  fill: '#00D4AA',
                  fontSize: 12,
                  fontWeight: 'bold'
                }
              }
            ]
          }
          
          chart.setOption(option)
          
          const resizeChart = () => {
            chart.resize()
          }
          window.addEventListener('resize', resizeChart)
          
          const cleanup = () => {
            window.removeEventListener('resize', resizeChart)
            chart.dispose()
          }
          chartCleanupFunctions.value.push(cleanup)
        })
      }
      
      const createMobileChart2 = () => {
        nextTick(() => {
          if (!mobileChart2.value) return
          
          const chart = echarts.init(mobileChart2.value)
          
          const option = {
            animation: true,
            animationDuration: 1000,
            series: [
              {
                type: 'pie',
                radius: ['60%', '80%'],
                center: ['50%', '50%'],
                startAngle: 90,
                data: [
                  {
                    value: reportData?.correctRate,
                    name: '得分',
                    itemStyle: {
                      color: '#00D4AA',
                      borderRadius: [0, 0, 0, 0]
                    }
                  },
                  {
                    value: 100 - reportData?.correctRate,
                    name: '未得分',
                    itemStyle: {
                      color: '#E5E5E5',
                      borderRadius: [0, 0, 0, 0]
                    }
                  }
                ],
                label: {
                  show: false
                },
                labelLine: {
                  show: false
                },
                silent: true,
                emphasis: {
                  disabled: true
                }
              }
            ],
            graphic: [
              {
                type: 'text',
                left: 'center',
                top: 'center',
                style: {
                  text: `${reportData?.correctRate*1}/100`,
                  textAlign: 'center',
                  fill: '#00D4AA',
                  fontSize: 12,
                  fontWeight: 'bold'
                }
              }
            ]
          }
          
          chart.setOption(option)
          
          const resizeChart = () => {
            chart.resize()
          }
          window.addEventListener('resize', resizeChart)
          
          const cleanup = () => {
            window.removeEventListener('resize', resizeChart)
            chart.dispose()
          }
          chartCleanupFunctions.value.push(cleanup)
        })
      }
      
      const createTimeChart = () => {
  nextTick(() => {
    if (!timeChart.value) return
    
    const chart = echarts.init(timeChart.value)
    
    const limitMinutes = reportData.times || 25
    const actualMinutes = parseInt(formattedTrainTime.value.value) || 0
    const overtimeMinutes = Math.max(0, actualMinutes - limitMinutes)
    const normalMinutes = Math.min(actualMinutes, limitMinutes)
    
    const option = {
      grid: {
        left: '15%',
        right: '20%',
        top: '15%',
        bottom: '25%'
      },
      xAxis: {
        type: 'category',
        data: ['规定时长', '答题时长'],
        axisLabel: {
          fontSize: 12,
          color: '#666'
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        show: true,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          show: true,
          fontSize: 10,
          color: '#999'
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#f0f0f0',
            type: 'solid'
          }
        }
      },
      series: [
        // 规定时长柱
        {
          name: '规定时长',
          type: 'bar',
          stack: 'total',
          data: [limitMinutes, 0],
          itemStyle: {
            color: '#7BA8FF'
          },
          label: {
            show: true,
            position: 'top',
            formatter: function(params) {
              return params.data !== 0 ? params.data : ''
            },
            fontSize: 12,
            fontWeight: 'bold',
            color: '#333'
          },
          barWidth: '50%'
        },
        // 答题时长正常部分
        {
          name: '答题时长正常',
          type: 'bar',
          stack: 'answer',
          data: [0, normalMinutes],
          itemStyle: {
            color: '#7BA8FF'
          },
          label: {
            show: false
          }
        },
        // 答题时长超时部分
        {
          name: '答题时长超时',
          type: 'bar',
          stack: 'answer',
          data: [0, overtimeMinutes],
          itemStyle: {
            color: '#FF9999'
          },
          label: {
            show: overtimeMinutes > 0,
            position: 'top',
            formatter: function() {
              return actualMinutes
            },
            fontSize: 12,
            fontWeight: 'bold',
            color: '#333'
          }
        }
      ]
    }

    // 如果超时，添加标注
    if (isOvertime.value) {
      (option as any).graphic = [
        {
          type: 'text',
          left: '75%',
          top: '25%',
          style: {
            text: `超时${overtimeMinutes}分`,
            fontSize: 12,
            fontWeight: 'bold',
            fill: '#FF6B6B'
          }
        },
        {
          type: 'line',
          left: '70%',
          top: '30%',
          shape: {
            x1: 0,
            y1: 0,
            x2: -15,
            y2: 15
          },
          style: {
            stroke: '#FF6B6B',
            lineWidth: 1
          }
        }
      ]
    }
    
    chart.setOption(option)
    
    const resizeChart = () => {
      chart.resize()
    }
    window.addEventListener('resize', resizeChart)
    
    const cleanup = () => {
      window.removeEventListener('resize', resizeChart)
      chart.dispose()
    }
    chartCleanupFunctions.value.push(cleanup)
  })
}
      
      // 二维码相关
      const generateMobileQRCode = async () => {
        await nextTick()
        
        if (!mobileQrCanvas.value) return
        
        try {
          let shareUrl = window.location.href
          
          // if (uploadedImageUrl.value) {
          //   shareUrl = uploadedImageUrl.value
          // } else {
          //   shareUrl = await captureAndUpload()
          // }
          
          await QRCode.toCanvas(mobileQrCanvas.value, shareUrl, {
            width: 120,
            height: 120,
            margin: 1,
            color: {
              dark: '#000000',
              light: '#FFFFFF'
            }
          })
          
        } catch (error) {
          console.error('生成二维码失败:', error)
          if (mobileQrContainer.value) {
            mobileQrContainer.value.innerHTML = '<i class="el-icon-qrcode" style="font-size: 40px; color: #999;"></i>'
          }
        }
      }
      
      // 截图上传功能
      const captureAndUpload = async (): Promise<string> => {
        try {
          screenshotLoading.value = true
          
          const mobileContent = mobileContentRef.value
          if (!mobileContent) {
            throw new Error('找不到要截图的元素')
          }
      
          // 隐藏二维码区域
          const qrCard = document.querySelector('.qr-card') as HTMLElement
          const originalQrDisplay = qrCard ? qrCard.style.display : ''
          if (qrCard) {
            qrCard.style.display = 'none'
          }
      
          const canvas = await html2canvas(mobileContent, {
            backgroundColor: '#ffffff',
            scale: 1,
            logging: false,
            useCORS: true,
            allowTaint: true,
            foreignObjectRendering: false,
            imageTimeout: 60000
          })
      
          // 恢复二维码区域显示
          if (qrCard) {
            qrCard.style.display = originalQrDisplay
          }
      
          return new Promise((resolve, reject) => {
            canvas.toBlob(async (blob: Blob | null) => {
              if (!blob) {
                reject(new Error('截图生成失败'))
                return
              }
      
              try {
                const formData = new FormData()
                const fileName = `mobile_test_report_${reportData.trainingId}_${Date.now()}.png`
                formData.append('file', blob, fileName)
      
                const uploadResponse = await uploadApi(formData) as any
                
                if (uploadResponse.code === 200 && uploadResponse.data?.url) {
                  const imageUrl = `${uploadResponse.data.url}${uploadResponse.data.key}`
                  uploadedImageUrl.value = imageUrl
                  resolve(imageUrl)
                } else {
                  reject(new Error('上传失败'))
                }
              } catch (error) {
                reject(error)
              }
            }, 'image/png', 0.8)
          })
        } catch (error) {
          console.error('截图失败:', error)
          ElMessage.error('截图失败: ' + (error as Error).message)
          return `${window.location.origin}/report/share?testId=${reportData.testId || 'demo'}&score=${reportData.score}`
        } finally {
          screenshotLoading.value = false
        }
      }
      
      // 页面操作
      const goBack = () => {
        router.go(-1)
      }
      
      const onGovideo = (val:any) =>{
        router.push({
          path: '/note/wkvideo2',
          query: {
            pointId: val.pointId,
            type:'note',
            subject:reportData.subject,
            noteId:queryData.reportId,
            isShow:'hide'
          }
        })
      }
      
      const answerRecord = () => { 
        router.push({
          path: '/ai_percision/entrance_assessment/answer_record',
          query: {
            data: dataEncrypt({
              reportId: queryData.reportId,
              pageSource: '1'
            }),
          }
        })
      }
      
      const continueStudy = () => {
        router.push({
          path: '/note/note_list'
        })
      }
      
      // 数据获取
      const getData = async () => {

        getDetailssApi({trainingId: queryData.reportId}).then((res: any) => {

          if (res.code == 200) {
            Object.assign(reportData, res.data)
            
            nextTick(() => {
              createMobileChart1()
              createMobileChart2()
              createTimeChart()
              
              setTimeout(() => {
                generateMobileQRCode()
              }, 2000)
            })
          }
          loading.value = false
        }).catch((error) => {
          console.error('数据获取失败:', error)
          loading.value = false
        })
      }
      
      // 生命周期
      onMounted(() => {
        getData()
        reportData.testId = route.query.testId as string || 'demo'
        reportData.bookId = route.query.bookId as string || 'demo'
      })
      
      onUnmounted(() => {
        chartCleanupFunctions.value.forEach(cleanup => cleanup())
      })
      </script>
      
      <style lang="scss" scoped>
      .mobile-report-main {
        width: 100vw;
        min-height: 100vh;
        background: linear-gradient(180deg, #6B9AFF 0%, #7BA8FF 50%, #9BBFFF 100%);
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
      }
      
      .mobile-header {
        display: flex;
        // align-items: center;
        justify-content: space-between;
        padding: 24px 16px 16px 16px;
        background: transparent;
        position: relative;
        // z-index: 10;
        height: 230px;
        background: url(@/assets/img/synchronous/topNav.png) no-repeat;
        background-size: 100% 100%;
        .header-back {
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
        }
        
        .header-title {
          font-size: 18px;
          font-weight: 600;
          color: white;
          text-align: center;
          padding-top: 6px;
          position: absolute;
          top: 30px;
          left: 50%;
          transform: translateX(-50%);
        }
        .test-date-tag{
          color: #fff;
          font-size: 12px;
          padding: 6px 12px;
          background: #00000033;
          border-radius: 16px;
          align-self: flex-start;
          margin-top: 160px;
          margin-right: auto;
          position: absolute;
          left: 30px;
          bottom: 60px;
        }
        .header-placeholder {
          width: 40px;
        }
      }
      
      .mobile-content {
        padding: 0 16px 20px 16px;
        margin-top: -30px;
      }
      
      .user-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 16px;
        padding: 20px;
        margin-bottom: 16px;
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        position: relative;
        
        .user-info {
          display: flex;
          align-items: center;
          margin-bottom: 20px;
          
          .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 25px;
            overflow: hidden;
            margin-right: 12px;
            position: absolute;
            top: -20px ;
            left: 20px;
            .avatar-img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
          
          .user-details {
            flex: 1;
            padding-top: 30px;
            .user-name {
              font-size: 18px;
              font-weight: 600;
              color: #333;
              margin-bottom: 4px;
              .semester{
                font-size: 13px;
                color: #5A85EC;
                background: #E5F2FD;
                padding: 4px 8px;
                border-radius: 12px;
                font-weight: 500;
                margin-left: 10px;
              }
            }
            
            .user-grade {
              font-size: 14px;
              color: #5A85EC;
              background: #E5F2FD;
              padding: 2px 8px;
              border-radius: 10px;
              display: inline-block;
              margin-bottom: 4px;
            }
            
            .test-date {
              font-size: 12px;
              color: #666;
            }
          }
        }
                 .score-fs{
           position: absolute;
           top: 20px;
           right: 20px;
           display: flex;
           align-items: baseline;
           line-height: 1;
           
           .score-number{
             font-size: 50px;
             color: red;
             line-height: 1;
             font-weight: 700;align-self: flex-end;
           }
           
           .score-unit{
             color: red;
             font-size: 20px;
             line-height: 1;
             margin-left: 2px;
             align-self: flex-end;
            padding-bottom: 5px;
           }
         }
        .score-display {
          display: flex;
          justify-content: center;
          margin-bottom: 20px;
          
          .score-circle {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 60px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            
            &::before {
              content: '';
              position: absolute;
              inset: 8px;
              background: white;
              border-radius: 52px;
            }
            
            .score-number {
              font-size: 36px;
              font-weight: 700;
              color: #5A85EC;
              position: relative;
              z-index: 2;
              line-height: 1;
            }
            
            .score-unit {
              font-size: 14px;
              color: #5A85EC;
              position: relative;
              z-index: 2;
              margin-top: 4px;
            }
          }
        }
        
        .test-content {
          margin-bottom: 20px;
          text-align: left;
          border-bottom: 2px solid #f1f1f1;
          padding-bottom: 20px;
          .content-label {
            color: #5A85EC;
            font-size: 14px;
            font-weight: 500;
          }
          
          .content-text {
            color: #333;
            font-size: 14px;
            margin-left: 4px;
          }
        }
        
        .grade-display {
          text-align: left;
          display: flex;
          
          .grade-text {
            font-size: 16px;
            color: #333;
            margin-bottom: 12px;
            font-weight: 500;
            padding-top: 10px;
          }
          
          .grade-image {
            width: 120px;
            height: auto;
          }
        }
      }
      
      .stats-card, .knowledge-card, .weakness-card, .suggestion-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 16px;
        // padding: 20px;
        margin-bottom: 16px;
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        padding-top: 10px;
      }
      
      .stats-title, .card-title {
        // display: flex;
        // align-items: center;
        // justify-content: center;
        margin-bottom: 20px;
        // background: red;
        background: #cf4393;
        line-height: 40px;
        border-radius: 0 20px 20px 0;
        width: 120px;
        color: #fff;
        .title-number {
          font-size: 16px;
          font-weight: 700;
          color: #fff;
          margin-right: 8px;
          padding-left: 5px;
        }
        
        .title-text {
          font-size: 16px;
          font-weight: 600;
         color: #fff;
        }
      }
      
      .stats-grid {
      //   display: grid;
      //   grid-template-columns: 1fr 1fr;
      //   gap: 16px;
        
        // margin-bottom: 20px;
        padding:0 20px;
        .stat-item {
          display: flex;
          align-items: center;
          background: rgb(243,246,250);
          padding: 5px 10px;
          border-radius: 5px;
          margin-bottom: 10px;
          
          .stat-header {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            
            .stat-label {
              font-size: 18px;
              color: #333;
              margin-left: 4px;
            }
          }
          
          .stat-chart {
            margin-bottom: 8px;
            
            .chart-container {
              width: 160px;
              height: 160px;
              margin: 0 auto;
            }
          }
          
          .stat-value {
            font-size: 14px;
            font-weight: 600;
            color: #5A85EC;
          }
        }
      }
      
      .detailed-stats {
        padding:0 20px;
        .stats-row {
          display: flex;
          // justify-content: space-around;
          margin-bottom: 16px;
          background: rgb(243,246,250);;
          border-radius: 5px;
          padding: 10px ;
          
          .stats-item {
            display: flex;
            align-items: center;
            
            .stats-label {
              font-size: 18px;
              color: #333;
              margin: 0 8px;
            }
            
            .stats-number {
              font-size: 18px;
              // font-weight: 700;
              // color: #5A85EC;
              margin-left: 8px;
            }
          }
        }
        
        .stats-circles {
        //   display: flex;
        //   justify-content: space-around;
          margin-bottom: 20px;
          margin-left: auto;
          margin-right: 20px;
          margin-top: 20px;
          .circle-stat {
            display: flex;
            align-items: center;
            
            .circle-label {
              display: flex;
              align-items: center;
              border-radius: 10px;
              font-size: 12px;
              color: #666;
              margin-bottom: 8px;
              background: #fff;
              padding: 5px;
              width: 106px;
              text-align: center;
              .dot{
                width: 6px;
                height: 6px;
                border-radius: 100%;
                margin-right: 5px;
                margin-left: 10px;
                background: red;
              }
              .red{

              }
            }
            
            .circle-value {
              width: 36px;
              height: 36px;
              border-radius: 18px;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 14px;
              font-weight: 600;
              color: white;
              margin: 0 auto;
              
              &.total {
                background: #9E9E9E;
              }
              
              &.correct {
                background: #00D4AA;
              }
              
              &.wrong {
                background: #FF6B6B;
              }
            }
          }
        }
        
        .time-stats {
          .time-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            .time-flex {
              display: flex;
              align-items: center;
              
              .time-label {
                font-size: 14px;
                color: #333;
                margin-left: 4px;
              }
            }
            
            .time-value {
              font-size: 16px;
              font-weight: 600;
              color: #ff6b6b;
              
              .time-unit {
                font-size: 12px;
                margin-left: 2px;
              }
            }
          }
        }
        
        .time-chart-section {
          background: rgb(243,246,250);
          border-radius: 8px;
          padding: 15px;
          margin-top: 16px;
          
          .time-chart-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            
            .time-chart-title {
              font-size: 14px;
              color: #333;
              margin-left: 8px;
              font-weight: 500;
            }
          }
          
          .time-chart-content {
            .time-chart-container {
              width: 100%;
              height: 140px;
              margin-bottom: 8px;
              margin-left: 20px;
            }
            
            .chart-unit {
              text-align: left;
              font-size: 12px;
              color: #666;
              margin-left: 20px;
            }
          }
          
          .overtime-warning {
            margin-top: 12px;
            text-align: center;
            font-size: 12px;
            color: #FF6B6B;
            font-weight: 500;
          }
        }
      }
      
      .knowledge-list {
        padding: 0 20px 20px 20px;
        margin-bottom: 20px;
        .knowledge-item {
          margin-bottom: 16px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .knowledge-name {
            font-size: 14px;
            color: #333;
            margin-bottom: 8px;
            font-weight: 500;
          }
          
          .knowledge-progress {
            display: flex;
            align-items: center;
            
            .progress-bar {
              flex: 1;
              height: 8px;
              background: #f0f0f0;
              border-radius: 4px;
              overflow: hidden;
              margin-right: 8px;
              
              .progress-fill {
                height: 100%;
                border-radius: 4px;
                transition: width 0.3s ease;
              }
            }
            
            .progress-text {
              font-size: 12px;
              color: #666;
              font-weight: 500;
              min-width: 40px;
            }
          }
        }
      }
      
      .weakness-table {
        .table-header {
          display: grid;
          grid-template-columns: 80px 1fr 60px 50px;
          gap: 8px;
          padding: 12px 0;
          border-bottom: 1px solid #f0f0f0;
          
          .header-cell {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            text-align: center;
          }
        }
        
        .table-row {
          display: grid;
          grid-template-columns: 80px 1fr 60px 50px;
          gap: 8px;
          padding: 12px 0;
          border-bottom: 1px solid #f5f5f5;
          
          &:last-child {
            border-bottom: none;
          }
          
          .table-cell {
            font-size: 12px;
            color: #333;
            display: flex;
            align-items: center;
            justify-content: center;
            
            &.point-name {
              justify-content: flex-start;
              line-height: 1.3;
            }
          }
        }
      }
      
      .status-tag {
        padding: 4px 8px;
        border-radius: 10px;
        font-size: 10px;
        font-weight: 500;
        color: white;
        text-align: center;
        
        &.not-mastered {
          background: linear-gradient(to right, #F07F4C, #C95656);
        }
        
        &.average {
          background: linear-gradient(to right, #F6D22B, #F29500);
        }
        
        &.mastered {
          background: linear-gradient(to right, #08D8B8, #00B392);
        }
      }
      
      .suggestion-content {
        .suggestion-header {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 12px;
        }
        
        .suggestion-desc {
          font-size: 14px;
          color: #333;
          text-align: center;
          margin-bottom: 16px;
          line-height: 1.4;
        }
        
        .suggestion-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          margin-bottom: 20px;
          padding-top: 20px;
          
          .suggestion-tag {
            background: #F5F5F5;
            color: #009C7F;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: background 0.3s ease;
            
            &:active {
              background: #E0E0E0;
            }
          }
        }
        
        .suggestion-buttons {
          display: flex;
          gap: 12px;
          
          button {
            flex: 1;
            padding: 12px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: opacity 0.3s ease;
            
            &:active {
              opacity: 0.7;
            }
            
            &.btn-record {
              background: #F5F5F5;
              color: #666;
            }
            
            &.btn-correct {
              background: #00C9A3;
              color: white;
            }
          }
        }
      }
      
      .qr-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 16px;
        padding: 20px;
        text-align: center;
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        
        .qr-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          margin-bottom: 16px;
        }
        
        .qr-container {
          display: flex;
          justify-content: center;
          
          canvas {
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }
          
          .qr-loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 120px;
            height: 120px;
            
            .loading-spinner {
              width: 20px;
              height: 20px;
              border: 2px solid #f3f3f3;
              border-top: 2px solid #5A85EC;
              border-radius: 50%;
              animation: spin 1s linear infinite;
              margin-bottom: 8px;
            }
            
            .loading-text {
              font-size: 12px;
              color: #666;
            }
          }
        }
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      // 响应式适配更小的屏幕
      @media (max-width: 375px) {
        .mobile-content {
          padding: 0 12px 20px 12px;
        }
        
        .user-card, .stats-card, .knowledge-card, .weakness-card, .suggestion-card, .qr-card {
          padding: 16px;
        }
        
        .score-circle {
          width: 100px !important;
          height: 100px !important;
          border-radius: 50px !important;
          
          &::before {
            inset: 6px !important;
            border-radius: 44px !important;
          }
          
          .score-number {
            font-size: 30px !important;
          }
        }
      }
      </style> 