import CryptoJS from "crypto-js"

// 必须为16、24、32位
const KEY = "W7oCufy99F6enlcR"
const IV = "5nwoWBsZBA4ujTkC"

/**
 * AES加密 ：字符串 key iv  返回base64
 */
export const Encrypt = (word: any, keyStr = KEY, ivStr = IV): any => {
  const key = CryptoJS.enc.Utf8.parse(keyStr)
  const iv = CryptoJS.enc.Utf8.parse(ivStr)
  const srcs = CryptoJS.enc.Utf8.parse(word)
  const encrypted = CryptoJS.AES.encrypt(srcs, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  })
  return encrypted.toString()
}

/**
 * AES 解密 ：字符串 key iv  返回base64
 *
 * @return {string}
 */
export const Decrypt = (word: any, keyStr = KEY, ivStr = IV): any => {
  const key = CryptoJS.enc.Utf8.parse(keyStr)
  const iv = CryptoJS.enc.Utf8.parse(ivStr)
  const decrypt = CryptoJS.AES.decrypt(word, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  })
  return CryptoJS.enc.Utf8.stringify(decrypt)
}

/**
 * 传参加密 ：字符串 key iv  返回base64
 */
export const dataEncrypt = (word: any, keyStr = KEY): any => {
  return CryptoJS.AES.encrypt(JSON.stringify(word), keyStr).toString()
}

/**
 * 传参解密 ：字符串 key iv  返回base64
 *
 * @return {string}
 */
export const dataDecrypt = (word: any, keyStr = KEY): any => {
  const bytes = CryptoJS.AES.decrypt(word, keyStr)
  const aa = bytes.toString(CryptoJS.enc.Utf8)
  return JSON.parse(aa)
}

/**
 * 前端地址AES加密 ：字符串 key iv  返回base64
 */
export const UrlEncrypt = (word: any, keyStr = KEY): any => {
  const key = CryptoJS.enc.Utf8.parse(keyStr)
  // 生成随机IV(每次加密都不同)
  const iv = CryptoJS.lib.WordArray.random(16)
  const srcs = CryptoJS.enc.Utf8.parse(word)
  const encrypted = CryptoJS.AES.encrypt(srcs, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  }).toString()
  const safeCiphertext = encrypted.replace(/\+/g, "-").replace(/\//g, "_").replace(/=+$/, "")
  // 返回 "IV:密文"
  return iv.toString() + ":" + safeCiphertext
}

/**
 * 前端地址AES 解密 ：字符串 key iv  返回base64
 *
 * @return {string}
 */
export const UrlDecrypt = (word: any, keyStr = KEY): any => {
  const originalCiphertext = word.replace(/-/g, "+").replace(/_/g, "/")
  const key = CryptoJS.enc.Utf8.parse(keyStr)
  const parts = originalCiphertext.split(":")
  const iv = CryptoJS.enc.Hex.parse(parts[0])
  const decrypt = CryptoJS.AES.decrypt(parts[1], key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  })
  return CryptoJS.enc.Utf8.stringify(decrypt)
}
/**
 * 合并对象属性更新值 ：后者属性值更新到前者，单只返回前者原有的属性
 *
 * @return {object}
 */
export const mergeObject = (obj1: any, obj2: any): any => {
  for (const key in obj2) {
    if (Object.prototype.hasOwnProperty.call(obj2, key)) {
      obj1[key] = obj2[key]
    }
  }
  return obj1
}
/**
 * 深层合并对象属性：递归地将后者属性值更新到前者，只返回前者原有的结构
 *
 * @param {object} obj1 - 目标对象（将被修改）
 * @param {object} obj2 - 源对象（提供更新值）
 * @return {object} 返回修改后的obj1
 */
export const deepMergeObject = (obj1: any, obj2: any): any => {
  // 处理非对象或null的情况
  if (typeof obj1 !== "object" || obj1 === null) {
    return typeof obj2 === "object" && obj2 !== null ? { ...obj2 } : obj2
  }
  if (typeof obj2 !== "object" || obj2 === null) {
    return { ...obj1 }
  }

  // 创建新对象而不是修改原对象
  const result = { ...obj1 }

  // 遍历obj2的所有属性
  for (const key in obj2) {
    if (Object.prototype.hasOwnProperty.call(obj2, key)) {
      // 如果obj1也有这个key
      if (Object.prototype.hasOwnProperty.call(obj1, key)) {
        // 递归处理对象类型的值
        if (
          typeof obj1[key] === "object" &&
          obj1[key] !== null &&
          typeof obj2[key] === "object" &&
          obj2[key] !== null
        ) {
          result[key] = deepMergeObject(obj1[key], obj2[key])
        } else {
          // 非对象类型直接使用obj2的值
          result[key] = obj2[key]
        }
      } else {
        // obj1没有的key，直接添加
        result[key] = obj2[key]
      }
    }
  }
  return result
}
