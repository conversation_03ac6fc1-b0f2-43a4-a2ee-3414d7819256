import store from "@/store"
import { defineStore } from "pinia"
import router from "@/router"

export const useRouteStore = defineStore("routeStore", {
  state: () => {
    return {
      token: "",
      menuList: [] as any[],
    }
  },
  actions: {
    initMenuList() {
        this.menuList = []
        const routeArr = router.options.routes
        routeArr.forEach((item: any) => {
            if(item.meta?.title) {
                if(item.name == router.currentRoute.value.matched[0]?.name) {
                    item.selected = true
                } else {
                    item.selected = false
                }
                this.menuList.push(item)
            }
        })
    },
    setSelectMenu(name: any) {
      this.menuList.forEach((item: any) => {
        if(item.name == name) {
            item.selected = true
        } else {
            item.selected = false
        }
    })
    },
  },
})
/** 在 setup 外使用 */
export function useRouteStoreHook() {
    return useRouteStore(store)
}