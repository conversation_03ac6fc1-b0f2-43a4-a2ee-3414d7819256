<template>
  <div class="area-box">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <span>正在加载区域数据...</span>
    </div>
    
    <!-- 区域列表 -->
    <div v-else v-for="(item, index) in areaArr" :label="item.id" :key="item.id" class="area-item">
      <div
        v-if="item.type < 0"
        class="right-menu-avatar"
        :class="item.isSelect ? 'is-selected-item' : ''"
        @click="selectItem(index, -1, item)"
      >
        {{ item.name }}
      </div>
       <template v-else>
         <div :class="item.isSelect ? 'is-selected-item' : ''" class="right-menu-avatar">
           {{ item.name }}
         </div>
         <el-select v-model="item.name" placeholder="请选择" class="right-menu-item" style="width: auto">
           <el-option v-for="(it,ind) in item.children" :key="it.key" :label="it.name" :value="it.name" @click="selectItem(index, ind, it)">
           </el-option>
         </el-select>
       </template>
    </div>
    
    <!-- 错误重试 -->
    <div v-if="!loading && areaArr.length === 0" class="error-container">
      <p>区域数据加载失败</p>
      <button @click="retryLoad" class="retry-btn">重新加载</button>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted } from "vue"
import { useCommonStore } from "@/store/modules/common"

const commonStore = useCommonStore()
const emit = defineEmits(["getRegionId"])
const areaArr = ref<any[]>([])
const nowareaArr = ref({} as any)
const loading = ref(false)
const retryCount = ref(0)
const maxRetries = 3

onMounted(async () => {
  await initAreaData()
})

// 初始化区域数据
const initAreaData = async () => {
  try {
    loading.value = true
    
    // 获取区域数据
    await getAreaData()
    
    // 获取当前定位数据
    await getCurrentLocationData()
    
    // 处理定位匹配
    await handleLocationMatch()
    
  } catch (error) {
    console.error('初始化区域数据失败:', error)
    
    // 如果失败且重试次数未达到上限，进行重试
    if (retryCount.value < maxRetries) {
      retryCount.value++
      console.log(`正在重试... (${retryCount.value}/${maxRetries})`)
      setTimeout(() => {
        initAreaData()
      }, 1000 * retryCount.value) // 递增延迟重试
    } else {
      console.error('多次重试后仍然失败，使用默认数据')
      // 设置默认的"全部"选项
      areaArr.value = [{
        id: 0,
        isSelect: true,
        name: "全部",
        sname: "全部",
        type: "-1"
      }]
    }
  } finally {
    loading.value = false
  }
}

// 获取当前定位数据
const getCurrentLocationData = async () => {
  try {
    // 确保获取当前定位数据
    if (!commonStore.nowareaArr || Object.keys(commonStore.nowareaArr).length === 0) {
      await commonStore.getNowAreaData()
    }
    
    nowareaArr.value = commonStore.nowareaArr || {}
    nowareaArr.value.isSelect = false
  } catch (error) {
    console.warn('获取当前定位数据失败:', error)
    nowareaArr.value = {}
  }
}

// 处理定位匹配
const handleLocationMatch = async () => {
  const list = areaArr.value
  const name = nowareaArr.value?.name || ''
  
  if (name && list.length > 0) {
    let x = 0, y = 0
    let found = false
    
    for (let i of list) {
      if (i.name === name) {
        // 直辖市
        selectItem(x, -1, i)
        found = true
        break
      }
      
      if (i.children) {
        // 带省市
        y = 0
        for (let n of i.children) {
          if (n.name === name) {
            list[x].name = name
            list[x].sname = name
            selectItem(x, y, n)
            found = true
            break
          }
          y++
        }
        if (found) break
      }
      x++
    }
    
    if (found) {
      areaArr.value = [...list] // 触发响应式更新
    }
  }
}

//获取地区数据
const getAreaData = async function () {
  try {
    // 检查store中是否有数据
    if (commonStore.areaArr.length === 0) {
      console.log('正在获取区域数据...')
      await commonStore.getAreaData()
    }
    
    // 验证数据是否成功获取
    if (commonStore.areaArr.length === 0) {
      throw new Error('区域数据获取失败或为空')
    }
    
    // 深拷贝数据，避免直接修改store数据
    let list = JSON.parse(JSON.stringify(addKey(commonStore.areaArr)))
    
    // 处理城市children，添加省份选项
    for (let i of list) {
      if (i.children && i.children.length > 0) {
        let children2: any = []
        // 添加省份本身作为选项
        children2.push({
          id: i.id,
          name: i.name,
          sname: i.sname,
          type: "0"
        })
        // 添加城市选项
        for (let n of i.children) {
          children2.push(JSON.parse(JSON.stringify(n)))
        }
        i.children = children2
      }
    }
    
    // 在最前面添加"全部"选项
    list.unshift({
      id: 0,
      isSelect: false,
      name: "全部",
      sname: "全部",
      type: "-1"
    })
    
    areaArr.value = list
    console.log('区域数据获取成功，共', list.length, '个区域')
    
  } catch (error) {
    console.error('获取区域数据时发生错误:', error)
    throw error // 重新抛出错误，让上层处理
  }
}

// 选中操作
const selectItem = (firstind: number, secondInd: number, data: any) => {
  try {
    // 选中变色
    let list = [...areaArr.value] // 创建副本避免直接修改
    let z = 0
    
    for (let i of list) {
      i.isSelect = false
      if (i.children) {
        // 还原省名
        if (z !== firstind) {
          i.name = i.children[0]?.name || i.name
          i.sname = i.children[0]?.sname || i.sname
        }
      }
      z++
    }
    
    if (secondInd === -1) {
      // 直辖市
      if (list[firstind]) {
        list[firstind].isSelect = true
      }
    } else {
      // 省市
      if (list[firstind]) {
        list[firstind].isSelect = true
      }
    }
    
    // 更新数据
    areaArr.value = list
    
    // 发送事件
    emit("getRegionId", data.id)
  } catch (error) {
    console.error('选择区域时发生错误:', error)
  }
}

// 重试加载
const retryLoad = () => {
  retryCount.value = 0 // 重置重试计数
  initAreaData()
}

//递归添加是否选中标志
const addKey = (data: any[]) => {
  if (!Array.isArray(data)) return []
  
  data.forEach((item: any) => {
    item.isSelect = false
    if (item.children?.length > 0) {
      addKey(item.children)
    }
  })
  return data
}
</script>
<style lang="scss" scoped>
.area-box {
  display: contents;
  
  .loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #666;
    font-size: 14px;
    
    .loading-spinner {
      width: 16px;
      height: 16px;
      border: 2px solid #f3f3f3;
      border-top: 2px solid #5A85EC;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: 8px;
    }
  }
  
  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    color: #999;
    
    p {
      margin: 0 0 10px 0;
      font-size: 14px;
    }
    
    .retry-btn {
      padding: 6px 12px;
      background: #5A85EC;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      
      &:hover {
        background: #4674E1;
      }
    }
  }
  
  .area-item {
    margin-right: 1.875rem;
    display: flex;
    cursor: pointer;
    align-items: center;
    margin-bottom: .9375rem;
    .right-menu-avatar {
      color: #4d4d4d;
      font-size: .875rem;
      font-weight: 700;
    }
  }
}

/* 旋转动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.now-location {
  height: 1.25rem;
  color: #4d4d4d;
  font-weight: 700;
}
.name-box {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.png-area {
  margin-left: 1.25rem;
  width: .875rem;
  height: .6875rem;
}
.selected-box {
  width: 100%;
  .tag {
    margin-right: .375rem;
  }
}
.clear-all {
  color: #666666;
  display: inline-block;
  width: 2.375rem;
  height: 1.25rem;
  border-radius: .25rem;
  font-size: .75rem;
  text-align: center;
  border: .0625rem solid #999;
  line-height: 1.25rem;
  cursor: pointer;
}
:deep(.el-tag) {
  background: var(--percision-tecah-main-color);
  position: relative;
  color: #f1f3f5;
  justify-content: flex-start;
}
:deep(.el-tag__close) {
  color: #fff;
  &:hover {
    background: var(--percision-tecah-main-color);
  }
}
.png-location {
  margin-right: .25rem;
}
.area-dropdown {
  height: 18.75rem;
  overflow: auto;
}
.is-selected-item {
  border-radius: .25rem;
  width: 100%;
  background: #e3fff7;
  color: var(--percision-tecah-select-font-color) !important;
  font-weight: bold;
  padding: .3125rem .625rem;
}

:deep(.right-menu-item .el-select__wrapper){
  margin: 0 0 0 -3.25rem;
  cursor: pointer;
}

.right-menu-item{
  width: auto;
  opacity: 0;
}
:deep(.el-select__placeholder){
  display: none;
}
:deep(.el-select__selection){
  display: none;
}
</style>
