//统计学练时长
import {
  setStudyTimeApi,
  addIntegral<PERSON>pi
} from "@/api/user"
//可带学科的页面
const path = [
  'note/note_list',
  'note/wkvideo2',
  'note/wkvideo',
  'teach_room/teach_list',
  'teach_room/teach_collect',
  'teach_room/teach_video2',
  'teach_room/teach_video',
  'teach_room/teach_view',
  'ai_percision/knowledge_graph_detail/regarding_learning',
  'ai_percision/knowledge_graph_detail',
  'ai_percision/knowledge_graph',
]

export const subList = {
  0: '未知',
  10: '小学数学',
  11: '小学语文',
  12: '小学英语',
  14: '小学科学',
  20: '初中数学',
  21: '初中物理',
  22: '初中化学',
  23: '初中生物',
  24: '初中科学',
  25: '初中地理',
  26: '初中语文',
  27: '初中英语',
  28: '初中政治',
  29: '初中历史',
  30: '高中数学',
  31: '高中物理',
  32: '高中化学',
  33: '高中生物',
  35: '高中地理',
  36: '高中语文',
  37: '高中英语',
  38: '高中政治',
  39: '高中历史'
}

const subKey = {
  未知: 0,
  科学: 14,
  数学: 20,
  物理: 21,
  化学: 22,
  生物: 23,
  地理: 25,
  语文: 26,
  英语: 27,
  道法: 28,
  政治: 38,
  历史: 29
}

// 统计学科时长-每秒
let timeFun = null
export function learnTime() {
  if(!timeFun){
    timeFun = setInterval(() => {
      const learnNow = localStorage.learnNow ? JSON.parse(localStorage.learnNow) : ''
      if (!learnNow) {
        return
      }
      //判断路由
      const route = location.href
      for (let i of path) {
        if (route.indexOf(i) > -1) {
          // 带学科的页面
          const key = localStorage.learnKey
          if (key) {
            saveTime(key)
          }
        }
      }
    }, 1000)
  }
}
//保存时间加1秒
export function saveTime(key) {
  //设置学科数组
  const list = JSON.parse(localStorage.learnTime || '[]')
  if (!list.length) {
    for (const i in subList) {
      list.push({
        key: i, //学科id
        name: subList[i], //学科名称
        time: 0, //时间，秒
        total: 0 //总时长，分
      })
    }
    localStorage.learnTime = JSON.stringify(list)
  }
  //获取学科索引
  let index = ''
  for (const n in list) {
    if (list[n].key == key) {
      index = n
    }
  }
  list[index].time++
  // 每60秒,time重置，total累加1
  if (list[index].time >= 60) {
    list[index].time = 0
    list[index].total++
    //能被10整除，满10分钟清0
    if(list[index].total% 10 === 0){
      addCoin()
    }
    console.log(key,"dayin一下 saveTimesaveTimesaveTime")
    setStudyTime(key)
  }
  localStorage.learnTime = JSON.stringify(list)
}

//记录学习学科
export function setLearnKey(key, nocheck) {
  learnTime()
  //判断学习用户
  const learnNow = localStorage.learnNow ? JSON.parse(localStorage.learnNow) : ''
  if (!learnNow) {
    return
  }
  //不做高中小学科判断
  if (nocheck) {
    localStorage.learnKey = key
    return
  }
  key = checkKey(key)
  //取第二位数，确定学科
  key = Number(key)
  let key2 = Number((key + '').substring(1))
  const gradeId = Number(learnNow.gradeId)
  if (gradeId == 6) {
    //6年级五四版处理，0数6语7英3生5地8道9史
    const {
      versions
    } = learnNow
    let isfive4 = 0
    for (const i of versions) {
      const five4 = i.editionName.indexOf('五四') > -1,
        name = i.subjectName
      if (key2 == 5 && name == '地理') {
        key2 = 25
        isfive4 = 1
      }
      if (five4) {
        if (key2 == 0 && name == '数学') {
          key2 = 20
          isfive4 = 1
        } else if (key2 == 6 && name == '语文') {
          key2 = 26
          isfive4 = 1
        } else if (key2 == 7 && name == '英语') {
          key2 = 27
          isfive4 = 1
        } else if (key2 == 3 && name == '生物') {
          key2 = 23
          isfive4 = 1
        } else if (key2 == 8 && name == '道法') {
          key2 = 28
          isfive4 = 1
        } else if (key2 == 9 && name == '历史') {
          key2 = 29
          isfive4 = 1
        }
      }
    }
    if (!isfive4) {
      key2 = setPrimary(key, key2)
    }
  } else if (gradeId < 7) {
    //小学
    key2 = setPrimary(key, key2)
  } else if (gradeId < 10) {
    //初中
    key2 += 20
  } else {
    //高中
    key2 += 30
  }
  localStorage.learnKey = key2
}

//设置小学学科
export function setPrimary(key, key2) {
  //小学匹配处理,11语文/12英语/14科学，与高初中学科不一致
  if (key == 26 || key == 36) {
    key2 = 11
  } else if (key == 27 || key == 37) {
    key2 = 12
  } else {
    key2 += 10
  }
  return key2
}

//判断学科为中文转数值
export function checkKey(key) {
  const reg = new RegExp('^[\u4e00-\u9fa5]+$')
  if (!reg.test(key)) {
    return key
  } else {
    return subKey[key]
  }
}

//记录学习时长
export function setStudyTime(key) {
  if (key) {
    const param = {
      subject: key,
      source:3
    }
    setStudyTimeApi(param)
  }
}

// 学练时长-添加积分
const addCoin = () => {
  const learnNow = localStorage.learnNow ? JSON.parse(localStorage.learnNow) : ''
  let param = {
    learnUserId: learnNow.learnId,
    type: 0, //0=加积分 1=减积分
    integral: 1, //积分
    source: 4, //0=登陆5分 1=做题得分 2= 学习视频课程 3=看视频知识点 4学练时长 5兑换物品
    isData: 0 //ture:机构排名 false：全部排名
  }
  addIntegralApi(param)
}