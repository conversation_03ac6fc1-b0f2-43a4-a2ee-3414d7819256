import { request } from "@/utils/axios"
const SDKPORT = "/api/xiaoyeoo/sdkapi/analyse"

/** (新)显示闯关列表 */
export function getStudyChapterApi(params: Object) {
  return request<IApiResponseData<IVersionInfo[]>>({
    url: `${SDKPORT}/showIndividuationTrain`,
    method: "GET",
    params
  })
}
/** (改_新)创建个性化闯关训练 */
export function individuationTrainApi(data: Object) {
  return request<IApiResponseData<IVersionInfo[]>>({
    url: `${SDKPORT}/individuationTrain`,
    method: "POST",
    data
  })
}
/** (新)用户学情报告 */
export function userAnalyseApi(params: Object) {
  return request<IApiResponseData<IVersionInfo[]>>({
    url: `${SDKPORT}/userAnalyse`,
    method: "GET",
    params
  })
}
/** 章节学情报告 */
export function userAnalyseToChapterApi(params: Object) {
  return request<IApiResponseData<IVersionInfo[]>>({
    url: `${SDKPORT}/userAnalyseToChapter`,
    method: "GET",
    params
  })
}
/** 提升模式学情报告 */
export function userAnalyseToPointApi(params: Object) {
  return request<IApiResponseData<IVersionInfo[]>>({
    url: `${SDKPORT}/userAnalyseToPoint`,
    method: "GET",
    params,
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

