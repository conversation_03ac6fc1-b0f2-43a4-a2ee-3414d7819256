import { request } from "@/utils/axios"

const ASSESSMENT_PORT = "/api/xiaoyeoo/sdkapi/assessment"

/** 获取单元测评列表 */
export function getUnitAssessmentListApi(params: any) {
  return request({
    url: `${ASSESSMENT_PORT}/unitList`,
    method: "GET",
    params
  })
}

/** 获取期中测评信息 */
export function getMidtermAssessmentApi(params: any) {
  return request({
    url: `${ASSESSMENT_PORT}/midterm`,
    method: "GET",
    params
  })
}

/** 获取期末测评信息 */
export function getFinalAssessmentApi(params: any) {
  return request({
    url: `${ASSESSMENT_PORT}/final`,
    method: "GET",
    params
  })
}

/** 开始单元测评 */
export function startUnitAssessmentApi(data: any) {
  return request({
    url: `${ASSESSMENT_PORT}/startUnit`,
    method: "POST",
    data
  })
}

/** 开始期中测评 */
export function startMidtermAssessmentApi(data: any) {
  return request({
    url: `${ASSESSMENT_PORT}/startMidterm`,
    method: "POST",
    data
  })
}

/** 开始期末测评 */
export function startFinalAssessmentApi(data: any) {
  return request({
    url: `${ASSESSMENT_PORT}/startFinal`,
    method: "POST",
    data
  })
}

/** 获取学习模式配置 */
export function getLearningModesApi(params: any) {
  return request({
    url: `${ASSESSMENT_PORT}/learningModes`,
    method: "GET",
    params
  })
}

/** 保存学习模式选择 */
export function saveLearningModeApi(data: any) {
  return request({
    url: `${ASSESSMENT_PORT}/saveLearningMode`,
    method: "POST",
    data
  })
}

/** 获取入门测评概览数据 */
export function getAssessmentOverviewApi(params: any) {
  return request({
    url: `${ASSESSMENT_PORT}/`,
    method: "GET",
    params
  })
} 