! function(n, e) {
  "object" == typeof exports && "object" == typeof module ? module.exports = e() : "function" == typeof define && define
    .amd ? define("DPlayer", [], e) : "object" == typeof exports ? exports.DPlayer = e() : n.DPlayer = e()
}(self, (() => (() => {
  var n = {
      916: (n, e, t) => {
        var a = t(140);
        n.exports = function(n) {
          "use strict";
          var e, o = "",
            r = (n = n || {}).video,
            i = n.options,
            l = a.$escape,
            s = n.tran,
            p = n.icons,
            d = n.index,
            c = a.$each;
            // console.log(n.options)
          return n.$value, n.$index, o +=
            '<div class="dplayer-mask"></div>\n<div class="dplayer-video-wrap">\n    ', e = t(568)(r), o += e,
            o += "\n    ", 
            //改 加视频标题
            o += `<div class="full-tit" style="display:none" id="fulltit">${i.video?.title || ''}</div>`,
            o += '<div class="dplayer-bezel">\n        <span class="dplayer-bezel-icon"></span>\n        ',
            o += '\n        <span class="diplayer-loading-icon">', o += p
            .loading, o +=
            '</span>\n    </div>\n</div>\n<div class="dplayer-controller-mask"></div>\n<div class="dplayer-controller">\n    <div class="dplayer-icons dplayer-comment-box">\n        <button class="dplayer-icon dplayer-comment-setting-icon" data-balloon="',
            o += l(s("setting")), o +=
            '" data-balloon-pos="up">\n            <span class="dplayer-icon-content" title="全屏">', o += p.pallette, o +=
            '</span>\n        </button>\n        <div class="dplayer-comment-setting-box">\n            <div class="dplayer-comment-setting-color">\n                <div class="dplayer-comment-setting-title">',
            o += l(s("set-danmaku-color")), o +=
            '</div>\n                <label>\n                    <input type="radio" name="dplayer-danmaku-color-',
            o += l(d), o +=
            '" value="#fff" checked>\n                    <span style="background: #fff;"></span>\n                </label>\n                <label>\n                    <input type="radio" name="dplayer-danmaku-color-',
            o += l(d), o +=
            '" value="#e54256">\n                    <span style="background: #e54256"></span>\n                </label>\n                <label>\n                    <input type="radio" name="dplayer-danmaku-color-',
            o += l(d), o +=
            '" value="#ffe133">\n                    <span style="background: #ffe133"></span>\n                </label>\n                <label>\n                    <input type="radio" name="dplayer-danmaku-color-',
            o += l(d), o +=
            '" value="#64DD17">\n                    <span style="background: #64DD17"></span>\n                </label>\n                <label>\n                    <input type="radio" name="dplayer-danmaku-color-',
            o += l(d), o +=
            '" value="#39ccff">\n                    <span style="background: #39ccff"></span>\n                </label>\n                <label>\n                    <input type="radio" name="dplayer-danmaku-color-',
            o += l(d), o +=
            '" value="#D500F9">\n                    <span style="background: #D500F9"></span>\n                </label>\n            </div>\n            <div class="dplayer-comment-setting-type">\n                <div class="dplayer-comment-setting-title">',
            o += l(s("set-danmaku-type")), o +=
            '</div>\n                <label>\n                    <input type="radio" name="dplayer-danmaku-type-',
            o += l(d), o += '" value="1">\n                    <span>', o += l(s("top")), o +=
            '</span>\n                </label>\n                <label>\n                    <input type="radio" name="dplayer-danmaku-type-',
            o += l(d), o += '" value="0" checked>\n                    <span>', o += l(s("rolling")), o +=
            '</span>\n                </label>\n                <label>\n                    <input type="radio" name="dplayer-danmaku-type-',
            o += l(d), o += '" value="2">\n                    <span>', o += l(s("bottom")), o +=
            '</span>\n                </label>\n            </div>\n        </div>\n        <input class="dplayer-comment-input" type="text" placeholder="',
            o += l(s("input-danmaku-enter")), o +=
            '" maxlength="30">\n        <button class="dplayer-icon dplayer-send-icon" data-balloon="', o +=
            l(s("send")), o += '" data-balloon-pos="up">\n            <span class="dplayer-icon-content">',
            o += p.send, o +=
            '</span>\n        </button>\n    </div>\n    <div class="dplayer-icons dplayer-icons-left">\n        <button class="dplayer-icon dplayer-play-icon">\n            <span class="dplayer-icon-content">',
            o += p.play, o +=
            '</span>\n        </button>\n        <div class="dplayer-volume">\n            <button class="dplayer-icon dplayer-volume-icon">\n                <span class="dplayer-icon-content">',
            o += p.volumeDown, o +=
            '</span>\n            </button>\n            <div class="dplayer-volume-bar-wrap" data-balloon-pos="up">\n                <div class="dplayer-volume-bar">\n                    <div class="dplayer-volume-bar-inner" style="background: ',
            o += l(i.theme), o +=
            ';">\n                        <span class="dplayer-thumb" style="background: ', o += l(i.theme),
            o +=
            '"></span>\n                    </div>\n                </div>\n            </div>\n        </div>\n        <span class="dplayer-time">\n            <span class="dplayer-ptime">0:00</span> /\n            <span class="dplayer-dtime">0:00</span>\n        </span>\n        ',
            i.live && (o +=
              '\n        <span class="dplayer-live-badge"><span class="dplayer-live-dot" style="background: ',
              o += l(i.theme), o += ';"></span>', o += l(s("live")), o += "</span>\n        "), o +=
            '\n    </div>\n    <div class="dplayer-icons dplayer-icons-right">\n        ', i.video.quality &&
            (o +=
              '\n        <div class="dplayer-quality">\n            <button class="dplayer-icon dplayer-quality-icon">',
              o += l(i.video.quality[i.video.defaultQuality].name), o +=
              '</button>\n            <div class="dplayer-quality-mask">\n                <div class="dplayer-quality-list">\n                ',
              c(i.video.quality, (function(n, e) {
                o += '\n                    <div class="dplayer-quality-item" data-index="', o += l(e),
                  o += '">', o += l(n.name), o += "</div>\n                "
              })), o += "\n                </div>\n            </div>\n        </div>\n        "), o +=
            "\n        ", i.screenshot && (o +=
              '\n        <div class="dplayer-icon dplayer-camera-icon" data-balloon="', o += l(s(
                "screenshot")), o +=
              '" data-balloon-pos="up">\n            <span class="dplayer-icon-content">', o += p.camera, o +=
              "</span>\n        </div>\n        "), o += "\n        ", o += "\n        ", i.chromecast && (o +=
              '\n        <div class="dplayer-icon dplayer-chromecast-icon" data-balloon="', o += l(s(
                "chromecast")), o +=
              '" data-balloon-pos="up">\n            <span class="dplayer-icon-content">', o += p.chromecast,
              o += "</span>\n        </div>\n        "), o +=
            '\n        <div class="dplayer-comment">\n            <button class="dplayer-icon dplayer-comment-icon" data-balloon="',
            o += l(s("send-danmaku")), o +=
            '" data-balloon-pos="up">\n                <span class="dplayer-icon-content">', o += p.comment,
            o += "</span>\n            </button>\n        </div>\n        ", i.subtitle && (o += "\n        ",
              "string" == typeof i.subtitle.url ? (o +=
                '\n        <div class="dplayer-subtitle-btn">\n            <button class="dplayer-icon dplayer-subtitle-icon" data-balloon="',
                o += l(s("hide-subs")), o +=
                '" data-balloon-pos="up">\n                <span class="dplayer-icon-content">', o += p
                .subtitle, o += "</span>\n            </button>\n        </div>\n        ") : (o +=
                '\n        <div class="dplayer-subtitles">\n            <button class="dplayer-icon dplayer-subtitles-icon" data-balloon="',
                o += l(s("subtitle")), o +=
                '" data-balloon-pos="up">\n                <span class="dplayer-icon-content">', o += p
                .subtitle, o +=
                '</span>\n            </button>\n            <div class="dplayer-subtitles-box">\n                <div class="dplayer-subtitles-panel">\n                    ',
                c(i.subtitle.url, (function(n, e) {
                  o += '\n                        <div class="dplayer-subtitles-item" data-subtitle="',
                    o += l(n.url), o +=
                    '">\n                            \x3c!-- if lang, show tran(lang). if lang and name, show name + (tran(lang)). if name, show name. off option use lang for translation. --\x3e\n                            <span class="dplayer-label">',
                    o += l(n.lang ? n.name ? n.name + " (" + s(n.lang) + ")" : s(n.lang) : n.name), o +=
                    "</span>\n                        </div>\n                    "
                })), o += "\n                </div>\n            </div>\n        </div>\n        "), o +=
              "\n        "), o +=
            // 改速度弹窗
`<div class="dplayer-setting">
  <div class="dplayer-icon dplayer-setting-icon" data-balloon="${l(s(" setting"))}" data-balloon-pos="up">倍速</div>
  <div class="dplayer-setting-box">
    <div class="dplayer-setting-speed-panel">
      <div class="dplayer-setting-speed-item" data-speed="0.5">
        <span class="dplayer-label">0.5X</span>
      </div>
      <div class="dplayer-setting-speed-item" data-speed="0.75">
        <span class="dplayer-label">0.75X</span>
      </div>
      <div class="dplayer-setting-speed-item green" data-speed="1">
        <span class="dplayer-label">1.0X</span>
      </div>
      <div class="dplayer-setting-speed-item" data-speed="1.5">
        <span class="dplayer-label">1.5X</span>
      </div>
      <div class="dplayer-setting-speed-item" data-speed="2.0">
        <span class="dplayer-label">2.0X</span>
      </div>
    </div>
  </div>
</div>
<div class="dplayer-full">
  <button class="dplayer-icon dplayer-full-icon" data-balloon="${l(s("fullscreen"))}" data-balloon-pos="up" data-title="全屏" id="fullIcon">
    <span class="dplayer-icon-content">${p.full}</span>
  </button>
</div>
</div>
<div class="dplayer-bar-wrap">
  <div class="dplayer-bar-time hidden">00:00</div>
  <div class="dplayer-bar-preview"></div>
  <div class="dplayer-bar">
    <div class="dplayer-loaded" style="width: 0;"></div>
    <div class="dplayer-played" style="width: 0; background: ${l(i.theme)}">
      <span class="dplayer-thumb" style="background: ${l(i.theme)}"></span>
    </div>
  </div>
</div>
</div>
<div class="dplayer-info-panel dplayer-info-panel-hide">
  <div class="dplayer-info-panel-close">[x]</div>
  <div class="dplayer-info-panel-item dplayer-info-panel-item-version">
    <span class="dplayer-info-panel-item-title">Player version</span>
    <span class="dplayer-info-panel-item-data"></span>
  </div>
  <div class="dplayer-info-panel-item dplayer-info-panel-item-fps">
    <span class="dplayer-info-panel-item-title">Player FPS</span>
    <span class="dplayer-info-panel-item-data"></span>
  </div>
  <div class="dplayer-info-panel-item dplayer-info-panel-item-type">
    <span class="dplayer-info-panel-item-title">Video type</span>
    <span class="dplayer-info-panel-item-data"></span>
  </div>
  <div class="dplayer-info-panel-item dplayer-info-panel-item-url">
    <span class="dplayer-info-panel-item-title">Video url</span>
    <span class="dplayer-info-panel-item-data"></span>
  </div>
  <div class="dplayer-info-panel-item dplayer-info-panel-item-resolution">
    <span class="dplayer-info-panel-item-title">Video resolution</span>
    <span class="dplayer-info-panel-item-data"></span>
  </div>
  <div class="dplayer-info-panel-item dplayer-info-panel-item-duration">
    <span class="dplayer-info-panel-item-title">Video duration</span>
    <span class="dplayer-info-panel-item-data"></span>
  </div>
</div>
<div class="dplayer-notice-list"></div>
<button class="dplayer-mobile-play">${p.play}</button>
`
        }
      },
      568: (n, e, t) => {
        var a = t(140);
        n.exports = function(n) {
          "use strict";
          var e = "",
            t = (n = n || {}).enableSubtitle,
            o = n.subtitle,
            r = n.current,
            i = n.airplay,
            l = n.pic,
            s = a.$escape,
            p = n.screenshot,
            d = n.preload,
            c = n.url;
            //改
          return t = o && "webvtt" === o.type, e += '\n<video\n id="video" preload muted autoplay class="dplayer-video ', r && (e +=
            "dplayer-video-current"), e += '"\n    webkit-playsinline\n    ', i && (e +=
            ' x-webkit-airplay="allow" '), e += "\n    playsinline\n    ", l && (e += 'poster="', e += s(l),
            e += '"'), e += "\n    ", (p || t) && (e += 'crossorigin="anonymous"'), e += "\n    ", d && (
            e += 'preload="', e += s(d), e += '"'), e += "\n    ", i ? e += "\n    nosrc\n    " : c && (e +=
            '\n    src="', e += s(c), e += '"\n    '), e += "\n    >\n    ", i && (e +=
            '\n    <source src="', e += s(c), e += '">\n    '), e += "\n    ", t && (e +=
            '\n    <track class="dplayer-subtrack" kind="metadata" default src="', e += s("string" ==
              typeof o.url ? o.url : o.url[o.index].url), e += '"></track>\n    '), e + "\n</video>"
        }
      },
      234: (n, e, t) => {
        "use strict";
        t.d(e, {
          Z: () => l
        });
        var a = t(189),
          o = t.n(a),
          r = t(291),
          i = t.n(r)()(o());
        i.push([n.id,
          ':root {\n  --balloon-border-radius: 2px;\n  --balloon-color: rgba(16, 16, 16, 0.95);\n  --balloon-text-color: #fff;\n  --balloon-font-size: 12px;\n  --balloon-move: 4px; }\n\nbutton[aria-label][data-balloon-pos] {\n  overflow: visible; }\n\n[aria-label][data-balloon-pos] {\n  position: relative;\n  cursor: pointer; }\n  [aria-label][data-balloon-pos]:after {\n    opacity: 0;\n    pointer-events: none;\n    transition: all 0.18s ease-out 0.18s;\n    text-indent: 0;\n    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;\n    font-weight: normal;\n    font-style: normal;\n    text-shadow: none;\n    font-size: 12px;\n    font-size: var(--balloon-font-size);\n    background: rgba(16, 16, 16, 0.95);\n    background: var(--balloon-color);\n    border-radius: 2px;\n    color: #fff;\n    color: var(--balloon-text-color);\n    border-radius: var(--balloon-border-radius);\n    content: attr(aria-label);\n    padding: .5em 1em;\n    position: absolute;\n    white-space: nowrap;\n    z-index: 10; }\n  [aria-label][data-balloon-pos]:before {\n    width: 0;\n    height: 0;\n    border: 5px solid transparent;\n    border-top-color: rgba(16, 16, 16, 0.95);\n    border-top-color: var(--balloon-color);\n    opacity: 0;\n    pointer-events: none;\n    transition: all 0.18s ease-out 0.18s;\n    content: "";\n    position: absolute;\n    z-index: 10; }\n  [aria-label][data-balloon-pos]:hover:before, [aria-label][data-balloon-pos]:hover:after, [aria-label][data-balloon-pos][data-balloon-visible]:before, [aria-label][data-balloon-pos][data-balloon-visible]:after, [aria-label][data-balloon-pos]:not([data-balloon-nofocus]):focus:before, [aria-label][data-balloon-pos]:not([data-balloon-nofocus]):focus:after {\n    opacity: 1;\n    pointer-events: none; }\n  [aria-label][data-balloon-pos].font-awesome:after {\n    font-family: FontAwesome, -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, Oxygen, Ubuntu, Cantarell, \'Open Sans\', \'Helvetica Neue\', sans-serif; }\n  [aria-label][data-balloon-pos][data-balloon-break]:after {\n    white-space: pre; }\n  [aria-label][data-balloon-pos][data-balloon-break][data-balloon-length]:after {\n    white-space: pre-line;\n    word-break: break-word; }\n  [aria-label][data-balloon-pos][data-balloon-blunt]:before, [aria-label][data-balloon-pos][data-balloon-blunt]:after {\n    transition: none; }\n  [aria-label][data-balloon-pos][data-balloon-pos="up"]:hover:after, [aria-label][data-balloon-pos][data-balloon-pos="up"][data-balloon-visible]:after, [aria-label][data-balloon-pos][data-balloon-pos="down"]:hover:after, [aria-label][data-balloon-pos][data-balloon-pos="down"][data-balloon-visible]:after {\n    transform: translate(-50%, 0); }\n  [aria-label][data-balloon-pos][data-balloon-pos="up"]:hover:before, [aria-label][data-balloon-pos][data-balloon-pos="up"][data-balloon-visible]:before, [aria-label][data-balloon-pos][data-balloon-pos="down"]:hover:before, [aria-label][data-balloon-pos][data-balloon-pos="down"][data-balloon-visible]:before {\n    transform: translate(-50%, 0); }\n  [aria-label][data-balloon-pos][data-balloon-pos*="-left"]:after {\n    left: 0; }\n  [aria-label][data-balloon-pos][data-balloon-pos*="-left"]:before {\n    left: 5px; }\n  [aria-label][data-balloon-pos][data-balloon-pos*="-right"]:after {\n    right: 0; }\n  [aria-label][data-balloon-pos][data-balloon-pos*="-right"]:before {\n    right: 5px; }\n  [aria-label][data-balloon-pos][data-balloon-po*="-left"]:hover:after, [aria-label][data-balloon-pos][data-balloon-po*="-left"][data-balloon-visible]:after, [aria-label][data-balloon-pos][data-balloon-pos*="-right"]:hover:after, [aria-label][data-balloon-pos][data-balloon-pos*="-right"][data-balloon-visible]:after {\n    transform: translate(0, 0); }\n  [aria-label][data-balloon-pos][data-balloon-po*="-left"]:hover:before, [aria-label][data-balloon-pos][data-balloon-po*="-left"][data-balloon-visible]:before, [aria-label][data-balloon-pos][data-balloon-pos*="-right"]:hover:before, [aria-label][data-balloon-pos][data-balloon-pos*="-right"][data-balloon-visible]:before {\n    transform: translate(0, 0); }\n  [aria-label][data-balloon-pos][data-balloon-pos^="up"]:before, [aria-label][data-balloon-pos][data-balloon-pos^="up"]:after {\n    bottom: 100%;\n    transform-origin: top;\n    transform: translate(0, 4px);\n    transform: translate(0, var(--balloon-move)); }\n  [aria-label][data-balloon-pos][data-balloon-pos^="up"]:after {\n    margin-bottom: 10px; }\n  [aria-label][data-balloon-pos][data-balloon-pos="up"]:before, [aria-label][data-balloon-pos][data-balloon-pos="up"]:after {\n    left: 50%;\n    transform: translate(-50%, 4px);\n    transform: translate(-50%, var(--balloon-move)); }\n  [aria-label][data-balloon-pos][data-balloon-pos^="down"]:before, [aria-label][data-balloon-pos][data-balloon-pos^="down"]:after {\n    top: 100%;\n    transform: translate(0, calc(4px * -1));\n    transform: translate(0, calc(var(--balloon-move) * -1)); }\n  [aria-label][data-balloon-pos][data-balloon-pos^="down"]:after {\n    margin-top: 10px; }\n  [aria-label][data-balloon-pos][data-balloon-pos^="down"]:before {\n    width: 0;\n    height: 0;\n    border: 5px solid transparent;\n    border-bottom-color: rgba(16, 16, 16, 0.95);\n    border-bottom-color: var(--balloon-color); }\n  [aria-label][data-balloon-pos][data-balloon-pos="down"]:after, [aria-label][data-balloon-pos][data-balloon-pos="down"]:before {\n    left: 50%;\n    transform: translate(-50%, calc(4px * -1));\n    transform: translate(-50%, calc(var(--balloon-move) * -1)); }\n  [aria-label][data-balloon-pos][data-balloon-pos="left"]:hover:after, [aria-label][data-balloon-pos][data-balloon-pos="left"][data-balloon-visible]:after, [aria-label][data-balloon-pos][data-balloon-pos="right"]:hover:after, [aria-label][data-balloon-pos][data-balloon-pos="right"][data-balloon-visible]:after {\n    transform: translate(0, -50%); }\n  [aria-label][data-balloon-pos][data-balloon-pos="left"]:hover:before, [aria-label][data-balloon-pos][data-balloon-pos="left"][data-balloon-visible]:before, [aria-label][data-balloon-pos][data-balloon-pos="right"]:hover:before, [aria-label][data-balloon-pos][data-balloon-pos="right"][data-balloon-visible]:before {\n    transform: translate(0, -50%); }\n  [aria-label][data-balloon-pos][data-balloon-pos="left"]:after, [aria-label][data-balloon-pos][data-balloon-pos="left"]:before {\n    right: 100%;\n    top: 50%;\n    transform: translate(4px, -50%);\n    transform: translate(var(--balloon-move), -50%); }\n  [aria-label][data-balloon-pos][data-balloon-pos="left"]:after {\n    margin-right: 10px; }\n  [aria-label][data-balloon-pos][data-balloon-pos="left"]:before {\n    width: 0;\n    height: 0;\n    border: 5px solid transparent;\n    border-left-color: rgba(16, 16, 16, 0.95);\n    border-left-color: var(--balloon-color); }\n  [aria-label][data-balloon-pos][data-balloon-pos="right"]:after, [aria-label][data-balloon-pos][data-balloon-pos="right"]:before {\n    left: 100%;\n    top: 50%;\n    transform: translate(calc(4px * -1), -50%);\n    transform: translate(calc(var(--balloon-move) * -1), -50%); }\n  [aria-label][data-balloon-pos][data-balloon-pos="right"]:after {\n    margin-left: 10px; }\n  [aria-label][data-balloon-pos][data-balloon-pos="right"]:before {\n    width: 0;\n    height: 0;\n    border: 5px solid transparent;\n    border-right-color: rgba(16, 16, 16, 0.95);\n    border-right-color: var(--balloon-color); }\n  [aria-label][data-balloon-pos][data-balloon-length]:after {\n    white-space: normal; }\n  [aria-label][data-balloon-pos][data-balloon-length="small"]:after {\n    width: 80px; }\n  [aria-label][data-balloon-pos][data-balloon-length="medium"]:after {\n    width: 150px; }\n  [aria-label][data-balloon-pos][data-balloon-length="large"]:after {\n    width: 260px; }\n  [aria-label][data-balloon-pos][data-balloon-length="xlarge"]:after {\n    width: 380px; }\n    @media screen and (max-width: 768px) {\n      [aria-label][data-balloon-pos][data-balloon-length="xlarge"]:after {\n        width: 90vw; } }\n  [aria-label][data-balloon-pos][data-balloon-length="fit"]:after {\n    width: 100%; }\n',
          "", {
            version: 3,
            sources: [
              "webpack://./node_modules/.pnpm/balloon-css@1.2.0/node_modules/balloon-css/balloon.css"
            ],
            names: [],
            mappings: "AAAA;EACE,4BAA4B;EAC5B,uCAAuC;EACvC,0BAA0B;EAC1B,yBAAyB;EACzB,mBAAmB,EAAE;;AAEvB;EACE,iBAAiB,EAAE;;AAErB;EACE,kBAAkB;EAClB,eAAe,EAAE;EACjB;IACE,UAAU;IACV,oBAAoB;IACpB,oCAAoC;IACpC,cAAc;IACd,wIAAwI;IACxI,mBAAmB;IACnB,kBAAkB;IAClB,iBAAiB;IACjB,eAAmC;IAAnC,mCAAmC;IACnC,kCAAgC;IAAhC,gCAAgC;IAChC,kBAAkB;IAClB,WAAgC;IAAhC,gCAAgC;IAChC,2CAA2C;IAC3C,yBAAyB;IACzB,iBAAiB;IACjB,kBAAkB;IAClB,mBAAmB;IACnB,WAAW,EAAE;EACf;IACE,QAAQ;IACR,SAAS;IACT,6BAA6B;IAC7B,wCAAsC;IAAtC,sCAAsC;IACtC,UAAU;IACV,oBAAoB;IACpB,oCAAoC;IACpC,WAAW;IACX,kBAAkB;IAClB,WAAW,EAAE;EACf;IACE,UAAU;IACV,oBAAoB,EAAE;EACxB;IACE,qJAAqJ,EAAE;EACzJ;IACE,gBAAgB,EAAE;EACpB;IACE,qBAAqB;IACrB,sBAAsB,EAAE;EAC1B;IACE,gBAAgB,EAAE;EACpB;IACE,6BAA6B,EAAE;EACjC;IACE,6BAA6B,EAAE;EACjC;IACE,OAAO,EAAE;EACX;IACE,SAAS,EAAE;EACb;IACE,QAAQ,EAAE;EACZ;IACE,UAAU,EAAE;EACd;IACE,0BAA0B,EAAE;EAC9B;IACE,0BAA0B,EAAE;EAC9B;IACE,YAAY;IACZ,qBAAqB;IACrB,4BAA4C;IAA5C,4CAA4C,EAAE;EAChD;IACE,mBAAmB,EAAE;EACvB;IACE,SAAS;IACT,+BAA+C;IAA/C,+CAA+C,EAAE;EACnD;IACE,SAAS;IACT,uCAAuD;IAAvD,uDAAuD,EAAE;EAC3D;IACE,gBAAgB,EAAE;EACpB;IACE,QAAQ;IACR,SAAS;IACT,6BAA6B;IAC7B,2CAAyC;IAAzC,yCAAyC,EAAE;EAC7C;IACE,SAAS;IACT,0CAA0D;IAA1D,0DAA0D,EAAE;EAC9D;IACE,6BAA6B,EAAE;EACjC;IACE,6BAA6B,EAAE;EACjC;IACE,WAAW;IACX,QAAQ;IACR,+BAA+C;IAA/C,+CAA+C,EAAE;EACnD;IACE,kBAAkB,EAAE;EACtB;IACE,QAAQ;IACR,SAAS;IACT,6BAA6B;IAC7B,yCAAuC;IAAvC,uCAAuC,EAAE;EAC3C;IACE,UAAU;IACV,QAAQ;IACR,0CAA0D;IAA1D,0DAA0D,EAAE;EAC9D;IACE,iBAAiB,EAAE;EACrB;IACE,QAAQ;IACR,SAAS;IACT,6BAA6B;IAC7B,0CAAwC;IAAxC,wCAAwC,EAAE;EAC5C;IACE,mBAAmB,EAAE;EACvB;IACE,WAAW,EAAE;EACf;IACE,YAAY,EAAE;EAChB;IACE,YAAY,EAAE;EAChB;IACE,YAAY,EAAE;IACd;MACE;QACE,WAAW,EAAE,EAAE;EACrB;IACE,WAAW,EAAE",
            sourcesContent: [
              ':root {\n  --balloon-border-radius: 2px;\n  --balloon-color: rgba(16, 16, 16, 0.95);\n  --balloon-text-color: #fff;\n  --balloon-font-size: 12px;\n  --balloon-move: 4px; }\n\nbutton[aria-label][data-balloon-pos] {\n  overflow: visible; }\n\n[aria-label][data-balloon-pos] {\n  position: relative;\n  cursor: pointer; }\n  [aria-label][data-balloon-pos]:after {\n    opacity: 0;\n    pointer-events: none;\n    transition: all 0.18s ease-out 0.18s;\n    text-indent: 0;\n    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;\n    font-weight: normal;\n    font-style: normal;\n    text-shadow: none;\n    font-size: var(--balloon-font-size);\n    background: var(--balloon-color);\n    border-radius: 2px;\n    color: var(--balloon-text-color);\n    border-radius: var(--balloon-border-radius);\n    content: attr(aria-label);\n    padding: .5em 1em;\n    position: absolute;\n    white-space: nowrap;\n    z-index: 10; }\n  [aria-label][data-balloon-pos]:before {\n    width: 0;\n    height: 0;\n    border: 5px solid transparent;\n    border-top-color: var(--balloon-color);\n    opacity: 0;\n    pointer-events: none;\n    transition: all 0.18s ease-out 0.18s;\n    content: "";\n    position: absolute;\n    z-index: 10; }\n  [aria-label][data-balloon-pos]:hover:before, [aria-label][data-balloon-pos]:hover:after, [aria-label][data-balloon-pos][data-balloon-visible]:before, [aria-label][data-balloon-pos][data-balloon-visible]:after, [aria-label][data-balloon-pos]:not([data-balloon-nofocus]):focus:before, [aria-label][data-balloon-pos]:not([data-balloon-nofocus]):focus:after {\n    opacity: 1;\n    pointer-events: none; }\n  [aria-label][data-balloon-pos].font-awesome:after {\n    font-family: FontAwesome, -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, Oxygen, Ubuntu, Cantarell, \'Open Sans\', \'Helvetica Neue\', sans-serif; }\n  [aria-label][data-balloon-pos][data-balloon-break]:after {\n    white-space: pre; }\n  [aria-label][data-balloon-pos][data-balloon-break][data-balloon-length]:after {\n    white-space: pre-line;\n    word-break: break-word; }\n  [aria-label][data-balloon-pos][data-balloon-blunt]:before, [aria-label][data-balloon-pos][data-balloon-blunt]:after {\n    transition: none; }\n  [aria-label][data-balloon-pos][data-balloon-pos="up"]:hover:after, [aria-label][data-balloon-pos][data-balloon-pos="up"][data-balloon-visible]:after, [aria-label][data-balloon-pos][data-balloon-pos="down"]:hover:after, [aria-label][data-balloon-pos][data-balloon-pos="down"][data-balloon-visible]:after {\n    transform: translate(-50%, 0); }\n  [aria-label][data-balloon-pos][data-balloon-pos="up"]:hover:before, [aria-label][data-balloon-pos][data-balloon-pos="up"][data-balloon-visible]:before, [aria-label][data-balloon-pos][data-balloon-pos="down"]:hover:before, [aria-label][data-balloon-pos][data-balloon-pos="down"][data-balloon-visible]:before {\n    transform: translate(-50%, 0); }\n  [aria-label][data-balloon-pos][data-balloon-pos*="-left"]:after {\n    left: 0; }\n  [aria-label][data-balloon-pos][data-balloon-pos*="-left"]:before {\n    left: 5px; }\n  [aria-label][data-balloon-pos][data-balloon-pos*="-right"]:after {\n    right: 0; }\n  [aria-label][data-balloon-pos][data-balloon-pos*="-right"]:before {\n    right: 5px; }\n  [aria-label][data-balloon-pos][data-balloon-po*="-left"]:hover:after, [aria-label][data-balloon-pos][data-balloon-po*="-left"][data-balloon-visible]:after, [aria-label][data-balloon-pos][data-balloon-pos*="-right"]:hover:after, [aria-label][data-balloon-pos][data-balloon-pos*="-right"][data-balloon-visible]:after {\n    transform: translate(0, 0); }\n  [aria-label][data-balloon-pos][data-balloon-po*="-left"]:hover:before, [aria-label][data-balloon-pos][data-balloon-po*="-left"][data-balloon-visible]:before, [aria-label][data-balloon-pos][data-balloon-pos*="-right"]:hover:before, [aria-label][data-balloon-pos][data-balloon-pos*="-right"][data-balloon-visible]:before {\n    transform: translate(0, 0); }\n  [aria-label][data-balloon-pos][data-balloon-pos^="up"]:before, [aria-label][data-balloon-pos][data-balloon-pos^="up"]:after {\n    bottom: 100%;\n    transform-origin: top;\n    transform: translate(0, var(--balloon-move)); }\n  [aria-label][data-balloon-pos][data-balloon-pos^="up"]:after {\n    margin-bottom: 10px; }\n  [aria-label][data-balloon-pos][data-balloon-pos="up"]:before, [aria-label][data-balloon-pos][data-balloon-pos="up"]:after {\n    left: 50%;\n    transform: translate(-50%, var(--balloon-move)); }\n  [aria-label][data-balloon-pos][data-balloon-pos^="down"]:before, [aria-label][data-balloon-pos][data-balloon-pos^="down"]:after {\n    top: 100%;\n    transform: translate(0, calc(var(--balloon-move) * -1)); }\n  [aria-label][data-balloon-pos][data-balloon-pos^="down"]:after {\n    margin-top: 10px; }\n  [aria-label][data-balloon-pos][data-balloon-pos^="down"]:before {\n    width: 0;\n    height: 0;\n    border: 5px solid transparent;\n    border-bottom-color: var(--balloon-color); }\n  [aria-label][data-balloon-pos][data-balloon-pos="down"]:after, [aria-label][data-balloon-pos][data-balloon-pos="down"]:before {\n    left: 50%;\n    transform: translate(-50%, calc(var(--balloon-move) * -1)); }\n  [aria-label][data-balloon-pos][data-balloon-pos="left"]:hover:after, [aria-label][data-balloon-pos][data-balloon-pos="left"][data-balloon-visible]:after, [aria-label][data-balloon-pos][data-balloon-pos="right"]:hover:after, [aria-label][data-balloon-pos][data-balloon-pos="right"][data-balloon-visible]:after {\n    transform: translate(0, -50%); }\n  [aria-label][data-balloon-pos][data-balloon-pos="left"]:hover:before, [aria-label][data-balloon-pos][data-balloon-pos="left"][data-balloon-visible]:before, [aria-label][data-balloon-pos][data-balloon-pos="right"]:hover:before, [aria-label][data-balloon-pos][data-balloon-pos="right"][data-balloon-visible]:before {\n    transform: translate(0, -50%); }\n  [aria-label][data-balloon-pos][data-balloon-pos="left"]:after, [aria-label][data-balloon-pos][data-balloon-pos="left"]:before {\n    right: 100%;\n    top: 50%;\n    transform: translate(var(--balloon-move), -50%); }\n  [aria-label][data-balloon-pos][data-balloon-pos="left"]:after {\n    margin-right: 10px; }\n  [aria-label][data-balloon-pos][data-balloon-pos="left"]:before {\n    width: 0;\n    height: 0;\n    border: 5px solid transparent;\n    border-left-color: var(--balloon-color); }\n  [aria-label][data-balloon-pos][data-balloon-pos="right"]:after, [aria-label][data-balloon-pos][data-balloon-pos="right"]:before {\n    left: 100%;\n    top: 50%;\n    transform: translate(calc(var(--balloon-move) * -1), -50%); }\n  [aria-label][data-balloon-pos][data-balloon-pos="right"]:after {\n    margin-left: 10px; }\n  [aria-label][data-balloon-pos][data-balloon-pos="right"]:before {\n    width: 0;\n    height: 0;\n    border: 5px solid transparent;\n    border-right-color: var(--balloon-color); }\n  [aria-label][data-balloon-pos][data-balloon-length]:after {\n    white-space: normal; }\n  [aria-label][data-balloon-pos][data-balloon-length="small"]:after {\n    width: 80px; }\n  [aria-label][data-balloon-pos][data-balloon-length="medium"]:after {\n    width: 150px; }\n  [aria-label][data-balloon-pos][data-balloon-length="large"]:after {\n    width: 260px; }\n  [aria-label][data-balloon-pos][data-balloon-length="xlarge"]:after {\n    width: 380px; }\n    @media screen and (max-width: 768px) {\n      [aria-label][data-balloon-pos][data-balloon-length="xlarge"]:after {\n        width: 90vw; } }\n  [aria-label][data-balloon-pos][data-balloon-length="fit"]:after {\n    width: 100%; }\n'
            ],
            sourceRoot: ""
          }
        ]);
        const l = i
      },
      336: (n, e, t) => {
        "use strict";
        t.d(e, {
          Z: () => u
        });
        var a = t(189),
          o = t.n(a),
          r = t(291),
          i = t.n(r),
          l = t(234),
          s = t(943),
          p = t.n(s),
          d = new URL(t(831), t.b),
          c = i()(o());
        c.i(l.Z);
        var A = p()(d);
        c.push([n.id,
          `@keyframes my-face {\n  2% {\n    transform: translate(0, 1.5px) rotate(1.5deg);\n  }\n  4% {\n    transform: translate(0, -1.5px) rotate(-0.5deg);\n  }\n  6% {\n    transform: translate(0, 1.5px) rotate(-1.5deg);\n  }\n  8% {\n    transform: translate(0, -1.5px) rotate(-1.5deg);\n  }\n  10% {\n    transform: translate(0, 2.5px) rotate(1.5deg);\n  }\n  12% {\n    transform: translate(0, -0.5px) rotate(1.5deg);\n  }\n  14% {\n    transform: translate(0, -1.5px) rotate(1.5deg);\n  }\n  16% {\n    transform: translate(0, -0.5px) rotate(-1.5deg);\n  }\n  18% {\n    transform: translate(0, 0.5px) rotate(-1.5deg);\n  }\n  20% {\n    transform: translate(0, -1.5px) rotate(2.5deg);\n  }\n  22% {\n    transform: translate(0, 0.5px) rotate(-1.5deg);\n  }\n  24% {\n    transform: translate(0, 1.5px) rotate(1.5deg);\n  }\n  26% {\n    transform: translate(0, 0.5px) rotate(0.5deg);\n  }\n  28% {\n    transform: translate(0, 0.5px) rotate(1.5deg);\n  }\n  30% {\n    transform: translate(0, -0.5px) rotate(2.5deg);\n  }\n  32% {\n    transform: translate(0, 1.5px) rotate(-0.5deg);\n  }\n  34% {\n    transform: translate(0, 1.5px) rotate(-0.5deg);\n  }\n  36% {\n    transform: translate(0, -1.5px) rotate(2.5deg);\n  }\n  38% {\n    transform: translate(0, 1.5px) rotate(-1.5deg);\n  }\n  40% {\n    transform: translate(0, -0.5px) rotate(2.5deg);\n  }\n  42% {\n    transform: translate(0, 2.5px) rotate(-1.5deg);\n  }\n  44% {\n    transform: translate(0, 1.5px) rotate(0.5deg);\n  }\n  46% {\n    transform: translate(0, -1.5px) rotate(2.5deg);\n  }\n  48% {\n    transform: translate(0, -0.5px) rotate(0.5deg);\n  }\n  50% {\n    transform: translate(0, 0.5px) rotate(0.5deg);\n  }\n  52% {\n    transform: translate(0, 2.5px) rotate(2.5deg);\n  }\n  54% {\n    transform: translate(0, -1.5px) rotate(1.5deg);\n  }\n  56% {\n    transform: translate(0, 2.5px) rotate(2.5deg);\n  }\n  58% {\n    transform: translate(0, 0.5px) rotate(2.5deg);\n  }\n  60% {\n    transform: translate(0, 2.5px) rotate(2.5deg);\n  }\n  62% {\n    transform: translate(0, -0.5px) rotate(2.5deg);\n  }\n  64% {\n    transform: translate(0, -0.5px) rotate(1.5deg);\n  }\n  66% {\n    transform: translate(0, 1.5px) rotate(-0.5deg);\n  }\n  68% {\n    transform: translate(0, -1.5px) rotate(-0.5deg);\n  }\n  70% {\n    transform: translate(0, 1.5px) rotate(0.5deg);\n  }\n  72% {\n    transform: translate(0, 2.5px) rotate(1.5deg);\n  }\n  74% {\n    transform: translate(0, -0.5px) rotate(0.5deg);\n  }\n  76% {\n    transform: translate(0, -0.5px) rotate(2.5deg);\n  }\n  78% {\n    transform: translate(0, -0.5px) rotate(1.5deg);\n  }\n  80% {\n    transform: translate(0, 1.5px) rotate(1.5deg);\n  }\n  82% {\n    transform: translate(0, -0.5px) rotate(0.5deg);\n  }\n  84% {\n    transform: translate(0, 1.5px) rotate(2.5deg);\n  }\n  86% {\n    transform: translate(0, -1.5px) rotate(-1.5deg);\n  }\n  88% {\n    transform: translate(0, -0.5px) rotate(2.5deg);\n  }\n  90% {\n    transform: translate(0, 2.5px) rotate(-0.5deg);\n  }\n  92% {\n    transform: translate(0, 0.5px) rotate(-0.5deg);\n  }\n  94% {\n    transform: translate(0, 2.5px) rotate(0.5deg);\n  }\n  96% {\n    transform: translate(0, -0.5px) rotate(1.5deg);\n  }\n  98% {\n    transform: translate(0, -1.5px) rotate(-0.5deg);\n  }\n  0%,\n  100% {\n    transform: translate(0, 0) rotate(0deg);\n  }\n}\n.dplayer {\n  position: relative;\n  overflow: hidden;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n  line-height: 1;\n}\n.dplayer * {\n  box-sizing: content-box;\n}\n.dplayer svg {\n  width: 100%;\n  height: 100%;\n}\n.dplayer svg path,\n.dplayer svg circle {\n  fill: #fff;\n}\n.dplayer:-webkit-full-screen {\n  width: 100%;\n  height: 100%;\n  background: #000;\n  position: fixed;\n  z-index: 100000;\n  left: 0;\n  top: 0;\n  margin: 0;\n  padding: 0;\n  transform: translate(0, 0);\n}\n.dplayer.dplayer-no-danmaku .dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-box .dplayer-setting-showdan,\n.dplayer.dplayer-no-danmaku .dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-box .dplayer-setting-danmaku,\n.dplayer.dplayer-no-danmaku .dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-box .dplayer-setting-danunlimit {\n  display: none;\n}\n.dplayer.dplayer-no-danmaku .dplayer-controller .dplayer-icons .dplayer-comment {\n  display: none;\n}\n.dplayer.dplayer-no-danmaku .dplayer-danmaku {\n  display: none;\n}\n.dplayer.dplayer-live .dplayer-time {\n  display: none;\n}\n.dplayer.dplayer-live .dplayer-bar-wrap {\n  display: none;\n}\n.dplayer.dplayer-live .dplayer-setting-speed {\n  display: none;\n}\n.dplayer.dplayer-live .dplayer-setting-loop {\n  display: none;\n}\n.dplayer.dplayer-live.dplayer-no-danmaku .dplayer-setting {\n  display: none;\n}\n.dplayer.dplayer-arrow .dplayer-danmaku {\n  font-size: 18px;\n}\n.dplayer.dplayer-arrow .dplayer-icon {\n  margin: 0 -3px;\n}\n.dplayer.dplayer-playing .dplayer-danmaku .dplayer-danmaku-move {\n  animation-play-state: running;\n}\n@media (min-width: 900px) {\n  .dplayer.dplayer-playing .dplayer-controller-mask {\n    opacity: 0;\n  }\n  .dplayer.dplayer-playing .dplayer-controller {\n    opacity: 0;\n  }\n  .dplayer.dplayer-playing:hover .dplayer-controller-mask {\n    opacity: 1;\n  }\n  .dplayer.dplayer-playing:hover .dplayer-controller {\n    opacity: 1;\n  }\n}\n.dplayer.dplayer-loading .dplayer-bezel .diplayer-loading-icon {\n  display: block;\n}\n.dplayer.dplayer-loading .dplayer-danmaku,\n.dplayer.dplayer-paused .dplayer-danmaku,\n.dplayer.dplayer-loading .dplayer-danmaku-move,\n.dplayer.dplayer-paused .dplayer-danmaku-move {\n  animation-play-state: paused;\n}\n.dplayer.dplayer-hide-controller {\n  cursor: none;\n}\n.dplayer.dplayer-hide-controller .dplayer-controller-mask {\n  opacity: 0;\n  transform: translateY(100%);\n}\n.dplayer.dplayer-hide-controller .dplayer-controller {\n  opacity: 0;\n  transform: translateY(100%);\n}\n.dplayer.dplayer-show-controller .dplayer-controller-mask {\n  opacity: 1;\n}\n.dplayer.dplayer-show-controller .dplayer-controller {\n  opacity: 1;\n}\n.dplayer.dplayer-fulled {\n  width: 100% !important;\n  height: 100% !important;\n}\n.dplayer.dplayer-fulled {\n  position: fixed;\n  z-index: 100000;\n  left: 0;\n  top: 0;\n}\n.dplayer.dplayer-mobile .dplayer-controller .dplayer-icons .dplayer-volume,\n.dplayer.dplayer-mobile .dplayer-controller .dplayer-icons .dplayer-camera-icon,\n.dplayer.dplayer-mobile .dplayer-controller .dplayer-icons .dplayer-airplay-icon,\n.dplayer.dplayer-mobile .dplayer-controller .dplayer-icons .dplayer-chromecast-icon,\n.dplayer.dplayer-mobile .dplayer-controller .dplayer-icons .dplayer-play-icon {\n  display: none;\n}\n.dplayer.dplayer-mobile .dplayer-controller .dplayer-icons .dplayer-full .dplayer-full-in-icon {\n  position: static;\n  display: inline-block;\n}\n.dplayer.dplayer-mobile .dplayer-bar-time {\n  display: none;\n}\n.dplayer.dplayer-mobile.dplayer-hide-controller .dplayer-mobile-play {\n  display: none;\n}\n.dplayer.dplayer-mobile .dplayer-mobile-play {\n  display: block;\n}\n.dplayer-web-fullscreen-fix {\n  position: fixed;\n  top: 0;\n  left: 0;\n  margin: 0;\n  padding: 0;\n}\n[data-balloon]:before {\n  display: none;\n}\n[data-balloon]:after {\n  padding: 0.3em 0.7em;\n  background: rgba(17, 17, 17, 0.7);\n}\n[data-balloon][data-balloon-pos="up"]:after {\n  margin-bottom: 0;\n}\n.dplayer-bezel {\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: 0;\n  bottom: 0;\n  font-size: 22px;\n  color: #fff;\n  pointer-events: none;\n}\n.dplayer-bezel .dplayer-bezel-icon {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  margin: -26px 0 0 -26px;\n  height: 52px;\n  width: 52px;\n  padding: 12px;\n  box-sizing: border-box;\n  background: rgba(0, 0, 0, 0.5);\n  border-radius: 50%;\n  opacity: 0;\n  pointer-events: none;\n}\n.dplayer-bezel .dplayer-bezel-icon.dplayer-bezel-transition {\n  animation: bezel-hide 0.5s linear;\n}\n@keyframes bezel-hide {\n  from {\n    opacity: 1;\n    transform: scale(1);\n  }\n  to {\n    opacity: 0;\n    transform: scale(2);\n  }\n}\n.dplayer-bezel .dplayer-danloading {\n  position: absolute;\n  top: 50%;\n  margin-top: -7px;\n  width: 100%;\n  text-align: center;\n  font-size: 14px;\n  line-height: 14px;\n  animation: my-face 5s infinite ease-in-out;\n}\n.dplayer-bezel .diplayer-loading-icon {\n  display: none;\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  margin: -18px 0 0 -18px;\n  height: 36px;\n  width: 36px;\n  pointer-events: none;\n}\n.dplayer-bezel .diplayer-loading-icon .diplayer-loading-hide {\n  display: none;\n}\n.dplayer-bezel .diplayer-loading-icon .diplayer-loading-dot {\n  animation: diplayer-loading-dot-fade 0.8s ease infinite;\n  opacity: 0;\n  transform-origin: 4px 4px;\n}\n.dplayer-bezel .diplayer-loading-icon .diplayer-loading-dot.diplayer-loading-dot-1 {\n  animation-delay: 0.1s;\n}\n.dplayer-bezel .diplayer-loading-icon .diplayer-loading-dot.diplayer-loading-dot-2 {\n  animation-delay: 0.2s;\n}\n.dplayer-bezel .diplayer-loading-icon .diplayer-loading-dot.diplayer-loading-dot-3 {\n  animation-delay: 0.3s;\n}\n.dplayer-bezel .diplayer-loading-icon .diplayer-loading-dot.diplayer-loading-dot-4 {\n  animation-delay: 0.4s;\n}\n.dplayer-bezel .diplayer-loading-icon .diplayer-loading-dot.diplayer-loading-dot-5 {\n  animation-delay: 0.5s;\n}\n.dplayer-bezel .diplayer-loading-icon .diplayer-loading-dot.diplayer-loading-dot-6 {\n  animation-delay: 0.6s;\n}\n.dplayer-bezel .diplayer-loading-icon .diplayer-loading-dot.diplayer-loading-dot-7 {\n  animation-delay: 0.7s;\n}\n@keyframes diplayer-loading-dot-fade {\n  0% {\n    opacity: 0.7;\n    transform: scale(1.2, 1.2);\n  }\n  50% {\n    opacity: 0.25;\n    transform: scale(0.9, 0.9);\n  }\n  to {\n    opacity: 0.25;\n    transform: scale(0.85, 0.85);\n  }\n}\n.dplayer-controller-mask {\n  background: url(${A}) repeat-x bottom;\n  height: 98px;\n  width: 100%;\n  position: absolute;\n  bottom: 0;\n  transition: all 0.3s ease;\n}\n.dplayer-controller {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 41px;\n  padding: 0 20px;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n  transition: all 0.3s ease;\n}\n.dplayer-controller.dplayer-controller-comment .dplayer-icons {\n  display: none;\n}\n.dplayer-controller.dplayer-controller-comment .dplayer-icons.dplayer-comment-box {\n  display: block;\n}\n.dplayer-controller .dplayer-bar-wrap {\n  padding: 5px 0;\n  cursor: pointer;\n  position: absolute;\n  bottom: 33px;\n  width: calc(100% - 40px);\n  height: 3px;\n}\n.dplayer-controller .dplayer-bar-wrap:hover .dplayer-bar .dplayer-played .dplayer-thumb {\n  transform: scale(1);\n}\n.dplayer-controller .dplayer-bar-wrap:hover .dplayer-highlight {\n  display: block;\n  width: 8px;\n  transform: translateX(-4px);\n  top: 4px;\n  height: 40%;\n}\n.dplayer-controller .dplayer-bar-wrap .dplayer-highlight {\n  z-index: 12;\n  position: absolute;\n  top: 5px;\n  width: 6px;\n  height: 20%;\n  border-radius: 6px;\n  background-color: #fff;\n  text-align: center;\n  transform: translateX(-3px);\n  transition: all 0.2s ease-in-out;\n}\n.dplayer-controller .dplayer-bar-wrap .dplayer-highlight:hover .dplayer-highlight-text {\n  display: block;\n}\n.dplayer-controller .dplayer-bar-wrap .dplayer-highlight:hover ~ .dplayer-bar-preview {\n  opacity: 0;\n}\n.dplayer-controller .dplayer-bar-wrap .dplayer-highlight:hover ~ .dplayer-bar-time {\n  opacity: 0;\n}\n.dplayer-controller .dplayer-bar-wrap .dplayer-highlight .dplayer-highlight-text {\n  display: none;\n  position: absolute;\n  left: 50%;\n  top: -24px;\n  padding: 5px 8px;\n  background-color: rgba(0, 0, 0, 0.62);\n  color: #fff;\n  border-radius: 4px;\n  font-size: 12px;\n  white-space: nowrap;\n  transform: translateX(-50%);\n}\n.dplayer-controller .dplayer-bar-wrap .dplayer-bar-preview {\n  position: absolute;\n  background: #fff;\n  pointer-events: none;\n  display: none;\n  background-size: 16000px 100%;\n}\n.dplayer-controller .dplayer-bar-wrap .dplayer-bar-preview-canvas {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  z-index: 1;\n  pointer-events: none;\n}\n.dplayer-controller .dplayer-bar-wrap .dplayer-bar-time {\n  position: absolute;\n  left: 0px;\n  top: -20px;\n  border-radius: 4px;\n  padding: 5px 7px;\n  background-color: rgba(0, 0, 0, 0.62);\n  color: #fff;\n  font-size: 12px;\n  text-align: center;\n  opacity: 1;\n  transition: opacity 0.1s ease-in-out;\n  word-wrap: normal;\n  word-break: normal;\n  z-index: 2;\n  pointer-events: none;\n}\n.dplayer-controller .dplayer-bar-wrap .dplayer-bar-time.hidden {\n  opacity: 0;\n}\n.dplayer-controller .dplayer-bar-wrap .dplayer-bar {\n  position: relative;\n  height: 3px;\n  width: 100%;\n  background: rgba(255, 255, 255, 0.2);\n  cursor: pointer;\n}\n.dplayer-controller .dplayer-bar-wrap .dplayer-bar .dplayer-loaded {\n  position: absolute;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  background: rgba(255, 255, 255, 0.4);\n  height: 3px;\n  transition: all 0.5s ease;\n  will-change: width;\n}\n.dplayer-controller .dplayer-bar-wrap .dplayer-bar .dplayer-played {\n  position: absolute;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  height: 3px;\n  will-change: width;\n}\n.dplayer-controller .dplayer-bar-wrap .dplayer-bar .dplayer-played .dplayer-thumb {\n  position: absolute;\n  top: 0;\n  right: 5px;\n  margin-top: -4px;\n  margin-right: -10px;\n  height: 11px;\n  width: 11px;\n  border-radius: 50%;\n  cursor: pointer;\n  transition: all 0.3s ease-in-out;\n  transform: scale(0);\n}\n.dplayer-controller .dplayer-icons {\n  height: 38px;\n  position: absolute;\n  bottom: 0;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box {\n  display: none;\n  position: absolute;\n  transition: all 0.3s ease-in-out;\n  z-index: 2;\n  height: 38px;\n  bottom: 0;\n  left: 20px;\n  right: 20px;\n  color: #fff;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-icon {\n  padding: 7px;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-setting-icon {\n  position: absolute;\n  left: 0;\n  top: 0;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-send-icon {\n  position: absolute;\n  right: 0;\n  top: 0;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-setting-box {\n  position: absolute;\n  background: rgba(28, 28, 28, 0.9);\n  bottom: 41px;\n  left: 0;\n  box-shadow: 0 0 25px rgba(0, 0, 0, 0.3);\n  border-radius: 4px;\n  padding: 10px 10px 16px;\n  font-size: 14px;\n  width: 204px;\n  transition: all 0.3s ease-in-out;\n  transform: scale(0);\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-setting-box.dplayer-comment-setting-open {\n  transform: scale(1);\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-setting-box input[type=radio] {\n  display: none;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-setting-box label {\n  cursor: pointer;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-setting-box .dplayer-comment-setting-title {\n  font-size: 13px;\n  color: #fff;\n  line-height: 30px;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-setting-box .dplayer-comment-setting-type {\n  font-size: 0;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-setting-box .dplayer-comment-setting-type .dplayer-comment-setting-title {\n  margin-bottom: 6px;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-setting-box .dplayer-comment-setting-type label:nth-child(2) span {\n  border-radius: 4px 0 0 4px;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-setting-box .dplayer-comment-setting-type label:nth-child(4) span {\n  border-radius: 0 4px 4px 0;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-setting-box .dplayer-comment-setting-type span {\n  width: 33%;\n  padding: 4px 6px;\n  line-height: 16px;\n  display: inline-block;\n  font-size: 12px;\n  color: #fff;\n  border: 1px solid #fff;\n  margin-right: -1px;\n  box-sizing: border-box;\n  text-align: center;\n  cursor: pointer;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-setting-box .dplayer-comment-setting-type input:checked + span {\n  background: #E4E4E6;\n  color: #1c1c1c;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-setting-box .dplayer-comment-setting-color {\n  font-size: 0;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-setting-box .dplayer-comment-setting-color label {\n  font-size: 0;\n  padding: 6px;\n  display: inline-block;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-setting-box .dplayer-comment-setting-color span {\n  width: 22px;\n  height: 22px;\n  display: inline-block;\n  border-radius: 50%;\n  box-sizing: border-box;\n  cursor: pointer;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-setting-box .dplayer-comment-setting-color span:hover {\n  animation: my-face 5s infinite ease-in-out;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-input {\n  outline: none;\n  border: none;\n  padding: 8px 31px;\n  font-size: 14px;\n  line-height: 18px;\n  text-align: center;\n  border-radius: 4px;\n  background: none;\n  margin: 0;\n  height: 100%;\n  box-sizing: border-box;\n  width: 100%;\n  color: #fff;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-input::-moz-placeholder {\n  color: #fff;\n  opacity: 0.8;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-input::placeholder {\n  color: #fff;\n  opacity: 0.8;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-input::-ms-clear {\n  display: none;\n}\n.dplayer-controller .dplayer-icons.dplayer-icons-left .dplayer-icon {\n  padding: 7px;\n}\n.dplayer-controller .dplayer-icons.dplayer-icons-right {\n  right: 20px;\n}\n.dplayer-controller .dplayer-icons.dplayer-icons-right .dplayer-icon {\n  padding: 8px;\n}\n.dplayer-controller .dplayer-icons .dplayer-time,\n.dplayer-controller .dplayer-icons .dplayer-live-badge {\n  line-height: 38px;\n  color: #eee;\n  text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);\n  vertical-align: middle;\n  font-size: 13px;\n  cursor: default;\n}\n.dplayer-controller .dplayer-icons .dplayer-live-dot {\n  display: inline-block;\n  width: 6px;\n  height: 6px;\n  vertical-align: 4%;\n  margin-right: 5px;\n  content: '';\n  border-radius: 6px;\n}\n.dplayer-controller .dplayer-icons .dplayer-icon {\n  width: 40px;\n  height: 100%;\n  border: none;\n  background-color: transparent;\n  outline: none;\n  cursor: pointer;\n  vertical-align: middle;\n  box-sizing: border-box;\n  display: inline-block;\n}\n.dplayer-controller .dplayer-icons .dplayer-icon .dplayer-icon-content {\n  transition: all 0.2s ease-in-out;\n  opacity: 0.8;\n}\n.dplayer-controller .dplayer-icons .dplayer-icon:hover .dplayer-icon-content {\n  opacity: 1;\n}\n.dplayer-controller .dplayer-icons .dplayer-icon.dplayer-quality-icon {\n  color: #fff;\n  width: auto;\n  line-height: 22px;\n  font-size: 14px;\n}\n.dplayer-controller .dplayer-icons .dplayer-icon.dplayer-comment-icon {\n  padding: 10px 9px 9px;\n}\n.dplayer-controller .dplayer-icons .dplayer-icon.dplayer-setting-icon {\n  padding-top: 8.5px;\n}\n.dplayer-controller .dplayer-icons .dplayer-icon.dplayer-volume-icon {\n  width: 43px;\n}\n.dplayer-controller .dplayer-icons .dplayer-volume {\n  position: relative;\n  display: inline-block;\n  cursor: pointer;\n  height: 100%;\n}\n.dplayer-controller .dplayer-icons .dplayer-volume:hover .dplayer-volume-bar-wrap .dplayer-volume-bar {\n  width: 45px;\n}\n.dplayer-controller .dplayer-icons .dplayer-volume:hover .dplayer-volume-bar-wrap .dplayer-volume-bar .dplayer-volume-bar-inner .dplayer-thumb {\n  transform: scale(1);\n}\n.dplayer-controller .dplayer-icons .dplayer-volume.dplayer-volume-active .dplayer-volume-bar-wrap .dplayer-volume-bar {\n  width: 45px;\n}\n.dplayer-controller .dplayer-icons .dplayer-volume.dplayer-volume-active .dplayer-volume-bar-wrap .dplayer-volume-bar .dplayer-volume-bar-inner .dplayer-thumb {\n  transform: scale(1);\n}\n.dplayer-controller .dplayer-icons .dplayer-volume .dplayer-volume-bar-wrap {\n  display: inline-block;\n  margin: 0 10px 0 -5px;\n  vertical-align: middle;\n  height: 100%;\n}\n.dplayer-controller .dplayer-icons .dplayer-volume .dplayer-volume-bar-wrap .dplayer-volume-bar {\n  position: relative;\n  top: 17px;\n  width: 0;\n  height: 3px;\n  background: #aaa;\n  transition: all 0.3s ease-in-out;\n}\n.dplayer-controller .dplayer-icons .dplayer-volume .dplayer-volume-bar-wrap .dplayer-volume-bar .dplayer-volume-bar-inner {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  height: 100%;\n  transition: all 0.1s ease;\n  will-change: width;\n}\n.dplayer-controller .dplayer-icons .dplayer-volume .dplayer-volume-bar-wrap .dplayer-volume-bar .dplayer-volume-bar-inner .dplayer-thumb {\n  position: absolute;\n  top: 0;\n  right: 5px;\n  margin-top: -4px;\n  margin-right: -10px;\n  height: 11px;\n  width: 11px;\n  border-radius: 50%;\n  cursor: pointer;\n  transition: all 0.3s ease-in-out;\n  transform: scale(0);\n}\n.dplayer-controller .dplayer-icons .dplayer-subtitle-btn {\n  display: inline-block;\n  height: 100%;\n}\n.dplayer-controller .dplayer-icons .dplayer-subtitles {\n  display: inline-block;\n  height: 100%;\n}\n.dplayer-controller .dplayer-icons .dplayer-subtitles .dplayer-subtitles-box {\n  position: absolute;\n  right: 0;\n  bottom: 50px;\n  transform: scale(0);\n  width: -moz-fit-content;\n  width: fit-content;\n  max-width: 240px;\n  min-width: 120px;\n  border-radius: 2px;\n  background: rgba(28, 28, 28, 0.9);\n  padding: 7px 0;\n  transition: all 0.3s ease-in-out;\n  overflow: auto;\n  z-index: 2;\n}\n.dplayer-controller .dplayer-icons .dplayer-subtitles .dplayer-subtitles-box.dplayer-subtitles-panel {\n  display: block;\n}\n.dplayer-controller .dplayer-icons .dplayer-subtitles .dplayer-subtitles-box.dplayer-subtitles-box-open {\n  transform: scale(1);\n}\n.dplayer-controller .dplayer-icons .dplayer-subtitles .dplayer-subtitles-item {\n  height: 30px;\n  padding: 5px 10px;\n  box-sizing: border-box;\n  cursor: pointer;\n  position: relative;\n}\n.dplayer-controller .dplayer-icons .dplayer-subtitles .dplayer-subtitles-item:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n.dplayer-controller .dplayer-icons .dplayer-setting {\n  display: inline-block;\n  height: 100%;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-box {\n  position: absolute;\n  right: 0;\n  bottom: 50px;\n  transform: scale(0);\n  width: 150px;\n  border-radius: 2px;\n  background: rgba(28, 28, 28, 0.9);\n  padding: 7px 0;\n  transition: all 0.3s ease-in-out;\n  overflow: hidden;\n  z-index: 2;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-box > div {\n  display: none;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-box > div.dplayer-setting-origin-panel {\n  display: block;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-box.dplayer-setting-box-open {\n  transform: scale(1);\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-box.dplayer-setting-box-narrow {\n  width: 70px;\n  text-align: center;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-box.dplayer-setting-box-speed .dplayer-setting-origin-panel {\n  display: none;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-box.dplayer-setting-box-speed .dplayer-setting-speed-panel {\n  display: block;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-item,\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-speed-item {\n  height: 30px;\n  padding: 5px 10px;\n  box-sizing: border-box;\n  cursor: pointer;\n  position: relative;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-item:hover,\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-speed-item:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-danmaku {\n  padding: 5px 0;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-danmaku .dplayer-label {\n  padding: 0 10px;\n  display: inline;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-danmaku:hover .dplayer-label {\n  display: none;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-danmaku:hover .dplayer-danmaku-bar-wrap {\n  display: inline-block;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-danmaku.dplayer-setting-danmaku-active .dplayer-label {\n  display: none;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-danmaku.dplayer-setting-danmaku-active .dplayer-danmaku-bar-wrap {\n  display: inline-block;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-danmaku .dplayer-danmaku-bar-wrap {\n  padding: 0 10px;\n  box-sizing: border-box;\n  display: none;\n  vertical-align: middle;\n  height: 100%;\n  width: 100%;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-danmaku .dplayer-danmaku-bar-wrap .dplayer-danmaku-bar {\n  position: relative;\n  top: 8.5px;\n  width: 100%;\n  height: 3px;\n  background: #fff;\n  transition: all 0.3s ease-in-out;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-danmaku .dplayer-danmaku-bar-wrap .dplayer-danmaku-bar .dplayer-danmaku-bar-inner {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  height: 100%;\n  transition: all 0.1s ease;\n  background: #aaa;\n  will-change: width;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-danmaku .dplayer-danmaku-bar-wrap .dplayer-danmaku-bar .dplayer-danmaku-bar-inner .dplayer-thumb {\n  position: absolute;\n  top: 0;\n  right: 5px;\n  margin-top: -4px;\n  margin-right: -10px;\n  height: 11px;\n  width: 11px;\n  border-radius: 50%;\n  cursor: pointer;\n  transition: all 0.3s ease-in-out;\n  background: #aaa;\n}\n.dplayer-controller .dplayer-icons .dplayer-full {\n  display: inline-block;\n  height: 100%;\n  position: relative;\n}\n.dplayer-controller .dplayer-icons .dplayer-full:hover .dplayer-full-in-icon {\n  display: block;\n}\n.dplayer-controller .dplayer-icons .dplayer-full .dplayer-full-in-icon {\n  position: absolute;\n  top: -30px;\n  z-index: 1;\n  display: none;\n}\n.dplayer-controller .dplayer-icons .dplayer-quality {\n  position: relative;\n  display: inline-block;\n  height: 100%;\n  z-index: 2;\n}\n.dplayer-controller .dplayer-icons .dplayer-quality:hover .dplayer-quality-list {\n  display: block;\n}\n.dplayer-controller .dplayer-icons .dplayer-quality:hover .dplayer-quality-mask {\n  display: block;\n}\n.dplayer-controller .dplayer-icons .dplayer-quality .dplayer-quality-mask {\n  display: none;\n  position: absolute;\n  bottom: 38px;\n  left: -18px;\n  width: 80px;\n  padding-bottom: 12px;\n}\n.dplayer-controller .dplayer-icons .dplayer-quality .dplayer-quality-list {\n  display: none;\n  font-size: 12px;\n  width: 80px;\n  border-radius: 2px;\n  background: rgba(28, 28, 28, 0.9);\n  padding: 5px 0;\n  transition: all 0.3s ease-in-out;\n  overflow: hidden;\n  color: #fff;\n  text-align: center;\n}\n.dplayer-controller .dplayer-icons .dplayer-quality .dplayer-quality-item {\n  height: 25px;\n  box-sizing: border-box;\n  cursor: pointer;\n  line-height: 25px;\n}\n.dplayer-controller .dplayer-icons .dplayer-quality .dplayer-quality-item:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n.dplayer-controller .dplayer-icons .dplayer-comment {\n  display: inline-block;\n  height: 100%;\n}\n.dplayer-controller .dplayer-icons .dplayer-label {\n  color: #eee;\n  font-size: 13px;\n  display: inline-block;\n  vertical-align: middle;\n  white-space: nowrap;\n}\n.dplayer-controller .dplayer-icons .dplayer-toggle {\n  width: 32px;\n  height: 20px;\n  text-align: center;\n  font-size: 0;\n  vertical-align: middle;\n  position: absolute;\n  top: 5px;\n  right: 10px;\n}\n.dplayer-controller .dplayer-icons .dplayer-toggle input {\n  max-height: 0;\n  max-width: 0;\n  display: none;\n}\n.dplayer-controller .dplayer-icons .dplayer-toggle input + label {\n  display: inline-block;\n  position: relative;\n  box-shadow: #dfdfdf 0 0 0 0 inset;\n  border: 1px solid #dfdfdf;\n  height: 20px;\n  width: 32px;\n  border-radius: 10px;\n  box-sizing: border-box;\n  cursor: pointer;\n  transition: 0.2s ease-in-out;\n}\n.dplayer-controller .dplayer-icons .dplayer-toggle input + label:before {\n  content: "";\n  position: absolute;\n  display: block;\n  height: 18px;\n  width: 18px;\n  top: 0;\n  left: 0;\n  border-radius: 15px;\n  transition: 0.2s ease-in-out;\n}\n.dplayer-controller .dplayer-icons .dplayer-toggle input + label:after {\n  content: "";\n  position: absolute;\n  display: block;\n  left: 0;\n  top: 0;\n  border-radius: 15px;\n  background: #fff;\n  transition: 0.2s ease-in-out;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);\n  height: 18px;\n  width: 18px;\n}\n.dplayer-controller .dplayer-icons .dplayer-toggle input:checked + label {\n  border-color: rgba(255, 255, 255, 0.5);\n}\n.dplayer-controller .dplayer-icons .dplayer-toggle input:checked + label:before {\n  width: 30px;\n  background: rgba(255, 255, 255, 0.5);\n}\n.dplayer-controller .dplayer-icons .dplayer-toggle input:checked + label:after {\n  left: 12px;\n}\n.dplayer-mobile-play {\n  display: none;\n  width: 50px;\n  height: 50px;\n  border: none;\n  background-color: transparent;\n  outline: none;\n  cursor: pointer;\n  box-sizing: border-box;\n  bottom: 0;\n  opacity: 0.8;\n  position: absolute;\n  left: 50%;\n  top: 50%;\n  transform: translate(-50%, -50%);\n}\n.dplayer-danmaku {\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: 0;\n  bottom: 0;\n  font-size: 22px;\n  color: #fff;\n}\n.dplayer-danmaku .dplayer-danmaku-item {\n  display: inline-block;\n  pointer-events: none;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n  cursor: default;\n  white-space: nowrap;\n  text-shadow: 0.5px 0.5px 0.5px rgba(0, 0, 0, 0.5);\n}\n.dplayer-danmaku .dplayer-danmaku-item--demo {\n  position: absolute;\n  visibility: hidden;\n}\n.dplayer-danmaku .dplayer-danmaku-right {\n  position: absolute;\n  right: 0;\n  transform: translateX(100%);\n}\n.dplayer-danmaku .dplayer-danmaku-right.dplayer-danmaku-move {\n  will-change: transform;\n  animation-name: 'danmaku';\n  animation-timing-function: linear;\n  animation-play-state: paused;\n}\n@keyframes danmaku {\n  from {\n    transform: translateX(100%);\n  }\n}\n.dplayer-danmaku .dplayer-danmaku-top,\n.dplayer-danmaku .dplayer-danmaku-bottom {\n  position: absolute;\n  width: 100%;\n  text-align: center;\n  visibility: hidden;\n}\n.dplayer-danmaku .dplayer-danmaku-top.dplayer-danmaku-move,\n.dplayer-danmaku .dplayer-danmaku-bottom.dplayer-danmaku-move {\n  will-change: visibility;\n  animation-name: 'danmaku-center';\n  animation-timing-function: linear;\n  animation-play-state: paused;\n}\n@keyframes danmaku-center {\n  from {\n    visibility: visible;\n  }\n  to {\n    visibility: visible;\n  }\n}\n.dplayer-logo {\n  pointer-events: none;\n  position: absolute;\n  left: 20px;\n  top: 20px;\n  max-width: 50px;\n  max-height: 50px;\n}\n.dplayer-logo img {\n  max-width: 100%;\n  max-height: 100%;\n  background: none;\n}\n.dplayer-menu {\n  position: absolute;\n  width: 170px;\n  border-radius: 2px;\n  background: rgba(28, 28, 28, 0.85);\n  padding: 5px 0;\n  overflow: hidden;\n  z-index: 3;\n  display: none;\n}\n.dplayer-menu.dplayer-menu-show {\n  display: block;\n}\n.dplayer-menu .dplayer-menu-item {\n  height: 30px;\n  box-sizing: border-box;\n  cursor: pointer;\n}\n.dplayer-menu .dplayer-menu-item:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n.dplayer-menu .dplayer-menu-item a {\n  padding: 0 10px;\n  line-height: 30px;\n  color: #eee;\n  font-size: 13px;\n  display: inline-block;\n  vertical-align: middle;\n  width: 100%;\n  box-sizing: border-box;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  overflow: hidden;\n}\n.dplayer-menu .dplayer-menu-item a:hover {\n  -webkit-text-decoration: none;\n  text-decoration: none;\n}\n.dplayer-notice-list {\n  position: absolute;\n  bottom: 60px;\n  left: 20px;\n}\n.dplayer-notice-list .dplayer-notice {\n  border-radius: 2px;\n  background: rgba(28, 28, 28, 0.9);\n  transition: all 0.3s ease-in-out;\n  overflow: hidden;\n  color: #fff;\n  display: table;\n  pointer-events: none;\n  animation: showNotice 0.3s ease 1 forwards;\n}\n.dplayer-notice-list .remove-notice {\n  animation: removeNotice 0.3s ease 1 forwards;\n}\n@keyframes showNotice {\n  from {\n    padding: 0;\n    font-size: 0;\n    margin-top: 0;\n  }\n  to {\n    padding: 7px 20px;\n    font-size: 14px;\n    margin-top: 5px;\n  }\n}\n@keyframes removeNotice {\n  0% {\n    padding: 7px 20px;\n    font-size: 14px;\n    margin-top: 5px;\n  }\n  20% {\n    font-size: 12px;\n  }\n  21% {\n    font-size: 0;\n    padding: 7px 10px;\n  }\n  100% {\n    padding: 0;\n    margin-top: 0;\n    font-size: 0;\n  }\n}\n.dplayer-subtitle {\n  position: absolute;\n  bottom: 40px;\n  width: 90%;\n  left: 5%;\n  text-align: center;\n  color: #fff;\n  text-shadow: 0.5px 0.5px 0.5px rgba(0, 0, 0, 0.5);\n  font-size: 20px;\n}\n.dplayer-subtitle.dplayer-subtitle-hide {\n  display: none;\n}\n.dplayer-mask {\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  z-index: 1;\n  display: none;\n}\n.dplayer-mask.dplayer-mask-show {\n  display: block;\n}\n.dplayer-video-wrap {\n  position: relative;\n  background: #000;\n  font-size: 0;\n  width: 100%;\n  height: 100%;\n}\n.dplayer-video-wrap .dplayer-video {\n  width: 100%;\n  height: 100%;\n  display: none;\n}\n.dplayer-video-wrap .dplayer-video-current {\n  display: block;\n}\n.dplayer-video-wrap .dplayer-video-prepare {\n  display: none;\n}\n.dplayer-info-panel {\n  position: absolute;\n  top: 10px;\n  left: 10px;\n  width: 400px;\n  background: rgba(28, 28, 28, 0.8);\n  padding: 10px;\n  color: #fff;\n  font-size: 12px;\n  border-radius: 2px;\n}\n.dplayer-info-panel-hide {\n  display: none;\n}\n.dplayer-info-panel .dplayer-info-panel-close {\n  cursor: pointer;\n  position: absolute;\n  right: 10px;\n  top: 10px;\n}\n.dplayer-info-panel .dplayer-info-panel-item > span {\n  display: inline-block;\n  vertical-align: middle;\n  line-height: 15px;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  overflow: hidden;\n}\n.dplayer-info-panel .dplayer-info-panel-item-title {\n  width: 100px;\n  text-align: right;\n  margin-right: 10px;\n}\n.dplayer-info-panel .dplayer-info-panel-item-data {\n  width: 260px;\n}\n`,
          "", {
            version: 3,
            sources: ["webpack://./src/css/global.less", "webpack://./src/css/index.less",
              "webpack://./src/css/player.less", "webpack://./src/css/balloon.less",
              "webpack://./src/css/bezel.less", "webpack://./src/css/controller.less",
              "webpack://./src/css/danmaku.less", "webpack://./src/css/logo.less",
              "webpack://./src/css/menu.less", "webpack://./src/css/notice.less",
              "webpack://./src/css/subtitle.less", "webpack://./src/css/video.less",
              "webpack://./src/css/info-panel.less"
            ],
            names: [],
            mappings: "AAAA;EACI;IACI,6CAAA;ECEN;EDAE;IACI,+CAAA;ECEN;EDAE;IACI,8CAAA;ECEN;EDAE;IACI,+CAAA;ECEN;EDAE;IACI,6CAAA;ECEN;EDAE;IACI,8CAAA;ECEN;EDAE;IACI,8CAAA;ECEN;EDAE;IACI,+CAAA;ECEN;EDAE;IACI,8CAAA;ECEN;EDAE;IACI,8CAAA;ECEN;EDAE;IACI,8CAAA;ECEN;EDAE;IACI,6CAAA;ECEN;EDAE;IACI,6CAAA;ECEN;EDAE;IACI,6CAAA;ECEN;EDAE;IACI,8CAAA;ECEN;EDAE;IACI,8CAAA;ECEN;EDAE;IACI,8CAAA;ECEN;EDAE;IACI,8CAAA;ECEN;EDAE;IACI,8CAAA;ECEN;EDAE;IACI,8CAAA;ECEN;EDAE;IACI,8CAAA;ECEN;EDAE;IACI,6CAAA;ECEN;EDAE;IACI,8CAAA;ECEN;EDAE;IACI,8CAAA;ECEN;EDAE;IACI,6CAAA;ECEN;EDAE;IACI,6CAAA;ECEN;EDAE;IACI,8CAAA;ECEN;EDAE;IACI,6CAAA;ECEN;EDAE;IACI,6CAAA;ECEN;EDAE;IACI,6CAAA;ECEN;EDAE;IACI,8CAAA;ECEN;EDAE;IACI,8CAAA;ECEN;EDAE;IACI,8CAAA;ECEN;EDAE;IACI,+CAAA;ECEN;EDAE;IACI,6CAAA;ECEN;EDAE;IACI,6CAAA;ECEN;EDAE;IACI,8CAAA;ECEN;EDAE;IACI,8CAAA;ECEN;EDAE;IACI,8CAAA;ECEN;EDAE;IACI,6CAAA;ECEN;EDAE;IACI,8CAAA;ECEN;EDAE;IACI,6CAAA;ECEN;EDAE;IACI,+CAAA;ECEN;EDAE;IACI,8CAAA;ECEN;EDAE;IACI,8CAAA;ECEN;EDAE;IACI,8CAAA;ECEN;EDAE;IACI,6CAAA;ECEN;EDAE;IACI,8CAAA;ECEN;EDAE;IACI,+CAAA;ECEN;EDAE;;IAEI,uCAAA;ECEN;AACF;ACzJA;EACI,kBAAA;EACA,gBAAA;EACA,yBAAA;KAAA,sBAAA;UAAA,iBAAA;EACA,cAAA;AD2JJ;AC/JA;EAOQ,uBAAA;AD2JR;AClKA;EAWQ,WAAA;EACA,YAAA;AD0JR;ACtKA;;EAgBY,UAAA;AD0JZ;ACtJI;EACI,WAAA;EACA,YAAA;EACA,gBAAA;EACA,eAAA;EACA,eAAA;EACA,OAAA;EACA,MAAA;EACA,SAAA;EACA,UAAA;EACA,0BAAA;ADwJR;ACpJI;;;EAKY,aAAA;ADoJhB;ACzJI;EAUQ,aAAA;ADkJZ;AC5JI;EAcQ,aAAA;ADiJZ;AC7II;EAEQ,aAAA;AD8IZ;AChJI;EAKQ,aAAA;AD8IZ;ACnJI;EAQQ,aAAA;AD8IZ;ACtJI;EAWQ,aAAA;AD8IZ;AC3IQ;EAEQ,aAAA;AD4IhB;ACvII;EAEQ,eAAA;ADwIZ;AC1II;EAKQ,cAAA;ADwIZ;ACpII;EAEQ,6BAAA;ADqIZ;AClIQ;EAAA;IAEQ,UAAA;EDoId;ECtIM;IAKQ,UAAA;EDoId;ECjIU;IAEQ,UAAA;EDkIlB;ECpIU;IAKQ,UAAA;EDkIlB;AACF;AC7HI;EAEQ,cAAA;AD8HZ;AC1HI;;;;EAIQ,4BAAA;AD4HZ;ACxHI;EACI,YAAA;AD0HR;AC3HI;EAIQ,UAAA;EACA,2BAAA;AD0HZ;AC/HI;EAQQ,UAAA;EACA,2BAAA;AD0HZ;ACvHI;EAEQ,UAAA;ADwHZ;AC1HI;EAKQ,UAAA;ADwHZ;ACrHI;EAKI,sBAAA;EACA,uBAAA;ADuHR;AC7HI;EACI,eAAA;EACA,eAAA;EACA,OAAA;EACA,MAAA;ADyHR;ACrHI;;;;;EAOY,aAAA;ADqHhB;AC5HI;EAUY,gBAAA;EACA,qBAAA;ADqHhB;AChII;EAgBQ,aAAA;ADmHZ;AChHQ;EAEQ,aAAA;ADiHhB;ACtII;EA0BQ,cAAA;AD+GZ;ACzGA;EACI,eAAA;EACA,MAAA;EACA,OAAA;EACA,SAAA;EACA,UAAA;AD2GJ;AElSA;EACI,aAAA;AFoSJ;AEjSA;EACI,oBAAA;EACA,iCAAA;AFmSJ;AEhSA;EACI,gBAAA;AFkSJ;AG9SA;EACI,kBAAA;EACA,OAAA;EACA,QAAA;EACA,MAAA;EACA,SAAA;EACA,eAAA;EACA,WAAA;EACA,oBAAA;AHgTJ;AGxTA;EAUQ,kBAAA;EACA,QAAA;EACA,SAAA;EACA,uBAAA;EACA,YAAA;EACA,WAAA;EACA,aAAA;EACA,sBAAA;EACA,8BAAA;EACA,kBAAA;EACA,UAAA;EACA,oBAAA;AHiTR;AGhTQ;EACI,iCAAA;AHkTZ;AGhTQ;EACI;IACI,UAAA;IACA,mBAAA;EHkTd;EGhTU;IACI,UAAA;IACA,mBAAA;EHkTd;AACF;AGnVA;EAqCQ,kBAAA;EACA,QAAA;EACA,gBAAA;EACA,WAAA;EACA,kBAAA;EACA,eAAA;EACA,iBAAA;EACA,0CAAA;AHiTR;AG7VA;EA+CQ,aAAA;EACA,kBAAA;EACA,QAAA;EACA,SAAA;EACA,uBAAA;EACA,YAAA;EACA,WAAA;EACA,oBAAA;AHiTR;AGvWA;EAwDY,aAAA;AHkTZ;AG1WA;EA2DY,uDAAA;EACA,UAAA;EACA,yBAAA;AHkTZ;AGhTiB;EACG,qBAAA;AHkTpB;AGnTiB;EACG,qBAAA;AHqTpB;AGtTiB;EACG,qBAAA;AHwTpB;AGzTiB;EACG,qBAAA;AH2TpB;AG5TiB;EACG,qBAAA;AH8TpB;AG/TiB;EACG,qBAAA;AHiUpB;AGlUiB;EACG,qBAAA;AHoUpB;AGhUQ;EACI;IACI,YAAA;IACA,0BAAA;EHkUd;EGhUU;IACI,aAAA;IACA,0BAAA;EHkUd;EGhUU;IACI,aAAA;IACA,4BAAA;EHkUd;AACF;AIlZA;EACI,mEAAA;EACA,YAAA;EACA,WAAA;EACA,kBAAA;EACA,SAAA;EACA,yBAAA;AJoZJ;AIjZA;EACI,kBAAA;EACA,SAAA;EACA,OAAA;EACA,QAAA;EACA,YAAA;EACA,eAAA;EACA,yBAAA;KAAA,sBAAA;UAAA,iBAAA;EACA,yBAAA;AJmZJ;AIlZI;EAEQ,aAAA;AJmZZ;AIrZI;EAKQ,cAAA;AJmZZ;AIjaA;EAkBQ,cAAA;EACA,eAAA;EACA,kBAAA;EACA,YAAA;EACA,wBAAA;EACA,WAAA;AJkZR;AIjZQ;EAEQ,mBAAA;AJkZhB;AIpZQ;EAKQ,cAAA;EACA,UAAA;EACA,2BAAA;EACA,QAAA;EACA,WAAA;AJkZhB;AInbA;EAqCY,WAAA;EACA,kBAAA;EACA,QAAA;EACA,UAAA;EACA,WAAA;EACA,kBAAA;EACA,sBAAA;EACA,kBAAA;EACA,2BAAA;EACA,gCAAA;AJiZZ;AIhZY;EAEQ,cAAA;AJiZpB;AI/YgB;EACI,UAAA;AJiZpB;AI/YgB;EACI,UAAA;AJiZpB;AIxcA;EA2DgB,aAAA;EACA,kBAAA;EACA,SAAA;EACA,UAAA;EACA,gBAAA;EACA,qCAAA;EACA,WAAA;EACA,kBAAA;EACA,eAAA;EACA,mBAAA;EACA,2BAAA;AJgZhB;AIrdA;EAyEY,kBAAA;EACA,gBAAA;EACA,oBAAA;EACA,aAAA;EACA,6BAAA;AJ+YZ;AI5dA;EAgFY,kBAAA;EACA,WAAA;EACA,YAAA;EACA,UAAA;EACA,oBAAA;AJ+YZ;AIneA;EA0FY,kBAAA;EACA,SAAA;EACA,UAAA;EACA,kBAAA;EACA,gBAAA;EACA,qCAAA;EACA,WAAA;EACA,eAAA;EACA,kBAAA;EACA,UAAA;EACA,oCAAA;EACA,iBAAA;EACA,kBAAA;EACA,UAAA;EACA,oBAAA;AJ4YZ;AI7ZY;EACI,UAAA;AJ+ZhB;AIvfA;EA2GY,kBAAA;EACA,WAAA;EACA,WAAA;EACA,oCAAA;EACA,eAAA;AJ+YZ;AI9fA;EAiHgB,kBAAA;EACA,OAAA;EACA,MAAA;EACA,SAAA;EACA,oCAAA;EACA,WAAA;EACA,yBAAA;EACA,kBAAA;AJgZhB;AIxgBA;EA2HgB,kBAAA;EACA,OAAA;EACA,MAAA;EACA,SAAA;EACA,WAAA;EACA,kBAAA;AJgZhB;AIhhBA;EAkIoB,kBAAA;EACA,MAAA;EACA,UAAA;EACA,gBAAA;EACA,mBAAA;EACA,YAAA;EACA,WAAA;EACA,kBAAA;EACA,eAAA;EACA,gCAAA;EACA,mBAAA;AJiZpB;AI7hBA;EAkJQ,YAAA;EACA,kBAAA;EACA,SAAA;AJ8YR;AI7YQ;EACI,aAAA;EACA,kBAAA;EACA,gCAAA;EACA,UAAA;EACA,YAAA;EACA,SAAA;EACA,UAAA;EACA,WAAA;EACA,WAAA;AJ+YZ;AIxZQ;EAWQ,YAAA;AJgZhB;AI3ZQ;EAcQ,kBAAA;EACA,OAAA;EACA,MAAA;AJgZhB;AIhaQ;EAmBQ,kBAAA;EACA,QAAA;EACA,MAAA;AJgZhB;AIraQ;EAwBQ,kBAAA;EACA,iCAAA;EACA,YAAA;EACA,OAAA;EACA,uCAAA;EACA,kBAAA;EACA,uBAAA;EACA,eAAA;EACA,YAAA;EACA,gCAAA;EACA,mBAAA;AJgZhB;AI/YgB;EACI,mBAAA;AJiZpB;AIrbQ;EAuCY,aAAA;AJiZpB;AIxbQ;EA0CY,eAAA;AJiZpB;AI3bQ;EA6CY,eAAA;EACA,WAAA;EACA,iBAAA;AJiZpB;AIhcQ;EAkDY,YAAA;AJiZpB;AIncQ;EAoDgB,kBAAA;AJkZxB;AI/YwB;EAEQ,0BAAA;AJgZhC;AI7YwB;EAEQ,0BAAA;AJ8YhC;AI5cQ;EAmEgB,UAAA;EACA,gBAAA;EACA,iBAAA;EACA,qBAAA;EACA,eAAA;EACA,WAAA;EACA,sBAAA;EACA,kBAAA;EACA,sBAAA;EACA,kBAAA;EACA,eAAA;AJ4YxB;AIzdQ;EAgFgB,mBAAA;EACA,cAAA;AJ4YxB;AI7dQ;EAqFY,YAAA;AJ2YpB;AIheQ;EAuFgB,YAAA;EACA,YAAA;EACA,qBAAA;AJ4YxB;AIreQ;EA4FgB,WAAA;EACA,YAAA;EACA,qBAAA;EACA,kBAAA;EACA,sBAAA;EACA,eAAA;AJ4YxB;AI3YwB;EACI,0CAAA;AJ6Y5B;AIhfQ;EAyGQ,aAAA;EACA,YAAA;EACA,iBAAA;EACA,eAAA;EACA,iBAAA;EACA,kBAAA;EACA,kBAAA;EACA,gBAAA;EACA,SAAA;EACA,YAAA;EACA,sBAAA;EACA,WAAA;EACA,WAAA;AJ0YhB;AIzYgB;EACI,WAAA;EACA,YAAA;AJ2YpB;AI7YgB;EACI,WAAA;EACA,YAAA;AJ2YpB;AIzYgB;EACI,aAAA;AJ2YpB;AIvYQ;EAEQ,YAAA;AJwYhB;AIrYQ;EACI,WAAA;AJuYZ;AIxYQ;EAGQ,YAAA;AJwYhB;AIpqBA;;EAiSY,iBAAA;EACA,WAAA;EACA,uCAAA;EACA,sBAAA;EACA,eAAA;EACA,eAAA;AJuYZ;AI7qBA;EAySY,qBAAA;EACA,UAAA;EACA,WAAA;EACA,kBAAA;EACA,iBAAA;EACA,WAAA;EACA,kBAAA;AJuYZ;AItrBA;EAkTY,WAAA;EACA,YAAA;EACA,YAAA;EACA,6BAAA;EACA,aAAA;EACA,eAAA;EACA,sBAAA;EACA,sBAAA;EACA,qBAAA;AJuYZ;AIjsBA;EA4TgB,gCAAA;EACA,YAAA;AJwYhB;AItYY;EAEQ,UAAA;AJuYpB;AIpYY;EACI,WAAA;EACA,WAAA;EACA,iBAAA;EACA,eAAA;AJsYhB;AIpYY;EACI,qBAAA;AJsYhB;AIpYY;EACI,kBAAA;AJsYhB;AIpYY;EACI,WAAA;AJsYhB;AIvtBA;EAqVY,kBAAA;EACA,qBAAA;EACA,eAAA;EACA,YAAA;AJqYZ;AIpYY;EAEQ,WAAA;AJqYpB;AIvYY;EAKQ,mBAAA;AJqYpB;AIlYY;EAEQ,WAAA;AJmYpB;AIrYY;EAKQ,mBAAA;AJmYpB;AIzuBA;EA0WgB,qBAAA;EACA,qBAAA;EACA,sBAAA;EACA,YAAA;AJkYhB;AI/uBA;EA+WoB,kBAAA;EACA,SAAA;EACA,QAAA;EACA,WAAA;EACA,gBAAA;EACA,gCAAA;AJmYpB;AIvvBA;EAsXwB,kBAAA;EACA,SAAA;EACA,OAAA;EACA,YAAA;EACA,yBAAA;EACA,kBAAA;AJoYxB;AI/vBA;EA6X4B,kBAAA;EACA,MAAA;EACA,UAAA;EACA,gBAAA;EACA,mBAAA;EACA,YAAA;EACA,WAAA;EACA,kBAAA;EACA,eAAA;EACA,gCAAA;EACA,mBAAA;AJqY5B;AI5wBA;EA8YY,qBAAA;EACA,YAAA;AJiYZ;AIhxBA;EAkZY,qBAAA;EACA,YAAA;AJiYZ;AIpxBA;EAqZgB,kBAAA;EACA,QAAA;EACA,YAAA;EACA,mBAAA;EACA,uBAAA;EAAA,kBAAA;EACA,gBAAA;EACA,gBAAA;EACA,kBAAA;EACA,iCAAA;EACA,cAAA;EACA,gCAAA;EACA,cAAA;EACA,UAAA;AJkYhB;AIjYgB;EACI,cAAA;AJmYpB;AIjYgB;EACI,mBAAA;AJmYpB;AIzyBA;EA0agB,YAAA;EACA,iBAAA;EACA,sBAAA;EACA,eAAA;EACA,kBAAA;AJkYhB;AIjYgB;EACI,0CAAA;AJmYpB;AInzBA;EAqbY,qBAAA;EACA,YAAA;AJiYZ;AIvzBA;EAwbgB,kBAAA;EACA,QAAA;EACA,YAAA;EACA,mBAAA;EACA,YAAA;EACA,kBAAA;EACA,iCAAA;EACA,cAAA;EACA,gCAAA;EACA,gBAAA;EACA,UAAA;AJkYhB;AIjYgB;EACI,aAAA;AJmYpB;AIlYoB;EACI,cAAA;AJoYxB;AIjYgB;EACI,mBAAA;AJmYpB;AIjYgB;EACI,WAAA;EACA,kBAAA;AJmYpB;AIjYgB;EAEQ,aAAA;AJkYxB;AIpYgB;EAKQ,cAAA;AJkYxB;AIv1BA;;EA2dgB,YAAA;EACA,iBAAA;EACA,sBAAA;EACA,eAAA;EACA,kBAAA;AJgYhB;AI/XgB;;EACI,0CAAA;AJkYpB;AIn2BA;EAqegB,cAAA;AJiYhB;AIt2BA;EAueoB,eAAA;EACA,eAAA;AJkYpB;AIhYgB;EAEQ,aAAA;AJiYxB;AInYgB;EAKQ,qBAAA;AJiYxB;AI9XgB;EAEQ,aAAA;AJ+XxB;AIjYgB;EAKQ,qBAAA;AJ+XxB;AIt3BA;EA2foB,eAAA;EACA,sBAAA;EACA,aAAA;EACA,sBAAA;EACA,YAAA;EACA,WAAA;AJ8XpB;AI93BA;EAkgBwB,kBAAA;EACA,UAAA;EACA,WAAA;EACA,WAAA;EACA,gBAAA;EACA,gCAAA;AJ+XxB;AIt4BA;EAygB4B,kBAAA;EACA,SAAA;EACA,OAAA;EACA,YAAA;EACA,yBAAA;EACA,gBAAA;EACA,kBAAA;AJgY5B;AI/4BA;EAihBgC,kBAAA;EACA,MAAA;EACA,UAAA;EACA,gBAAA;EACA,mBAAA;EACA,YAAA;EACA,WAAA;EACA,kBAAA;EACA,eAAA;EACA,gCAAA;EACA,gBAAA;AJiYhC;AI55BA;EAmiBY,qBAAA;EACA,YAAA;EACA,kBAAA;AJ4XZ;AI3XY;EAEQ,cAAA;AJ4XpB;AIp6BA;EA4iBgB,kBAAA;EACA,UAAA;EACA,UAAA;EACA,aAAA;AJ2XhB;AI16BA;EAmjBY,kBAAA;EACA,qBAAA;EACA,YAAA;EACA,UAAA;AJ0XZ;AIzXY;EAEQ,cAAA;AJ0XpB;AI5XY;EAKQ,cAAA;AJ0XpB;AIt7BA;EAgkBgB,aAAA;EACA,kBAAA;EACA,YAAA;EACA,WAAA;EACA,WAAA;EACA,oBAAA;AJyXhB;AI97BA;EAwkBgB,aAAA;EACA,eAAA;EACA,WAAA;EACA,kBAAA;EACA,iCAAA;EACA,cAAA;EACA,gCAAA;EACA,gBAAA;EACA,WAAA;EACA,kBAAA;AJyXhB;AI18BA;EAolBgB,YAAA;EACA,sBAAA;EACA,eAAA;EACA,iBAAA;AJyXhB;AIxXgB;EACI,0CAAA;AJ0XpB;AIn9BA;EA8lBY,qBAAA;EACA,YAAA;AJwXZ;AIv9BA;EAkmBY,WAAA;EACA,eAAA;EACA,qBAAA;EACA,sBAAA;EACA,mBAAA;AJwXZ;AI99BA;EAymBY,WAAA;EACA,YAAA;EACA,kBAAA;EACA,YAAA;EACA,sBAAA;EACA,kBAAA;EACA,QAAA;EACA,WAAA;AJwXZ;AIx+BA;EAknBgB,aAAA;EACA,YAAA;EACA,aAAA;AJyXhB;AI7+BA;EAunBgB,qBAAA;EACA,kBAAA;EACA,iCAAA;EACA,yBAAA;EACA,YAAA;EACA,WAAA;EACA,mBAAA;EACA,sBAAA;EACA,eAAA;EACA,4BAAA;AJyXhB;AIz/BA;EAmoBgB,WAAA;EACA,kBAAA;EACA,cAAA;EACA,YAAA;EACA,WAAA;EACA,MAAA;EACA,OAAA;EACA,mBAAA;EACA,4BAAA;AJyXhB;AIpgCA;EA8oBgB,WAAA;EACA,kBAAA;EACA,cAAA;EACA,OAAA;EACA,MAAA;EACA,mBAAA;EACA,gBAAA;EACA,4BAAA;EACA,wCAAA;EACA,YAAA;EACA,WAAA;AJyXhB;AIjhCA;EA2pBgB,sCAAA;AJyXhB;AIphCA;EA8pBgB,WAAA;EACA,oCAAA;AJyXhB;AIxhCA;EAkqBgB,UAAA;AJyXhB;AInXA;EACI,aAAA;EACA,WAAA;EACA,YAAA;EACA,YAAA;EACA,6BAAA;EACA,aAAA;EACA,eAAA;EACA,sBAAA;EAEA,SAAA;EACA,YAAA;EACA,kBAAA;EACA,SAAA;EACA,QAAA;EACA,gCAAA;AJoXJ;AKpjCA;EACI,kBAAA;EACA,OAAA;EACA,QAAA;EACA,MAAA;EACA,SAAA;EACA,eAAA;EACA,WAAA;ALsjCJ;AK7jCA;EASQ,qBAAA;EACA,oBAAA;EACA,yBAAA;KAAA,sBAAA;UAAA,iBAAA;EACA,eAAA;EACA,mBAAA;EACA,iDAAA;ALujCR;AKtjCQ;EACI,kBAAA;EACA,kBAAA;ALwjCZ;AKzkCA;EAqBQ,kBAAA;EACA,QAAA;EACA,2BAAA;ALujCR;AKtjCQ;EACI,sBAAA;EACA,yBAAA;EACA,iCAAA;EACA,4BAAA;ALwjCZ;AKrjCI;EACI;IACI,2BAAA;ELujCV;AACF;AKzlCA;;EAsCQ,kBAAA;EACA,WAAA;EACA,kBAAA;EACA,kBAAA;ALujCR;AKtjCQ;;EACI,uBAAA;EACA,gCAAA;EACA,iCAAA;EACA,4BAAA;ALyjCZ;AKtjCI;EACI;IACI,mBAAA;ELwjCV;EKtjCM;IACI,mBAAA;ELwjCV;AACF;AM/mCA;EACI,oBAAA;EACA,kBAAA;EACA,UAAA;EACA,SAAA;EACA,eAAA;EACA,gBAAA;ANinCJ;AMvnCA;EAQQ,eAAA;EACA,gBAAA;EACA,gBAAA;ANknCR;AO5nCA;EACI,kBAAA;EACA,YAAA;EACA,kBAAA;EACA,kCAAA;EACA,cAAA;EACA,gBAAA;EACA,UAAA;EACA,aAAA;AP8nCJ;AO7nCI;EACI,cAAA;AP+nCR;AOzoCA;EAaQ,YAAA;EACA,sBAAA;EACA,eAAA;AP+nCR;AO9nCQ;EACI,0CAAA;APgoCZ;AOjpCA;EAqBY,eAAA;EACA,iBAAA;EACA,WAAA;EACA,eAAA;EACA,qBAAA;EACA,sBAAA;EACA,WAAA;EACA,sBAAA;EACA,mBAAA;EACA,uBAAA;EACA,gBAAA;AP+nCZ;AO9nCY;EACI,6BAAA;EAAA,qBAAA;APgoChB;AQjqCA;EACI,kBAAA;EACA,YAAA;EACA,UAAA;ARmqCJ;AQtqCA;EAMQ,kBAAA;EACA,iCAAA;EACA,gCAAA;EACA,gBAAA;EACA,WAAA;EACA,cAAA;EACA,oBAAA;EACA,0CAAA;ARmqCR;AQhrCA;EAiBQ,4CAAA;ARkqCR;AQ9pCA;EACI;IACI,UAAA;IACA,YAAA;IACA,aAAA;ERgqCN;EQ9pCE;IACI,iBAAA;IACA,eAAA;IACA,eAAA;ERgqCN;AACF;AQ7pCA;EACI;IACI,iBAAA;IACA,eAAA;IACA,eAAA;ER+pCN;EQ7pCE;IACI,eAAA;ER+pCN;EQ7pCE;IACI,YAAA;IACA,iBAAA;ER+pCN;EQ7pCE;IACI,UAAA;IACA,aAAA;IACA,YAAA;ER+pCN;AACF;ASltCA;EACI,kBAAA;EACA,YAAA;EACA,UAAA;EACA,QAAA;EACA,kBAAA;EACA,WAAA;EACA,iDAAA;EACA,eAAA;ATotCJ;ASntCI;EACI,aAAA;ATqtCR;AU/tCA;EACI,kBAAA;EACA,MAAA;EACA,SAAA;EACA,OAAA;EACA,QAAA;EACA,UAAA;EACA,aAAA;AViuCJ;AUhuCI;EACI,cAAA;AVkuCR;AU9tCA;EACI,kBAAA;EACA,gBAAA;EACA,YAAA;EACA,WAAA;EACA,YAAA;AVguCJ;AUruCA;EAOQ,WAAA;EACA,YAAA;EACA,aAAA;AViuCR;AU1uCA;EAYQ,cAAA;AViuCR;AU7uCA;EAeQ,aAAA;AViuCR;AW7vCA;EACI,kBAAA;EACA,SAAA;EACA,UAAA;EACA,YAAA;EACA,iCAAA;EACA,aAAA;EACA,WAAA;EACA,eAAA;EACA,kBAAA;AX+vCJ;AW7vCI;EACI,aAAA;AX+vCR;AW3wCA;EAgBQ,eAAA;EACA,kBAAA;EACA,WAAA;EACA,SAAA;AX8vCR;AW1vCQ;EACI,qBAAA;EACA,sBAAA;EACA,iBAAA;EACA,mBAAA;EACA,uBAAA;EACA,gBAAA;AX4vCZ;AWzxCA;EAkCQ,YAAA;EACA,iBAAA;EACA,kBAAA;AX0vCR;AW9xCA;EAwCQ,YAAA;AXyvCR",
            sourcesContent: [
              "@keyframes my-face {\n    2% {\n        transform: translate(0, 1.5px) rotate(1.5deg);\n    }\n    4% {\n        transform: translate(0, -1.5px) rotate(-0.5deg);\n    }\n    6% {\n        transform: translate(0, 1.5px) rotate(-1.5deg);\n    }\n    8% {\n        transform: translate(0, -1.5px) rotate(-1.5deg);\n    }\n    10% {\n        transform: translate(0, 2.5px) rotate(1.5deg);\n    }\n    12% {\n        transform: translate(0, -0.5px) rotate(1.5deg);\n    }\n    14% {\n        transform: translate(0, -1.5px) rotate(1.5deg);\n    }\n    16% {\n        transform: translate(0, -0.5px) rotate(-1.5deg);\n    }\n    18% {\n        transform: translate(0, 0.5px) rotate(-1.5deg);\n    }\n    20% {\n        transform: translate(0, -1.5px) rotate(2.5deg);\n    }\n    22% {\n        transform: translate(0, 0.5px) rotate(-1.5deg);\n    }\n    24% {\n        transform: translate(0, 1.5px) rotate(1.5deg);\n    }\n    26% {\n        transform: translate(0, 0.5px) rotate(0.5deg);\n    }\n    28% {\n        transform: translate(0, 0.5px) rotate(1.5deg);\n    }\n    30% {\n        transform: translate(0, -0.5px) rotate(2.5deg);\n    }\n    32% {\n        transform: translate(0, 1.5px) rotate(-0.5deg);\n    }\n    34% {\n        transform: translate(0, 1.5px) rotate(-0.5deg);\n    }\n    36% {\n        transform: translate(0, -1.5px) rotate(2.5deg);\n    }\n    38% {\n        transform: translate(0, 1.5px) rotate(-1.5deg);\n    }\n    40% {\n        transform: translate(0, -0.5px) rotate(2.5deg);\n    }\n    42% {\n        transform: translate(0, 2.5px) rotate(-1.5deg);\n    }\n    44% {\n        transform: translate(0, 1.5px) rotate(0.5deg);\n    }\n    46% {\n        transform: translate(0, -1.5px) rotate(2.5deg);\n    }\n    48% {\n        transform: translate(0, -0.5px) rotate(0.5deg);\n    }\n    50% {\n        transform: translate(0, 0.5px) rotate(0.5deg);\n    }\n    52% {\n        transform: translate(0, 2.5px) rotate(2.5deg);\n    }\n    54% {\n        transform: translate(0, -1.5px) rotate(1.5deg);\n    }\n    56% {\n        transform: translate(0, 2.5px) rotate(2.5deg);\n    }\n    58% {\n        transform: translate(0, 0.5px) rotate(2.5deg);\n    }\n    60% {\n        transform: translate(0, 2.5px) rotate(2.5deg);\n    }\n    62% {\n        transform: translate(0, -0.5px) rotate(2.5deg);\n    }\n    64% {\n        transform: translate(0, -0.5px) rotate(1.5deg);\n    }\n    66% {\n        transform: translate(0, 1.5px) rotate(-0.5deg);\n    }\n    68% {\n        transform: translate(0, -1.5px) rotate(-0.5deg);\n    }\n    70% {\n        transform: translate(0, 1.5px) rotate(0.5deg);\n    }\n    72% {\n        transform: translate(0, 2.5px) rotate(1.5deg);\n    }\n    74% {\n        transform: translate(0, -0.5px) rotate(0.5deg);\n    }\n    76% {\n        transform: translate(0, -0.5px) rotate(2.5deg);\n    }\n    78% {\n        transform: translate(0, -0.5px) rotate(1.5deg);\n    }\n    80% {\n        transform: translate(0, 1.5px) rotate(1.5deg);\n    }\n    82% {\n        transform: translate(0, -0.5px) rotate(0.5deg);\n    }\n    84% {\n        transform: translate(0, 1.5px) rotate(2.5deg);\n    }\n    86% {\n        transform: translate(0, -1.5px) rotate(-1.5deg);\n    }\n    88% {\n        transform: translate(0, -0.5px) rotate(2.5deg);\n    }\n    90% {\n        transform: translate(0, 2.5px) rotate(-0.5deg);\n    }\n    92% {\n        transform: translate(0, 0.5px) rotate(-0.5deg);\n    }\n    94% {\n        transform: translate(0, 2.5px) rotate(0.5deg);\n    }\n    96% {\n        transform: translate(0, -0.5px) rotate(1.5deg);\n    }\n    98% {\n        transform: translate(0, -1.5px) rotate(-0.5deg);\n    }\n    0%,\n    100% {\n        transform: translate(0, 0) rotate(0deg);\n    }\n}",
              "@import '../../node_modules/balloon-css/balloon.css';\n@keyframes my-face {\n  2% {\n    transform: translate(0, 1.5px) rotate(1.5deg);\n  }\n  4% {\n    transform: translate(0, -1.5px) rotate(-0.5deg);\n  }\n  6% {\n    transform: translate(0, 1.5px) rotate(-1.5deg);\n  }\n  8% {\n    transform: translate(0, -1.5px) rotate(-1.5deg);\n  }\n  10% {\n    transform: translate(0, 2.5px) rotate(1.5deg);\n  }\n  12% {\n    transform: translate(0, -0.5px) rotate(1.5deg);\n  }\n  14% {\n    transform: translate(0, -1.5px) rotate(1.5deg);\n  }\n  16% {\n    transform: translate(0, -0.5px) rotate(-1.5deg);\n  }\n  18% {\n    transform: translate(0, 0.5px) rotate(-1.5deg);\n  }\n  20% {\n    transform: translate(0, -1.5px) rotate(2.5deg);\n  }\n  22% {\n    transform: translate(0, 0.5px) rotate(-1.5deg);\n  }\n  24% {\n    transform: translate(0, 1.5px) rotate(1.5deg);\n  }\n  26% {\n    transform: translate(0, 0.5px) rotate(0.5deg);\n  }\n  28% {\n    transform: translate(0, 0.5px) rotate(1.5deg);\n  }\n  30% {\n    transform: translate(0, -0.5px) rotate(2.5deg);\n  }\n  32% {\n    transform: translate(0, 1.5px) rotate(-0.5deg);\n  }\n  34% {\n    transform: translate(0, 1.5px) rotate(-0.5deg);\n  }\n  36% {\n    transform: translate(0, -1.5px) rotate(2.5deg);\n  }\n  38% {\n    transform: translate(0, 1.5px) rotate(-1.5deg);\n  }\n  40% {\n    transform: translate(0, -0.5px) rotate(2.5deg);\n  }\n  42% {\n    transform: translate(0, 2.5px) rotate(-1.5deg);\n  }\n  44% {\n    transform: translate(0, 1.5px) rotate(0.5deg);\n  }\n  46% {\n    transform: translate(0, -1.5px) rotate(2.5deg);\n  }\n  48% {\n    transform: translate(0, -0.5px) rotate(0.5deg);\n  }\n  50% {\n    transform: translate(0, 0.5px) rotate(0.5deg);\n  }\n  52% {\n    transform: translate(0, 2.5px) rotate(2.5deg);\n  }\n  54% {\n    transform: translate(0, -1.5px) rotate(1.5deg);\n  }\n  56% {\n    transform: translate(0, 2.5px) rotate(2.5deg);\n  }\n  58% {\n    transform: translate(0, 0.5px) rotate(2.5deg);\n  }\n  60% {\n    transform: translate(0, 2.5px) rotate(2.5deg);\n  }\n  62% {\n    transform: translate(0, -0.5px) rotate(2.5deg);\n  }\n  64% {\n    transform: translate(0, -0.5px) rotate(1.5deg);\n  }\n  66% {\n    transform: translate(0, 1.5px) rotate(-0.5deg);\n  }\n  68% {\n    transform: translate(0, -1.5px) rotate(-0.5deg);\n  }\n  70% {\n    transform: translate(0, 1.5px) rotate(0.5deg);\n  }\n  72% {\n    transform: translate(0, 2.5px) rotate(1.5deg);\n  }\n  74% {\n    transform: translate(0, -0.5px) rotate(0.5deg);\n  }\n  76% {\n    transform: translate(0, -0.5px) rotate(2.5deg);\n  }\n  78% {\n    transform: translate(0, -0.5px) rotate(1.5deg);\n  }\n  80% {\n    transform: translate(0, 1.5px) rotate(1.5deg);\n  }\n  82% {\n    transform: translate(0, -0.5px) rotate(0.5deg);\n  }\n  84% {\n    transform: translate(0, 1.5px) rotate(2.5deg);\n  }\n  86% {\n    transform: translate(0, -1.5px) rotate(-1.5deg);\n  }\n  88% {\n    transform: translate(0, -0.5px) rotate(2.5deg);\n  }\n  90% {\n    transform: translate(0, 2.5px) rotate(-0.5deg);\n  }\n  92% {\n    transform: translate(0, 0.5px) rotate(-0.5deg);\n  }\n  94% {\n    transform: translate(0, 2.5px) rotate(0.5deg);\n  }\n  96% {\n    transform: translate(0, -0.5px) rotate(1.5deg);\n  }\n  98% {\n    transform: translate(0, -1.5px) rotate(-0.5deg);\n  }\n  0%,\n  100% {\n    transform: translate(0, 0) rotate(0deg);\n  }\n}\n.dplayer {\n  position: relative;\n  overflow: hidden;\n  user-select: none;\n  line-height: 1;\n}\n.dplayer * {\n  box-sizing: content-box;\n}\n.dplayer svg {\n  width: 100%;\n  height: 100%;\n}\n.dplayer svg path,\n.dplayer svg circle {\n  fill: #fff;\n}\n.dplayer:-webkit-full-screen {\n  width: 100%;\n  height: 100%;\n  background: #000;\n  position: fixed;\n  z-index: 100000;\n  left: 0;\n  top: 0;\n  margin: 0;\n  padding: 0;\n  transform: translate(0, 0);\n}\n.dplayer.dplayer-no-danmaku .dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-box .dplayer-setting-showdan,\n.dplayer.dplayer-no-danmaku .dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-box .dplayer-setting-danmaku,\n.dplayer.dplayer-no-danmaku .dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-box .dplayer-setting-danunlimit {\n  display: none;\n}\n.dplayer.dplayer-no-danmaku .dplayer-controller .dplayer-icons .dplayer-comment {\n  display: none;\n}\n.dplayer.dplayer-no-danmaku .dplayer-danmaku {\n  display: none;\n}\n.dplayer.dplayer-live .dplayer-time {\n  display: none;\n}\n.dplayer.dplayer-live .dplayer-bar-wrap {\n  display: none;\n}\n.dplayer.dplayer-live .dplayer-setting-speed {\n  display: none;\n}\n.dplayer.dplayer-live .dplayer-setting-loop {\n  display: none;\n}\n.dplayer.dplayer-live.dplayer-no-danmaku .dplayer-setting {\n  display: none;\n}\n.dplayer.dplayer-arrow .dplayer-danmaku {\n  font-size: 18px;\n}\n.dplayer.dplayer-arrow .dplayer-icon {\n  margin: 0 -3px;\n}\n.dplayer.dplayer-playing .dplayer-danmaku .dplayer-danmaku-move {\n  animation-play-state: running;\n}\n@media (min-width: 900px) {\n  .dplayer.dplayer-playing .dplayer-controller-mask {\n    opacity: 0;\n  }\n  .dplayer.dplayer-playing .dplayer-controller {\n    opacity: 0;\n  }\n  .dplayer.dplayer-playing:hover .dplayer-controller-mask {\n    opacity: 1;\n  }\n  .dplayer.dplayer-playing:hover .dplayer-controller {\n    opacity: 1;\n  }\n}\n.dplayer.dplayer-loading .dplayer-bezel .diplayer-loading-icon {\n  display: block;\n}\n.dplayer.dplayer-loading .dplayer-danmaku,\n.dplayer.dplayer-paused .dplayer-danmaku,\n.dplayer.dplayer-loading .dplayer-danmaku-move,\n.dplayer.dplayer-paused .dplayer-danmaku-move {\n  animation-play-state: paused;\n}\n.dplayer.dplayer-hide-controller {\n  cursor: none;\n}\n.dplayer.dplayer-hide-controller .dplayer-controller-mask {\n  opacity: 0;\n  transform: translateY(100%);\n}\n.dplayer.dplayer-hide-controller .dplayer-controller {\n  opacity: 0;\n  transform: translateY(100%);\n}\n.dplayer.dplayer-show-controller .dplayer-controller-mask {\n  opacity: 1;\n}\n.dplayer.dplayer-show-controller .dplayer-controller {\n  opacity: 1;\n}\n.dplayer.dplayer-fulled {\n  position: fixed;\n  z-index: 100000;\n  left: 0;\n  top: 0;\n  width: 100% !important;\n  height: 100% !important;\n}\n.dplayer.dplayer-mobile .dplayer-controller .dplayer-icons .dplayer-volume,\n.dplayer.dplayer-mobile .dplayer-controller .dplayer-icons .dplayer-camera-icon,\n.dplayer.dplayer-mobile .dplayer-controller .dplayer-icons .dplayer-airplay-icon,\n.dplayer.dplayer-mobile .dplayer-controller .dplayer-icons .dplayer-chromecast-icon,\n.dplayer.dplayer-mobile .dplayer-controller .dplayer-icons .dplayer-play-icon {\n  display: none;\n}\n.dplayer.dplayer-mobile .dplayer-controller .dplayer-icons .dplayer-full .dplayer-full-in-icon {\n  position: static;\n  display: inline-block;\n}\n.dplayer.dplayer-mobile .dplayer-bar-time {\n  display: none;\n}\n.dplayer.dplayer-mobile.dplayer-hide-controller .dplayer-mobile-play {\n  display: none;\n}\n.dplayer.dplayer-mobile .dplayer-mobile-play {\n  display: block;\n}\n.dplayer-web-fullscreen-fix {\n  position: fixed;\n  top: 0;\n  left: 0;\n  margin: 0;\n  padding: 0;\n}\n[data-balloon]:before {\n  display: none;\n}\n[data-balloon]:after {\n  padding: 0.3em 0.7em;\n  background: rgba(17, 17, 17, 0.7);\n}\n[data-balloon][data-balloon-pos=\"up\"]:after {\n  margin-bottom: 0;\n}\n.dplayer-bezel {\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: 0;\n  bottom: 0;\n  font-size: 22px;\n  color: #fff;\n  pointer-events: none;\n}\n.dplayer-bezel .dplayer-bezel-icon {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  margin: -26px 0 0 -26px;\n  height: 52px;\n  width: 52px;\n  padding: 12px;\n  box-sizing: border-box;\n  background: rgba(0, 0, 0, 0.5);\n  border-radius: 50%;\n  opacity: 0;\n  pointer-events: none;\n}\n.dplayer-bezel .dplayer-bezel-icon.dplayer-bezel-transition {\n  animation: bezel-hide 0.5s linear;\n}\n@keyframes bezel-hide {\n  from {\n    opacity: 1;\n    transform: scale(1);\n  }\n  to {\n    opacity: 0;\n    transform: scale(2);\n  }\n}\n.dplayer-bezel .dplayer-danloading {\n  position: absolute;\n  top: 50%;\n  margin-top: -7px;\n  width: 100%;\n  text-align: center;\n  font-size: 14px;\n  line-height: 14px;\n  animation: my-face 5s infinite ease-in-out;\n}\n.dplayer-bezel .diplayer-loading-icon {\n  display: none;\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  margin: -18px 0 0 -18px;\n  height: 36px;\n  width: 36px;\n  pointer-events: none;\n}\n.dplayer-bezel .diplayer-loading-icon .diplayer-loading-hide {\n  display: none;\n}\n.dplayer-bezel .diplayer-loading-icon .diplayer-loading-dot {\n  animation: diplayer-loading-dot-fade 0.8s ease infinite;\n  opacity: 0;\n  transform-origin: 4px 4px;\n}\n.dplayer-bezel .diplayer-loading-icon .diplayer-loading-dot.diplayer-loading-dot-1 {\n  animation-delay: 0.1s;\n}\n.dplayer-bezel .diplayer-loading-icon .diplayer-loading-dot.diplayer-loading-dot-2 {\n  animation-delay: 0.2s;\n}\n.dplayer-bezel .diplayer-loading-icon .diplayer-loading-dot.diplayer-loading-dot-3 {\n  animation-delay: 0.3s;\n}\n.dplayer-bezel .diplayer-loading-icon .diplayer-loading-dot.diplayer-loading-dot-4 {\n  animation-delay: 0.4s;\n}\n.dplayer-bezel .diplayer-loading-icon .diplayer-loading-dot.diplayer-loading-dot-5 {\n  animation-delay: 0.5s;\n}\n.dplayer-bezel .diplayer-loading-icon .diplayer-loading-dot.diplayer-loading-dot-6 {\n  animation-delay: 0.6s;\n}\n.dplayer-bezel .diplayer-loading-icon .diplayer-loading-dot.diplayer-loading-dot-7 {\n  animation-delay: 0.7s;\n}\n@keyframes diplayer-loading-dot-fade {\n  0% {\n    opacity: 0.7;\n    transform: scale(1.2, 1.2);\n  }\n  50% {\n    opacity: 0.25;\n    transform: scale(0.9, 0.9);\n  }\n  to {\n    opacity: 0.25;\n    transform: scale(0.85, 0.85);\n  }\n}\n.dplayer-controller-mask {\n  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAADGCAYAAAAT+OqFAAAAdklEQVQoz42QQQ7AIAgEF/T/D+kbq/RWAlnQyyazA4aoAB4FsBSA/bFjuF1EOL7VbrIrBuusmrt4ZZORfb6ehbWdnRHEIiITaEUKa5EJqUakRSaEYBJSCY2dEstQY7AuxahwXFrvZmWl2rh4JZ07z9dLtesfNj5q0FU3A5ObbwAAAABJRU5ErkJggg==) repeat-x bottom;\n  height: 98px;\n  width: 100%;\n  position: absolute;\n  bottom: 0;\n  transition: all 0.3s ease;\n}\n.dplayer-controller {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 41px;\n  padding: 0 20px;\n  user-select: none;\n  transition: all 0.3s ease;\n}\n.dplayer-controller.dplayer-controller-comment .dplayer-icons {\n  display: none;\n}\n.dplayer-controller.dplayer-controller-comment .dplayer-icons.dplayer-comment-box {\n  display: block;\n}\n.dplayer-controller .dplayer-bar-wrap {\n  padding: 5px 0;\n  cursor: pointer;\n  position: absolute;\n  bottom: 33px;\n  width: calc(100% - 40px);\n  height: 3px;\n}\n.dplayer-controller .dplayer-bar-wrap:hover .dplayer-bar .dplayer-played .dplayer-thumb {\n  transform: scale(1);\n}\n.dplayer-controller .dplayer-bar-wrap:hover .dplayer-highlight {\n  display: block;\n  width: 8px;\n  transform: translateX(-4px);\n  top: 4px;\n  height: 40%;\n}\n.dplayer-controller .dplayer-bar-wrap .dplayer-highlight {\n  z-index: 12;\n  position: absolute;\n  top: 5px;\n  width: 6px;\n  height: 20%;\n  border-radius: 6px;\n  background-color: #fff;\n  text-align: center;\n  transform: translateX(-3px);\n  transition: all 0.2s ease-in-out;\n}\n.dplayer-controller .dplayer-bar-wrap .dplayer-highlight:hover .dplayer-highlight-text {\n  display: block;\n}\n.dplayer-controller .dplayer-bar-wrap .dplayer-highlight:hover ~ .dplayer-bar-preview {\n  opacity: 0;\n}\n.dplayer-controller .dplayer-bar-wrap .dplayer-highlight:hover ~ .dplayer-bar-time {\n  opacity: 0;\n}\n.dplayer-controller .dplayer-bar-wrap .dplayer-highlight .dplayer-highlight-text {\n  display: none;\n  position: absolute;\n  left: 50%;\n  top: -24px;\n  padding: 5px 8px;\n  background-color: rgba(0, 0, 0, 0.62);\n  color: #fff;\n  border-radius: 4px;\n  font-size: 12px;\n  white-space: nowrap;\n  transform: translateX(-50%);\n}\n.dplayer-controller .dplayer-bar-wrap .dplayer-bar-preview {\n  position: absolute;\n  background: #fff;\n  pointer-events: none;\n  display: none;\n  background-size: 16000px 100%;\n}\n.dplayer-controller .dplayer-bar-wrap .dplayer-bar-preview-canvas {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  z-index: 1;\n  pointer-events: none;\n}\n.dplayer-controller .dplayer-bar-wrap .dplayer-bar-time {\n  position: absolute;\n  left: 0px;\n  top: -20px;\n  border-radius: 4px;\n  padding: 5px 7px;\n  background-color: rgba(0, 0, 0, 0.62);\n  color: #fff;\n  font-size: 12px;\n  text-align: center;\n  opacity: 1;\n  transition: opacity 0.1s ease-in-out;\n  word-wrap: normal;\n  word-break: normal;\n  z-index: 2;\n  pointer-events: none;\n}\n.dplayer-controller .dplayer-bar-wrap .dplayer-bar-time.hidden {\n  opacity: 0;\n}\n.dplayer-controller .dplayer-bar-wrap .dplayer-bar {\n  position: relative;\n  height: 3px;\n  width: 100%;\n  background: rgba(255, 255, 255, 0.2);\n  cursor: pointer;\n}\n.dplayer-controller .dplayer-bar-wrap .dplayer-bar .dplayer-loaded {\n  position: absolute;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  background: rgba(255, 255, 255, 0.4);\n  height: 3px;\n  transition: all 0.5s ease;\n  will-change: width;\n}\n.dplayer-controller .dplayer-bar-wrap .dplayer-bar .dplayer-played {\n  position: absolute;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  height: 3px;\n  will-change: width;\n}\n.dplayer-controller .dplayer-bar-wrap .dplayer-bar .dplayer-played .dplayer-thumb {\n  position: absolute;\n  top: 0;\n  right: 5px;\n  margin-top: -4px;\n  margin-right: -10px;\n  height: 11px;\n  width: 11px;\n  border-radius: 50%;\n  cursor: pointer;\n  transition: all 0.3s ease-in-out;\n  transform: scale(0);\n}\n.dplayer-controller .dplayer-icons {\n  height: 38px;\n  position: absolute;\n  bottom: 0;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box {\n  display: none;\n  position: absolute;\n  transition: all 0.3s ease-in-out;\n  z-index: 2;\n  height: 38px;\n  bottom: 0;\n  left: 20px;\n  right: 20px;\n  color: #fff;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-icon {\n  padding: 7px;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-setting-icon {\n  position: absolute;\n  left: 0;\n  top: 0;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-send-icon {\n  position: absolute;\n  right: 0;\n  top: 0;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-setting-box {\n  position: absolute;\n  background: rgba(28, 28, 28, 0.9);\n  bottom: 41px;\n  left: 0;\n  box-shadow: 0 0 25px rgba(0, 0, 0, 0.3);\n  border-radius: 4px;\n  padding: 10px 10px 16px;\n  font-size: 14px;\n  width: 204px;\n  transition: all 0.3s ease-in-out;\n  transform: scale(0);\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-setting-box.dplayer-comment-setting-open {\n  transform: scale(1);\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-setting-box input[type=radio] {\n  display: none;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-setting-box label {\n  cursor: pointer;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-setting-box .dplayer-comment-setting-title {\n  font-size: 13px;\n  color: #fff;\n  line-height: 30px;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-setting-box .dplayer-comment-setting-type {\n  font-size: 0;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-setting-box .dplayer-comment-setting-type .dplayer-comment-setting-title {\n  margin-bottom: 6px;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-setting-box .dplayer-comment-setting-type label:nth-child(2) span {\n  border-radius: 4px 0 0 4px;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-setting-box .dplayer-comment-setting-type label:nth-child(4) span {\n  border-radius: 0 4px 4px 0;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-setting-box .dplayer-comment-setting-type span {\n  width: 33%;\n  padding: 4px 6px;\n  line-height: 16px;\n  display: inline-block;\n  font-size: 12px;\n  color: #fff;\n  border: 1px solid #fff;\n  margin-right: -1px;\n  box-sizing: border-box;\n  text-align: center;\n  cursor: pointer;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-setting-box .dplayer-comment-setting-type input:checked + span {\n  background: #E4E4E6;\n  color: #1c1c1c;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-setting-box .dplayer-comment-setting-color {\n  font-size: 0;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-setting-box .dplayer-comment-setting-color label {\n  font-size: 0;\n  padding: 6px;\n  display: inline-block;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-setting-box .dplayer-comment-setting-color span {\n  width: 22px;\n  height: 22px;\n  display: inline-block;\n  border-radius: 50%;\n  box-sizing: border-box;\n  cursor: pointer;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-setting-box .dplayer-comment-setting-color span:hover {\n  animation: my-face 5s infinite ease-in-out;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-input {\n  outline: none;\n  border: none;\n  padding: 8px 31px;\n  font-size: 14px;\n  line-height: 18px;\n  text-align: center;\n  border-radius: 4px;\n  background: none;\n  margin: 0;\n  height: 100%;\n  box-sizing: border-box;\n  width: 100%;\n  color: #fff;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-input::placeholder {\n  color: #fff;\n  opacity: 0.8;\n}\n.dplayer-controller .dplayer-icons.dplayer-comment-box .dplayer-comment-input::-ms-clear {\n  display: none;\n}\n.dplayer-controller .dplayer-icons.dplayer-icons-left .dplayer-icon {\n  padding: 7px;\n}\n.dplayer-controller .dplayer-icons.dplayer-icons-right {\n  right: 20px;\n}\n.dplayer-controller .dplayer-icons.dplayer-icons-right .dplayer-icon {\n  padding: 8px;\n}\n.dplayer-controller .dplayer-icons .dplayer-time,\n.dplayer-controller .dplayer-icons .dplayer-live-badge {\n  line-height: 38px;\n  color: #eee;\n  text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);\n  vertical-align: middle;\n  font-size: 13px;\n  cursor: default;\n}\n.dplayer-controller .dplayer-icons .dplayer-live-dot {\n  display: inline-block;\n  width: 6px;\n  height: 6px;\n  vertical-align: 4%;\n  margin-right: 5px;\n  content: '';\n  border-radius: 6px;\n}\n.dplayer-controller .dplayer-icons .dplayer-icon {\n  width: 40px;\n  height: 100%;\n  border: none;\n  background-color: transparent;\n  outline: none;\n  cursor: pointer;\n  vertical-align: middle;\n  box-sizing: border-box;\n  display: inline-block;\n}\n.dplayer-controller .dplayer-icons .dplayer-icon .dplayer-icon-content {\n  transition: all 0.2s ease-in-out;\n  opacity: 0.8;\n}\n.dplayer-controller .dplayer-icons .dplayer-icon:hover .dplayer-icon-content {\n  opacity: 1;\n}\n.dplayer-controller .dplayer-icons .dplayer-icon.dplayer-quality-icon {\n  color: #fff;\n  width: auto;\n  line-height: 22px;\n  font-size: 14px;\n}\n.dplayer-controller .dplayer-icons .dplayer-icon.dplayer-comment-icon {\n  padding: 10px 9px 9px;\n}\n.dplayer-controller .dplayer-icons .dplayer-icon.dplayer-setting-icon {\n  padding-top: 8.5px;\n}\n.dplayer-controller .dplayer-icons .dplayer-icon.dplayer-volume-icon {\n  width: 43px;\n}\n.dplayer-controller .dplayer-icons .dplayer-volume {\n  position: relative;\n  display: inline-block;\n  cursor: pointer;\n  height: 100%;\n}\n.dplayer-controller .dplayer-icons .dplayer-volume:hover .dplayer-volume-bar-wrap .dplayer-volume-bar {\n  width: 45px;\n}\n.dplayer-controller .dplayer-icons .dplayer-volume:hover .dplayer-volume-bar-wrap .dplayer-volume-bar .dplayer-volume-bar-inner .dplayer-thumb {\n  transform: scale(1);\n}\n.dplayer-controller .dplayer-icons .dplayer-volume.dplayer-volume-active .dplayer-volume-bar-wrap .dplayer-volume-bar {\n  width: 45px;\n}\n.dplayer-controller .dplayer-icons .dplayer-volume.dplayer-volume-active .dplayer-volume-bar-wrap .dplayer-volume-bar .dplayer-volume-bar-inner .dplayer-thumb {\n  transform: scale(1);\n}\n.dplayer-controller .dplayer-icons .dplayer-volume .dplayer-volume-bar-wrap {\n  display: inline-block;\n  margin: 0 10px 0 -5px;\n  vertical-align: middle;\n  height: 100%;\n}\n.dplayer-controller .dplayer-icons .dplayer-volume .dplayer-volume-bar-wrap .dplayer-volume-bar {\n  position: relative;\n  top: 17px;\n  width: 0;\n  height: 3px;\n  background: #aaa;\n  transition: all 0.3s ease-in-out;\n}\n.dplayer-controller .dplayer-icons .dplayer-volume .dplayer-volume-bar-wrap .dplayer-volume-bar .dplayer-volume-bar-inner {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  height: 100%;\n  transition: all 0.1s ease;\n  will-change: width;\n}\n.dplayer-controller .dplayer-icons .dplayer-volume .dplayer-volume-bar-wrap .dplayer-volume-bar .dplayer-volume-bar-inner .dplayer-thumb {\n  position: absolute;\n  top: 0;\n  right: 5px;\n  margin-top: -4px;\n  margin-right: -10px;\n  height: 11px;\n  width: 11px;\n  border-radius: 50%;\n  cursor: pointer;\n  transition: all 0.3s ease-in-out;\n  transform: scale(0);\n}\n.dplayer-controller .dplayer-icons .dplayer-subtitle-btn {\n  display: inline-block;\n  height: 100%;\n}\n.dplayer-controller .dplayer-icons .dplayer-subtitles {\n  display: inline-block;\n  height: 100%;\n}\n.dplayer-controller .dplayer-icons .dplayer-subtitles .dplayer-subtitles-box {\n  position: absolute;\n  right: 0;\n  bottom: 50px;\n  transform: scale(0);\n  width: fit-content;\n  max-width: 240px;\n  min-width: 120px;\n  border-radius: 2px;\n  background: rgba(28, 28, 28, 0.9);\n  padding: 7px 0;\n  transition: all 0.3s ease-in-out;\n  overflow: auto;\n  z-index: 2;\n}\n.dplayer-controller .dplayer-icons .dplayer-subtitles .dplayer-subtitles-box.dplayer-subtitles-panel {\n  display: block;\n}\n.dplayer-controller .dplayer-icons .dplayer-subtitles .dplayer-subtitles-box.dplayer-subtitles-box-open {\n  transform: scale(1);\n}\n.dplayer-controller .dplayer-icons .dplayer-subtitles .dplayer-subtitles-item {\n  height: 30px;\n  padding: 5px 10px;\n  box-sizing: border-box;\n  cursor: pointer;\n  position: relative;\n}\n.dplayer-controller .dplayer-icons .dplayer-subtitles .dplayer-subtitles-item:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n.dplayer-controller .dplayer-icons .dplayer-setting {\n  display: inline-block;\n  height: 100%;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-box {\n  position: absolute;\n  right: 0;\n  bottom: 50px;\n  transform: scale(0);\n  width: 150px;\n  border-radius: 2px;\n  background: rgba(28, 28, 28, 0.9);\n  padding: 7px 0;\n  transition: all 0.3s ease-in-out;\n  overflow: hidden;\n  z-index: 2;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-box > div {\n  display: none;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-box > div.dplayer-setting-origin-panel {\n  display: block;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-box.dplayer-setting-box-open {\n  transform: scale(1);\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-box.dplayer-setting-box-narrow {\n  width: 70px;\n  text-align: center;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-box.dplayer-setting-box-speed .dplayer-setting-origin-panel {\n  display: none;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-box.dplayer-setting-box-speed .dplayer-setting-speed-panel {\n  display: block;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-item,\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-speed-item {\n  height: 30px;\n  padding: 5px 10px;\n  box-sizing: border-box;\n  cursor: pointer;\n  position: relative;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-item:hover,\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-speed-item:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-danmaku {\n  padding: 5px 0;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-danmaku .dplayer-label {\n  padding: 0 10px;\n  display: inline;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-danmaku:hover .dplayer-label {\n  display: none;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-danmaku:hover .dplayer-danmaku-bar-wrap {\n  display: inline-block;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-danmaku.dplayer-setting-danmaku-active .dplayer-label {\n  display: none;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-danmaku.dplayer-setting-danmaku-active .dplayer-danmaku-bar-wrap {\n  display: inline-block;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-danmaku .dplayer-danmaku-bar-wrap {\n  padding: 0 10px;\n  box-sizing: border-box;\n  display: none;\n  vertical-align: middle;\n  height: 100%;\n  width: 100%;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-danmaku .dplayer-danmaku-bar-wrap .dplayer-danmaku-bar {\n  position: relative;\n  top: 8.5px;\n  width: 100%;\n  height: 3px;\n  background: #fff;\n  transition: all 0.3s ease-in-out;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-danmaku .dplayer-danmaku-bar-wrap .dplayer-danmaku-bar .dplayer-danmaku-bar-inner {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  height: 100%;\n  transition: all 0.1s ease;\n  background: #aaa;\n  will-change: width;\n}\n.dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-danmaku .dplayer-danmaku-bar-wrap .dplayer-danmaku-bar .dplayer-danmaku-bar-inner .dplayer-thumb {\n  position: absolute;\n  top: 0;\n  right: 5px;\n  margin-top: -4px;\n  margin-right: -10px;\n  height: 11px;\n  width: 11px;\n  border-radius: 50%;\n  cursor: pointer;\n  transition: all 0.3s ease-in-out;\n  background: #aaa;\n}\n.dplayer-controller .dplayer-icons .dplayer-full {\n  display: inline-block;\n  height: 100%;\n  position: relative;\n}\n.dplayer-controller .dplayer-icons .dplayer-full:hover .dplayer-full-in-icon {\n  display: block;\n}\n.dplayer-controller .dplayer-icons .dplayer-full .dplayer-full-in-icon {\n  position: absolute;\n  top: -30px;\n  z-index: 1;\n  display: none;\n}\n.dplayer-controller .dplayer-icons .dplayer-quality {\n  position: relative;\n  display: inline-block;\n  height: 100%;\n  z-index: 2;\n}\n.dplayer-controller .dplayer-icons .dplayer-quality:hover .dplayer-quality-list {\n  display: block;\n}\n.dplayer-controller .dplayer-icons .dplayer-quality:hover .dplayer-quality-mask {\n  display: block;\n}\n.dplayer-controller .dplayer-icons .dplayer-quality .dplayer-quality-mask {\n  display: none;\n  position: absolute;\n  bottom: 38px;\n  left: -18px;\n  width: 80px;\n  padding-bottom: 12px;\n}\n.dplayer-controller .dplayer-icons .dplayer-quality .dplayer-quality-list {\n  display: none;\n  font-size: 12px;\n  width: 80px;\n  border-radius: 2px;\n  background: rgba(28, 28, 28, 0.9);\n  padding: 5px 0;\n  transition: all 0.3s ease-in-out;\n  overflow: hidden;\n  color: #fff;\n  text-align: center;\n}\n.dplayer-controller .dplayer-icons .dplayer-quality .dplayer-quality-item {\n  height: 25px;\n  box-sizing: border-box;\n  cursor: pointer;\n  line-height: 25px;\n}\n.dplayer-controller .dplayer-icons .dplayer-quality .dplayer-quality-item:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n.dplayer-controller .dplayer-icons .dplayer-comment {\n  display: inline-block;\n  height: 100%;\n}\n.dplayer-controller .dplayer-icons .dplayer-label {\n  color: #eee;\n  font-size: 13px;\n  display: inline-block;\n  vertical-align: middle;\n  white-space: nowrap;\n}\n.dplayer-controller .dplayer-icons .dplayer-toggle {\n  width: 32px;\n  height: 20px;\n  text-align: center;\n  font-size: 0;\n  vertical-align: middle;\n  position: absolute;\n  top: 5px;\n  right: 10px;\n}\n.dplayer-controller .dplayer-icons .dplayer-toggle input {\n  max-height: 0;\n  max-width: 0;\n  display: none;\n}\n.dplayer-controller .dplayer-icons .dplayer-toggle input + label {\n  display: inline-block;\n  position: relative;\n  box-shadow: #dfdfdf 0 0 0 0 inset;\n  border: 1px solid #dfdfdf;\n  height: 20px;\n  width: 32px;\n  border-radius: 10px;\n  box-sizing: border-box;\n  cursor: pointer;\n  transition: 0.2s ease-in-out;\n}\n.dplayer-controller .dplayer-icons .dplayer-toggle input + label:before {\n  content: \"\";\n  position: absolute;\n  display: block;\n  height: 18px;\n  width: 18px;\n  top: 0;\n  left: 0;\n  border-radius: 15px;\n  transition: 0.2s ease-in-out;\n}\n.dplayer-controller .dplayer-icons .dplayer-toggle input + label:after {\n  content: \"\";\n  position: absolute;\n  display: block;\n  left: 0;\n  top: 0;\n  border-radius: 15px;\n  background: #fff;\n  transition: 0.2s ease-in-out;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);\n  height: 18px;\n  width: 18px;\n}\n.dplayer-controller .dplayer-icons .dplayer-toggle input:checked + label {\n  border-color: rgba(255, 255, 255, 0.5);\n}\n.dplayer-controller .dplayer-icons .dplayer-toggle input:checked + label:before {\n  width: 30px;\n  background: rgba(255, 255, 255, 0.5);\n}\n.dplayer-controller .dplayer-icons .dplayer-toggle input:checked + label:after {\n  left: 12px;\n}\n.dplayer-mobile-play {\n  display: none;\n  width: 50px;\n  height: 50px;\n  border: none;\n  background-color: transparent;\n  outline: none;\n  cursor: pointer;\n  box-sizing: border-box;\n  bottom: 0;\n  opacity: 0.8;\n  position: absolute;\n  left: 50%;\n  top: 50%;\n  transform: translate(-50%, -50%);\n}\n.dplayer-danmaku {\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: 0;\n  bottom: 0;\n  font-size: 22px;\n  color: #fff;\n}\n.dplayer-danmaku .dplayer-danmaku-item {\n  display: inline-block;\n  pointer-events: none;\n  user-select: none;\n  cursor: default;\n  white-space: nowrap;\n  text-shadow: 0.5px 0.5px 0.5px rgba(0, 0, 0, 0.5);\n}\n.dplayer-danmaku .dplayer-danmaku-item--demo {\n  position: absolute;\n  visibility: hidden;\n}\n.dplayer-danmaku .dplayer-danmaku-right {\n  position: absolute;\n  right: 0;\n  transform: translateX(100%);\n}\n.dplayer-danmaku .dplayer-danmaku-right.dplayer-danmaku-move {\n  will-change: transform;\n  animation-name: 'danmaku';\n  animation-timing-function: linear;\n  animation-play-state: paused;\n}\n@keyframes danmaku {\n  from {\n    transform: translateX(100%);\n  }\n}\n.dplayer-danmaku .dplayer-danmaku-top,\n.dplayer-danmaku .dplayer-danmaku-bottom {\n  position: absolute;\n  width: 100%;\n  text-align: center;\n  visibility: hidden;\n}\n.dplayer-danmaku .dplayer-danmaku-top.dplayer-danmaku-move,\n.dplayer-danmaku .dplayer-danmaku-bottom.dplayer-danmaku-move {\n  will-change: visibility;\n  animation-name: 'danmaku-center';\n  animation-timing-function: linear;\n  animation-play-state: paused;\n}\n@keyframes danmaku-center {\n  from {\n    visibility: visible;\n  }\n  to {\n    visibility: visible;\n  }\n}\n.dplayer-logo {\n  pointer-events: none;\n  position: absolute;\n  left: 20px;\n  top: 20px;\n  max-width: 50px;\n  max-height: 50px;\n}\n.dplayer-logo img {\n  max-width: 100%;\n  max-height: 100%;\n  background: none;\n}\n.dplayer-menu {\n  position: absolute;\n  width: 170px;\n  border-radius: 2px;\n  background: rgba(28, 28, 28, 0.85);\n  padding: 5px 0;\n  overflow: hidden;\n  z-index: 3;\n  display: none;\n}\n.dplayer-menu.dplayer-menu-show {\n  display: block;\n}\n.dplayer-menu .dplayer-menu-item {\n  height: 30px;\n  box-sizing: border-box;\n  cursor: pointer;\n}\n.dplayer-menu .dplayer-menu-item:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n.dplayer-menu .dplayer-menu-item a {\n  padding: 0 10px;\n  line-height: 30px;\n  color: #eee;\n  font-size: 13px;\n  display: inline-block;\n  vertical-align: middle;\n  width: 100%;\n  box-sizing: border-box;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  overflow: hidden;\n}\n.dplayer-menu .dplayer-menu-item a:hover {\n  text-decoration: none;\n}\n.dplayer-notice-list {\n  position: absolute;\n  bottom: 60px;\n  left: 20px;\n}\n.dplayer-notice-list .dplayer-notice {\n  border-radius: 2px;\n  background: rgba(28, 28, 28, 0.9);\n  transition: all 0.3s ease-in-out;\n  overflow: hidden;\n  color: #fff;\n  display: table;\n  pointer-events: none;\n  animation: showNotice 0.3s ease 1 forwards;\n}\n.dplayer-notice-list .remove-notice {\n  animation: removeNotice 0.3s ease 1 forwards;\n}\n@keyframes showNotice {\n  from {\n    padding: 0;\n    font-size: 0;\n    margin-top: 0;\n  }\n  to {\n    padding: 7px 20px;\n    font-size: 14px;\n    margin-top: 5px;\n  }\n}\n@keyframes removeNotice {\n  0% {\n    padding: 7px 20px;\n    font-size: 14px;\n    margin-top: 5px;\n  }\n  20% {\n    font-size: 12px;\n  }\n  21% {\n    font-size: 0;\n    padding: 7px 10px;\n  }\n  100% {\n    padding: 0;\n    margin-top: 0;\n    font-size: 0;\n  }\n}\n.dplayer-subtitle {\n  position: absolute;\n  bottom: 40px;\n  width: 90%;\n  left: 5%;\n  text-align: center;\n  color: #fff;\n  text-shadow: 0.5px 0.5px 0.5px rgba(0, 0, 0, 0.5);\n  font-size: 20px;\n}\n.dplayer-subtitle.dplayer-subtitle-hide {\n  display: none;\n}\n.dplayer-mask {\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  z-index: 1;\n  display: none;\n}\n.dplayer-mask.dplayer-mask-show {\n  display: block;\n}\n.dplayer-video-wrap {\n  position: relative;\n  background: #000;\n  font-size: 0;\n  width: 100%;\n  height: 100%;\n}\n.dplayer-video-wrap .dplayer-video {\n  width: 100%;\n  height: 100%;\n  display: none;\n}\n.dplayer-video-wrap .dplayer-video-current {\n  display: block;\n}\n.dplayer-video-wrap .dplayer-video-prepare {\n  display: none;\n}\n.dplayer-info-panel {\n  position: absolute;\n  top: 10px;\n  left: 10px;\n  width: 400px;\n  background: rgba(28, 28, 28, 0.8);\n  padding: 10px;\n  color: #fff;\n  font-size: 12px;\n  border-radius: 2px;\n}\n.dplayer-info-panel-hide {\n  display: none;\n}\n.dplayer-info-panel .dplayer-info-panel-close {\n  cursor: pointer;\n  position: absolute;\n  right: 10px;\n  top: 10px;\n}\n.dplayer-info-panel .dplayer-info-panel-item > span {\n  display: inline-block;\n  vertical-align: middle;\n  line-height: 15px;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  overflow: hidden;\n}\n.dplayer-info-panel .dplayer-info-panel-item-title {\n  width: 100px;\n  text-align: right;\n  margin-right: 10px;\n}\n.dplayer-info-panel .dplayer-info-panel-item-data {\n  width: 260px;\n}\n",
              ".dplayer {\n    position: relative;\n    overflow: hidden;\n    user-select: none;\n    line-height: 1;\n\n    * {\n        box-sizing: content-box;\n    }\n\n    svg {\n        width: 100%;\n        height: 100%;\n\n        path,\n        circle {\n            fill: #fff;\n        }\n    }\n\n    &:-webkit-full-screen {\n        width: 100%;\n        height: 100%;\n        background: #000;\n        position: fixed;\n        z-index: 100000;\n        left: 0;\n        top: 0;\n        margin: 0;\n        padding: 0;\n        transform: translate(0, 0);\n        \n    }\n\n    &.dplayer-no-danmaku {\n        .dplayer-controller .dplayer-icons .dplayer-setting .dplayer-setting-box {\n            .dplayer-setting-showdan,\n            .dplayer-setting-danmaku,\n            .dplayer-setting-danunlimit {\n                display: none;\n            }\n        }\n\n        .dplayer-controller .dplayer-icons .dplayer-comment {\n            display: none;\n        }\n\n        .dplayer-danmaku {\n            display: none;\n        }\n    }\n\n    &.dplayer-live {\n        .dplayer-time {\n            display: none;\n        }\n        .dplayer-bar-wrap {\n            display: none;\n        }\n        .dplayer-setting-speed {\n            display: none;\n        }\n        .dplayer-setting-loop {\n            display: none;\n        }\n\n        &.dplayer-no-danmaku {\n            .dplayer-setting {\n                display: none;\n            }\n        }\n    }\n\n    &.dplayer-arrow {\n        .dplayer-danmaku {\n            font-size: 18px;\n        }\n        .dplayer-icon {\n            margin: 0 -3px;\n        }\n    }\n\n    &.dplayer-playing {\n        .dplayer-danmaku .dplayer-danmaku-move {\n            animation-play-state: running;\n        }\n\n        @media (min-width: 900px) {\n            .dplayer-controller-mask {\n                opacity: 0;\n            }\n            .dplayer-controller {\n                opacity: 0;\n            }\n\n            &:hover {\n                .dplayer-controller-mask {\n                    opacity: 1;\n                }\n                .dplayer-controller {\n                    opacity: 1;\n                }\n            }\n        }\n    }\n\n    &.dplayer-loading {\n        .dplayer-bezel .diplayer-loading-icon {\n            display: block;\n        }\n    }\n\n    &.dplayer-loading,\n    &.dplayer-paused {\n        .dplayer-danmaku,\n        .dplayer-danmaku-move {\n            animation-play-state: paused;\n        }\n    }\n\n    &.dplayer-hide-controller {\n        cursor: none;\n\n        .dplayer-controller-mask {\n            opacity: 0;\n            transform: translateY(100%);\n        }\n        .dplayer-controller {\n            opacity: 0;\n            transform: translateY(100%);\n        }\n    }\n    &.dplayer-show-controller {\n        .dplayer-controller-mask {\n            opacity: 1;\n        }\n        .dplayer-controller {\n            opacity: 1;\n        }\n    }\n    &.dplayer-fulled {\n        position: fixed;\n        z-index: 100000;\n        left: 0;\n        top: 0;\n        width: 100% !important;\n        height: 100% !important;\n    }\n    &.dplayer-mobile {\n        .dplayer-controller .dplayer-icons {\n            .dplayer-volume,\n            .dplayer-camera-icon,\n            .dplayer-airplay-icon,\n            .dplayer-chromecast-icon,\n            .dplayer-play-icon {\n                display: none;\n            }\n            .dplayer-full .dplayer-full-in-icon {\n                position: static;\n                display: inline-block;\n            }\n        }\n\n        .dplayer-bar-time {\n            display: none;\n        }\n\n        &.dplayer-hide-controller {\n            .dplayer-mobile-play {\n                display: none;\n            }\n        }\n\n        .dplayer-mobile-play {\n            display: block;\n        }\n    }\n}\n\n// To hide scroll bar, apply this class to <body>\n.dplayer-web-fullscreen-fix {\n    position: fixed;\n    top: 0;\n    left: 0;\n    margin: 0;\n    padding: 0;\n}\n",
              "@import '../../node_modules/balloon-css/balloon.css';\n\n[data-balloon]:before {\n    display: none;\n}\n\n[data-balloon]:after {\n    padding: 0.3em 0.7em;\n    background: rgba(17, 17, 17, 0.7);\n}\n\n[data-balloon][data-balloon-pos=\"up\"]:after {\n    margin-bottom: 0;\n}",
              ".dplayer-bezel {\n    position: absolute;\n    left: 0;\n    right: 0;\n    top: 0;\n    bottom: 0;\n    font-size: 22px;\n    color: #fff;\n    pointer-events: none;\n    .dplayer-bezel-icon {\n        position: absolute;\n        top: 50%;\n        left: 50%;\n        margin: -26px 0 0 -26px;\n        height: 52px;\n        width: 52px;\n        padding: 12px;\n        box-sizing: border-box;\n        background: rgba(0, 0, 0, .5);\n        border-radius: 50%;\n        opacity: 0;\n        pointer-events: none;\n        &.dplayer-bezel-transition {\n            animation: bezel-hide .5s linear;\n        }\n        @keyframes bezel-hide {\n            from {\n                opacity: 1;\n                transform: scale(1);\n            }\n            to {\n                opacity: 0;\n                transform: scale(2);\n            }\n        }\n    }\n    .dplayer-danloading {\n        position: absolute;\n        top: 50%;\n        margin-top: -7px;\n        width: 100%;\n        text-align: center;\n        font-size: 14px;\n        line-height: 14px;\n        animation: my-face 5s infinite ease-in-out;\n    }\n    .diplayer-loading-icon {\n        display: none;\n        position: absolute;\n        top: 50%;\n        left: 50%;\n        margin: -18px 0 0 -18px;\n        height: 36px;\n        width: 36px;\n        pointer-events: none;\n        .diplayer-loading-hide {\n            display: none;\n        }\n        .diplayer-loading-dot {\n            animation: diplayer-loading-dot-fade .8s ease infinite;\n            opacity: 0;\n            transform-origin: 4px 4px;\n            each(range(7), {\n                &.diplayer-loading-dot-@{value} {\n                    animation-delay: (@value * 0.1s);\n                }\n            });\n        }\n        @keyframes diplayer-loading-dot-fade {\n            0% {\n                opacity: .7;\n                transform: scale(1.2, 1.2)\n            }\n            50% {\n                opacity: .25;\n                transform: scale(.9, .9)\n            }\n            to {\n                opacity: .25;\n                transform: scale(.85, .85)\n            }\n        }\n    }\n}",
              '.dplayer-controller-mask {\n    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAADGCAYAAAAT+OqFAAAAdklEQVQoz42QQQ7AIAgEF/T/D+kbq/RWAlnQyyazA4aoAB4FsBSA/bFjuF1EOL7VbrIrBuusmrt4ZZORfb6ehbWdnRHEIiITaEUKa5EJqUakRSaEYBJSCY2dEstQY7AuxahwXFrvZmWl2rh4JZ07z9dLtesfNj5q0FU3A5ObbwAAAABJRU5ErkJggg==) repeat-x bottom;\n    height: 98px;\n    width: 100%;\n    position: absolute;\n    bottom: 0;\n    transition: all 0.3s ease;\n}\n\n.dplayer-controller {\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    height: 41px;\n    padding: 0 20px;\n    user-select: none;\n    transition: all 0.3s ease;\n    &.dplayer-controller-comment {\n        .dplayer-icons {\n            display: none;\n        }\n        .dplayer-icons.dplayer-comment-box {\n            display: block;\n        }\n    }\n    .dplayer-bar-wrap {\n        padding: 5px 0;\n        cursor: pointer;\n        position: absolute;\n        bottom: 33px;\n        width: calc(100% - 40px);\n        height: 3px;\n        &:hover {\n            .dplayer-bar .dplayer-played .dplayer-thumb {\n                transform: scale(1);\n            }\n            .dplayer-highlight {\n                display: block;\n                width: 8px;\n                transform: translateX(-4px);\n                top: 4px;\n                height: 40%;\n            }\n        }\n        .dplayer-highlight {\n            z-index: 12;\n            position: absolute;\n            top: 5px;\n            width: 6px;\n            height: 20%;\n            border-radius: 6px;\n            background-color: #fff;\n            text-align: center;\n            transform: translateX(-3px);\n            transition: all .2s ease-in-out;\n            &:hover {\n                .dplayer-highlight-text {\n                    display: block;\n                }\n                &~.dplayer-bar-preview {\n                    opacity: 0;\n                }\n                &~.dplayer-bar-time {\n                    opacity: 0;\n                }\n            }\n            .dplayer-highlight-text {\n                display: none;\n                position: absolute;\n                left: 50%;\n                top: -24px;\n                padding: 5px 8px;\n                background-color: rgba(0, 0, 0, .62);\n                color: #fff;\n                border-radius: 4px;\n                font-size: 12px;\n                white-space: nowrap;\n                transform: translateX(-50%);\n            }\n        }\n        .dplayer-bar-preview {\n            position: absolute;\n            background: #fff;\n            pointer-events: none;\n            display: none;\n            background-size: 16000px 100%;\n        }\n        .dplayer-bar-preview-canvas {\n            position: absolute;\n            width: 100%;\n            height: 100%;\n            z-index: 1;\n            pointer-events: none;\n        }\n        .dplayer-bar-time {\n            &.hidden {\n                opacity: 0;\n            }\n            position: absolute;\n            left: 0px;\n            top: -20px;\n            border-radius: 4px;\n            padding: 5px 7px;\n            background-color: rgba(0, 0, 0, 0.62);\n            color: #fff;\n            font-size: 12px;\n            text-align: center;\n            opacity: 1;\n            transition: opacity .1s ease-in-out;\n            word-wrap: normal;\n            word-break: normal;\n            z-index: 2;\n            pointer-events: none;\n        }\n        .dplayer-bar {\n            position: relative;\n            height: 3px;\n            width: 100%;\n            background: rgba(255, 255, 255, .2);\n            cursor: pointer;\n            .dplayer-loaded {\n                position: absolute;\n                left: 0;\n                top: 0;\n                bottom: 0;\n                background: rgba(255, 255, 255, .4);\n                height: 3px;\n                transition: all 0.5s ease;\n                will-change: width;\n            }\n            .dplayer-played {\n                position: absolute;\n                left: 0;\n                top: 0;\n                bottom: 0;\n                height: 3px;\n                will-change: width;\n                .dplayer-thumb {\n                    position: absolute;\n                    top: 0;\n                    right: 5px;\n                    margin-top: -4px;\n                    margin-right: -10px;\n                    height: 11px;\n                    width: 11px;\n                    border-radius: 50%;\n                    cursor: pointer;\n                    transition: all .3s ease-in-out;\n                    transform: scale(0);\n                }\n            }\n        }\n    }\n    .dplayer-icons {\n        height: 38px;\n        position: absolute;\n        bottom: 0;\n        &.dplayer-comment-box {\n            display: none;\n            position: absolute;\n            transition: all .3s ease-in-out;\n            z-index: 2;\n            height: 38px;\n            bottom: 0;\n            left: 20px;\n            right: 20px;\n            color: #fff;\n            .dplayer-icon {\n                padding: 7px;\n            }\n            .dplayer-comment-setting-icon {\n                position: absolute;\n                left: 0;\n                top: 0;\n            }\n            .dplayer-send-icon {\n                position: absolute;\n                right: 0;\n                top: 0;\n            }\n            .dplayer-comment-setting-box {\n                position: absolute;\n                background: rgba(28, 28, 28, 0.9);\n                bottom: 41px;\n                left: 0;\n                box-shadow: 0 0 25px rgba(0, 0, 0, .3);\n                border-radius: 4px;\n                padding: 10px 10px 16px;\n                font-size: 14px;\n                width: 204px;\n                transition: all .3s ease-in-out;\n                transform: scale(0);\n                &.dplayer-comment-setting-open {\n                    transform: scale(1);\n                }\n                input[type=radio] {\n                    display: none;\n                }\n                label {\n                    cursor: pointer;\n                }\n                .dplayer-comment-setting-title {\n                    font-size: 13px;\n                    color: #fff;\n                    line-height: 30px;\n                }\n                .dplayer-comment-setting-type {\n                    font-size: 0;\n                    .dplayer-comment-setting-title {\n                        margin-bottom: 6px;\n                    }\n                    label {\n                        &:nth-child(2) {\n                            span {\n                                border-radius: 4px 0 0 4px;\n                            }\n                        }\n                        &:nth-child(4) {\n                            span {\n                                border-radius: 0 4px 4px 0;\n                            }\n                        }\n                    }\n                    span {\n                        width: 33%;\n                        padding: 4px 6px;\n                        line-height: 16px;\n                        display: inline-block;\n                        font-size: 12px;\n                        color: #fff;\n                        border: 1px solid #fff;\n                        margin-right: -1px;\n                        box-sizing: border-box;\n                        text-align: center;\n                        cursor: pointer;\n                    }\n                    input:checked+span {\n                        background: #E4E4E6;\n                        color: #1c1c1c;\n                    }\n                }\n                .dplayer-comment-setting-color {\n                    font-size: 0;\n                    label {\n                        font-size: 0;\n                        padding: 6px;\n                        display: inline-block;\n                    }\n                    span {\n                        width: 22px;\n                        height: 22px;\n                        display: inline-block;\n                        border-radius: 50%;\n                        box-sizing: border-box;\n                        cursor: pointer;\n                        &:hover {\n                            animation: my-face 5s infinite ease-in-out;\n                        }\n                    }\n                }\n            }\n            .dplayer-comment-input {\n                outline: none;\n                border: none;\n                padding: 8px 31px;\n                font-size: 14px;\n                line-height: 18px;\n                text-align: center;\n                border-radius: 4px;\n                background: none;\n                margin: 0;\n                height: 100%;\n                box-sizing: border-box;\n                width: 100%;\n                color: #fff;\n                &::placeholder {\n                    color: #fff;\n                    opacity: 0.8;\n                }\n                &::-ms-clear {\n                    display: none;\n                }\n            }\n        }\n        &.dplayer-icons-left {\n            .dplayer-icon {\n                padding: 7px;\n            }\n        }\n        &.dplayer-icons-right {\n            right: 20px;\n            .dplayer-icon {\n                padding: 8px;\n            }\n        }\n        .dplayer-time,\n        .dplayer-live-badge {\n            line-height: 38px;\n            color: #eee;\n            text-shadow: 0 0 2px rgba(0, 0, 0, .5);\n            vertical-align: middle;\n            font-size: 13px;\n            cursor: default;\n        }\n        .dplayer-live-dot {\n            display: inline-block;\n            width: 6px;\n            height: 6px;\n            vertical-align: 4%;\n            margin-right: 5px;\n            content: \'\';\n            border-radius: 6px;\n        }\n        .dplayer-icon {\n            width: 40px;\n            height: 100%;\n            border: none;\n            background-color: transparent;\n            outline: none;\n            cursor: pointer;\n            vertical-align: middle;\n            box-sizing: border-box;\n            display: inline-block;\n            .dplayer-icon-content {\n                transition: all .2s ease-in-out;\n                opacity: .8;\n            }\n            &:hover {\n                .dplayer-icon-content {\n                    opacity: 1;\n                }\n            }\n            &.dplayer-quality-icon {\n                color: #fff;\n                width: auto;\n                line-height: 22px;\n                font-size: 14px;\n            }\n            &.dplayer-comment-icon {\n                padding: 10px 9px 9px;\n            }\n            &.dplayer-setting-icon {\n                padding-top: 8.5px;\n            }\n            &.dplayer-volume-icon {\n                width: 43px;\n            }\n        }\n        .dplayer-volume {\n            position: relative;\n            display: inline-block;\n            cursor: pointer;\n            height: 100%;\n            &:hover {\n                .dplayer-volume-bar-wrap .dplayer-volume-bar {\n                    width: 45px;\n                }\n                .dplayer-volume-bar-wrap .dplayer-volume-bar .dplayer-volume-bar-inner .dplayer-thumb {\n                    transform: scale(1);\n                }\n            }\n            &.dplayer-volume-active {\n                .dplayer-volume-bar-wrap .dplayer-volume-bar {\n                    width: 45px;\n                }\n                .dplayer-volume-bar-wrap .dplayer-volume-bar .dplayer-volume-bar-inner .dplayer-thumb {\n                    transform: scale(1);\n                }\n            }\n            .dplayer-volume-bar-wrap {\n                display: inline-block;\n                margin: 0 10px 0 -5px;\n                vertical-align: middle;\n                height: 100%;\n                .dplayer-volume-bar {\n                    position: relative;\n                    top: 17px;\n                    width: 0;\n                    height: 3px;\n                    background: #aaa;\n                    transition: all 0.3s ease-in-out;\n                    .dplayer-volume-bar-inner {\n                        position: absolute;\n                        bottom: 0;\n                        left: 0;\n                        height: 100%;\n                        transition: all 0.1s ease;\n                        will-change: width;\n                        .dplayer-thumb {\n                            position: absolute;\n                            top: 0;\n                            right: 5px;\n                            margin-top: -4px;\n                            margin-right: -10px;\n                            height: 11px;\n                            width: 11px;\n                            border-radius: 50%;\n                            cursor: pointer;\n                            transition: all .3s ease-in-out;\n                            transform: scale(0);\n                        }\n                    }\n                }\n            }\n        }\n        .dplayer-subtitle-btn {\n            display: inline-block;\n            height: 100%;\n        }\n        .dplayer-subtitles {\n            display: inline-block;\n            height: 100%;\n            .dplayer-subtitles-box {\n                position: absolute;\n                right: 0;\n                bottom: 50px;\n                transform: scale(0);\n                width: fit-content;\n                max-width: 240px;\n                min-width: 120px;\n                border-radius: 2px;\n                background: rgba(28, 28, 28, 0.9);\n                padding: 7px 0;\n                transition: all .3s ease-in-out;\n                overflow: auto;\n                z-index: 2;\n                &.dplayer-subtitles-panel {\n                    display: block;\n                }\n                &.dplayer-subtitles-box-open {\n                    transform: scale(1);\n                }\n            }\n            .dplayer-subtitles-item {\n                height: 30px;\n                padding: 5px 10px;\n                box-sizing: border-box;\n                cursor: pointer;\n                position: relative;\n                &:hover {\n                    background-color: rgba(255, 255, 255, .1);\n                }\n            }\n        }\n        .dplayer-setting {\n            display: inline-block;\n            height: 100%;\n            .dplayer-setting-box {\n                position: absolute;\n                right: 0;\n                bottom: 50px;\n                transform: scale(0);\n                width: 150px;\n                border-radius: 2px;\n                background: rgba(28, 28, 28, 0.9);\n                padding: 7px 0;\n                transition: all .3s ease-in-out;\n                overflow: hidden;\n                z-index: 2;\n                &>div {\n                    display: none;\n                    &.dplayer-setting-origin-panel {\n                        display: block;\n                    }\n                }\n                &.dplayer-setting-box-open {\n                    transform: scale(1);\n                }\n                &.dplayer-setting-box-narrow {\n                    width: 70px;\n                    text-align: center;\n                }\n                &.dplayer-setting-box-speed {\n                    .dplayer-setting-origin-panel {\n                        display: none;\n                    }\n                    .dplayer-setting-speed-panel {\n                        display: block;\n                    }\n                }\n            }\n            .dplayer-setting-item,\n            .dplayer-setting-speed-item {\n                height: 30px;\n                padding: 5px 10px;\n                box-sizing: border-box;\n                cursor: pointer;\n                position: relative;\n                &:hover {\n                    background-color: rgba(255, 255, 255, .1);\n                }\n            }\n            .dplayer-setting-danmaku {\n                padding: 5px 0;\n                .dplayer-label {\n                    padding: 0 10px;\n                    display: inline;\n                }\n                &:hover {\n                    .dplayer-label {\n                        display: none;\n                    }\n                    .dplayer-danmaku-bar-wrap {\n                        display: inline-block;\n                    }\n                }\n                &.dplayer-setting-danmaku-active {\n                    .dplayer-label {\n                        display: none;\n                    }\n                    .dplayer-danmaku-bar-wrap {\n                        display: inline-block;\n                    }\n                }\n                .dplayer-danmaku-bar-wrap {\n                    padding: 0 10px;\n                    box-sizing: border-box;\n                    display: none;\n                    vertical-align: middle;\n                    height: 100%;\n                    width: 100%;\n                    .dplayer-danmaku-bar {\n                        position: relative;\n                        top: 8.5px;\n                        width: 100%;\n                        height: 3px;\n                        background: #fff;\n                        transition: all 0.3s ease-in-out;\n                        .dplayer-danmaku-bar-inner {\n                            position: absolute;\n                            bottom: 0;\n                            left: 0;\n                            height: 100%;\n                            transition: all 0.1s ease;\n                            background: #aaa;\n                            will-change: width;\n                            .dplayer-thumb {\n                                position: absolute;\n                                top: 0;\n                                right: 5px;\n                                margin-top: -4px;\n                                margin-right: -10px;\n                                height: 11px;\n                                width: 11px;\n                                border-radius: 50%;\n                                cursor: pointer;\n                                transition: all .3s ease-in-out;\n                                background: #aaa;\n                            }\n                        }\n                    }\n                }\n            }\n        }\n        .dplayer-full {\n            display: inline-block;\n            height: 100%;\n            position: relative;\n            &:hover {\n                .dplayer-full-in-icon {\n                    display: block;\n                }\n            }\n            .dplayer-full-in-icon {\n                position: absolute;\n                top: -30px;\n                z-index: 1;\n                display: none;\n            }\n        }\n        .dplayer-quality {\n            position: relative;\n            display: inline-block;\n            height: 100%;\n            z-index: 2;\n            &:hover {\n                .dplayer-quality-list {\n                    display: block;\n                }\n                .dplayer-quality-mask {\n                    display: block;\n                }\n            }\n            .dplayer-quality-mask {\n                display: none;\n                position: absolute;\n                bottom: 38px;\n                left: -18px;\n                width: 80px;\n                padding-bottom: 12px;\n            }\n            .dplayer-quality-list {\n                display: none;\n                font-size: 12px;\n                width: 80px;\n                border-radius: 2px;\n                background: rgba(28, 28, 28, 0.9);\n                padding: 5px 0;\n                transition: all .3s ease-in-out;\n                overflow: hidden;\n                color: #fff;\n                text-align: center;\n            }\n            .dplayer-quality-item {\n                height: 25px;\n                box-sizing: border-box;\n                cursor: pointer;\n                line-height: 25px;\n                &:hover {\n                    background-color: rgba(255, 255, 255, .1);\n                }\n            }\n        }\n        .dplayer-comment {\n            display: inline-block;\n            height: 100%;\n        }\n        .dplayer-label {\n            color: #eee;\n            font-size: 13px;\n            display: inline-block;\n            vertical-align: middle;\n            white-space: nowrap;\n        }\n        .dplayer-toggle {\n            width: 32px;\n            height: 20px;\n            text-align: center;\n            font-size: 0;\n            vertical-align: middle;\n            position: absolute;\n            top: 5px;\n            right: 10px;\n            input {\n                max-height: 0;\n                max-width: 0;\n                display: none;\n            }\n            input+label {\n                display: inline-block;\n                position: relative;\n                box-shadow: rgb(223, 223, 223) 0 0 0 0 inset;\n                border: 1px solid rgb(223, 223, 223);\n                height: 20px;\n                width: 32px;\n                border-radius: 10px;\n                box-sizing: border-box;\n                cursor: pointer;\n                transition: .2s ease-in-out;\n            }\n            input+label:before {\n                content: "";\n                position: absolute;\n                display: block;\n                height: 18px;\n                width: 18px;\n                top: 0;\n                left: 0;\n                border-radius: 15px;\n                transition: .2s ease-in-out;\n            }\n            input+label:after {\n                content: "";\n                position: absolute;\n                display: block;\n                left: 0;\n                top: 0;\n                border-radius: 15px;\n                background: #fff;\n                transition: .2s ease-in-out;\n                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);\n                height: 18px;\n                width: 18px;\n            }\n            input:checked+label {\n                border-color: rgba(255, 255, 255, 0.5);\n            }\n            input:checked+label:before {\n                width: 30px;\n                background: rgba(255, 255, 255, 0.5);\n            }\n            input:checked+label:after {\n                left: 12px;\n            }\n        }\n    }\n}\n\n.dplayer-mobile-play {\n    display: none;\n    width: 50px;\n    height: 50px;\n    border: none;\n    background-color: transparent;\n    outline: none;\n    cursor: pointer;\n    box-sizing: border-box;\n    position: absolute;\n    bottom: 0;\n    opacity: 0.8;\n    position: absolute;\n    left: 50%;\n    top: 50%;\n    transform: translate(-50%, -50%);\n}',
              ".dplayer-danmaku {\n    position: absolute;\n    left: 0;\n    right: 0;\n    top: 0;\n    bottom: 0;\n    font-size: 22px;\n    color: #fff;\n    .dplayer-danmaku-item {\n        display: inline-block;\n        pointer-events: none;\n        user-select: none;\n        cursor: default;\n        white-space: nowrap;\n        text-shadow: .5px .5px .5px rgba(0, 0, 0, .5);\n        &--demo {\n            position: absolute;\n            visibility: hidden;\n        }\n    }\n    .dplayer-danmaku-right {\n        position: absolute;\n        right: 0;\n        transform: translateX(100%);\n        &.dplayer-danmaku-move {\n            will-change: transform;\n            animation-name: 'danmaku';\n            animation-timing-function: linear;\n            animation-play-state: paused;\n        }\n    }\n    @keyframes danmaku {\n        from {\n            transform: translateX(100%);\n        }\n    }\n    .dplayer-danmaku-top,\n    .dplayer-danmaku-bottom {\n        position: absolute;\n        width: 100%;\n        text-align: center;\n        visibility: hidden;\n        &.dplayer-danmaku-move {\n            will-change: visibility;\n            animation-name: 'danmaku-center';\n            animation-timing-function: linear;\n            animation-play-state: paused;\n        }\n    }\n    @keyframes danmaku-center {\n        from {\n            visibility: visible;\n        }\n        to {\n            visibility: visible;\n        }\n    }\n}",
              ".dplayer-logo {\n    pointer-events: none;\n    position: absolute;\n    left: 20px;\n    top: 20px;\n    max-width: 50px;\n    max-height: 50px;\n    img {\n        max-width: 100%;\n        max-height: 100%;\n        background: none;\n    }\n}",
              ".dplayer-menu {\n    position: absolute;\n    width: 170px;\n    border-radius: 2px;\n    background: rgba(28, 28, 28, 0.85);\n    padding: 5px 0;\n    overflow: hidden;\n    z-index: 3;\n    display: none;\n    &.dplayer-menu-show {\n        display: block;\n    }\n    .dplayer-menu-item {\n        height: 30px;\n        box-sizing: border-box;\n        cursor: pointer;\n        &:hover {\n            background-color: rgba(255, 255, 255, .1);\n        }\n        a {\n            display: inline-block;\n            padding: 0 10px;\n            line-height: 30px;\n            color: #eee;\n            font-size: 13px;\n            display: inline-block;\n            vertical-align: middle;\n            width: 100%;\n            box-sizing: border-box;\n            white-space: nowrap;\n            text-overflow: ellipsis;\n            overflow: hidden;\n            &:hover {\n                text-decoration: none;\n            }\n        }\n    }\n}",
              ".dplayer-notice-list{\n    position: absolute;\n    bottom: 60px;\n    left: 20px;\n\n    .dplayer-notice {\n        border-radius: 2px;\n        background: rgba(28, 28, 28, 0.9);\n        transition: all .3s ease-in-out;\n        overflow: hidden;\n        color: #fff;\n        display: table;\n        pointer-events: none;\n        animation: showNotice .3s ease 1 forwards;\n    }\n\n    .remove-notice{\n        animation: removeNotice .3s ease 1 forwards;\n    }\n}\n\n@keyframes showNotice {\n    from {\n        padding: 0;\n        font-size: 0;\n        margin-top: 0;\n    }\n    to {\n        padding: 7px 20px;\n        font-size: 14px;\n        margin-top: 5px;\n    }\n}\n\n@keyframes removeNotice {\n    0%{\n        padding: 7px 20px;\n        font-size: 14px;\n        margin-top: 5px;\n    }\n    20%{\n        font-size: 12px;\n    }\n    21%{\n        font-size: 0;\n        padding: 7px 10px;\n    }\n    100%{\n        padding: 0;\n        margin-top: 0;\n        font-size: 0;\n    }\n}\n",
              ".dplayer-subtitle {\n    position: absolute;\n    bottom: 40px;\n    width: 90%;\n    left: 5%;\n    text-align: center;\n    color: #fff;\n    text-shadow: 0.5px 0.5px 0.5px rgba(0, 0, 0, 0.5);\n    font-size: 20px;\n    &.dplayer-subtitle-hide {\n        display: none;\n    }\n}",
              ".dplayer-mask {\n    position: absolute;\n    top: 0;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    z-index: 1;\n    display: none;\n    &.dplayer-mask-show {\n        display: block;\n    }\n}\n\n.dplayer-video-wrap {\n    position: relative;\n    background: #000;\n    font-size: 0;\n    width: 100%;\n    height: 100%;\n    .dplayer-video {\n        width: 100%;\n        height: 100%;\n        display: none;\n    }\n    .dplayer-video-current {\n        display: block;\n    }\n    .dplayer-video-prepare {\n        display: none;\n    }\n}",
              ".dplayer-info-panel {\n    position: absolute;\n    top: 10px;\n    left: 10px;\n    width: 400px;\n    background: rgba(28, 28, 28, 0.8);\n    padding: 10px;\n    color: #fff;\n    font-size: 12px;\n    border-radius: 2px;\n\n    &-hide {\n        display: none;\n    }\n\n    .dplayer-info-panel-close {\n        cursor: pointer;\n        position: absolute;\n        right: 10px;\n        top: 10px;\n    }\n\n    .dplayer-info-panel-item {\n        & > span {\n            display: inline-block;\n            vertical-align: middle;\n            line-height: 15px;\n            white-space: nowrap;\n            text-overflow: ellipsis;\n            overflow: hidden;\n        }\n    }\n\n    .dplayer-info-panel-item-title {\n        width: 100px;\n        text-align: right;\n        margin-right: 10px;\n    }\n    \n    .dplayer-info-panel-item-data {\n        width: 260px;\n    }\n}"
            ],
            sourceRoot: ""
          }
        ]);
        const u = c
      },
      72: n => {
        "use strict";
        var e = [];

        function t(n) {
          for (var t = -1, a = 0; a < e.length; a++)
            if (e[a].identifier === n) {
              t = a;
              break
            } return t
        }

        function a(n, a) {
          for (var r = {}, i = [], l = 0; l < n.length; l++) {
            var s = n[l],
              p = a.base ? s[0] + a.base : s[0],
              d = r[p] || 0,
              c = "".concat(p, " ").concat(d);
            r[p] = d + 1;
            var A = t(c),
              u = {
                css: s[1],
                media: s[2],
                sourceMap: s[3],
                supports: s[4],
                layer: s[5]
              };
            if (-1 !== A) e[A].references++, e[A].updater(u);
            else {
              var y = o(u, a);
              a.byIndex = l, e.splice(l, 0, {
                identifier: c,
                updater: y,
                references: 1
              })
            }
            i.push(c)
          }
          return i
        }

        function o(n, e) {
          var t = e.domAPI(e);
          return t.update(n),
            function(e) {
              if (e) {
                if (e.css === n.css && e.media === n.media && e.sourceMap === n.sourceMap && e.supports === n
                  .supports && e.layer === n.layer) return;
                t.update(n = e)
              } else t.remove()
            }
        }
        n.exports = function(n, o) {
          var r = a(n = n || [], o = o || {});
          return function(n) {
            n = n || [];
            for (var i = 0; i < r.length; i++) {
              var l = t(r[i]);
              e[l].references--
            }
            for (var s = a(n, o), p = 0; p < r.length; p++) {
              var d = t(r[p]);
              0 === e[d].references && (e[d].updater(), e.splice(d, 1))
            }
            r = s
          }
        }
      },
      976: n => {
        "use strict";
        var e = {};
        n.exports = function(n, t) {
          var a = function(n) {
            if (void 0 === e[n]) {
              var t = document.querySelector(n);
              if (window.HTMLIFrameElement && t instanceof window.HTMLIFrameElement) try {
                t = t.contentDocument.head
              } catch (n) {
                t = null
              }
              e[n] = t
            }
            return e[n]
          }(n);
          if (!a) throw new Error(
            "Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid."
            );
          a.appendChild(t)
        }
      },
      566: n => {
        "use strict";
        n.exports = function(n) {
          var e = document.createElement("style");
          return n.setAttributes(e, n.attributes), n.insert(e, n.options), e
        }
      },
      147: (n, e, t) => {
        "use strict";
        n.exports = function(n) {
          var e = t.nc;
          e && n.setAttribute("nonce", e)
        }
      },
      298: n => {
        "use strict";
        n.exports = function(n) {
          if ("undefined" == typeof document) return {
            update: function() {},
            remove: function() {}
          };
          var e = n.insertStyleElement(n);
          return {
            update: function(t) {
              ! function(n, e, t) {
                var a = "";
                t.supports && (a += "@supports (".concat(t.supports, ") {")), t.media && (a += "@media "
                  .concat(t.media, " {"));
                var o = void 0 !== t.layer;
                o && (a += "@layer".concat(t.layer.length > 0 ? " ".concat(t.layer) : "", " {")), a += t
                  .css, o && (a += "}"), t.media && (a += "}"), t.supports && (a += "}");
                var r = t.sourceMap;
                r && "undefined" != typeof btoa && (a +=
                  "\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(
                    encodeURIComponent(JSON.stringify(r)))), " */")), e.styleTagTransform(a, n, e.options)
              }(e, n, t)
            },
            remove: function() {
              ! function(n) {
                if (null === n.parentNode) return !1;
                n.parentNode.removeChild(n)
              }(e)
            }
          }
        }
      },
      396: n => {
        "use strict";
        n.exports = function(n, e) {
          if (e.styleSheet) e.styleSheet.cssText = n;
          else {
            for (; e.firstChild;) e.removeChild(e.firstChild);
            e.appendChild(document.createTextNode(n))
          }
        }
      },
      251: n => {
        n.exports =
          '<svg viewBox="0 0 288 288" xmlns="http://www.w3.org/2000/svg"><path d="M288 90v96c0 20-16 36-36 36h-10c-16 0-16-24 0-24h10c7 0 12-5 12-12V90c0-7-5-12-12-12H36c-7 0-12 5-12 12v96c0 7 5 12 12 12h10c16 0 16 24 0 24H36c-20 0-36-16-36-36V90c0-20 16-36 36-36h216c20 0 36 16 36 36zm-120 62l48 68c14 20 1 38-20 38H92c-21 0-34-18-20-38l48-68c13-18 35-18 48 0z"></path></svg>'
      },
      113: n => {
        n.exports =
          '<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 32 32"><path d="M16 23c-3.309 0-6-2.691-6-6s2.691-6 6-6 6 2.691 6 6-2.691 6-6 6zM16 13c-2.206 0-4 1.794-4 4s1.794 4 4 4c2.206 0 4-1.794 4-4s-1.794-4-4-4zM27 28h-22c-1.654 0-3-1.346-3-3v-16c0-1.654 1.346-3 3-3h3c0.552 0 1 0.448 1 1s-0.448 1-1 1h-3c-0.551 0-1 0.449-1 1v16c0 0.552 0.449 1 1 1h22c0.552 0 1-0.448 1-1v-16c0-0.551-0.448-1-1-1h-11c-0.552 0-1-0.448-1-1s0.448-1 1-1h11c1.654 0 3 1.346 3 3v16c0 1.654-1.346 3-3 3zM24 10.5c0 0.828 0.672 1.5 1.5 1.5s1.5-0.672 1.5-1.5c0-0.828-0.672-1.5-1.5-1.5s-1.5 0.672-1.5 1.5zM15 4c0 0.552-0.448 1-1 1h-4c-0.552 0-1-0.448-1-1v0c0-0.552 0.448-1 1-1h4c0.552 0 1 0.448 1 1v0z"></path></svg>'
      },
      193: n => {
        n.exports =
          '<svg aria-hidden="true" focusable="false" data-prefix="fab" data-icon="chromecast" class="svg-inline--fa fa-chromecast fa-w-16" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M447.8,64H64c-23.6,0-42.7,19.1-42.7,42.7v63.9H64v-63.9h383.8v298.6H298.6V448H448c23.6,0,42.7-19.1,42.7-42.7V106.7 C490.7,83.1,471.4,64,447.8,64z M21.3,383.6L21.3,383.6l0,63.9h63.9C85.2,412.2,56.6,383.6,21.3,383.6L21.3,383.6z M21.3,298.6V341 c58.9,0,106.6,48.1,106.6,107h42.7C170.7,365.6,103.7,298.7,21.3,298.6z M213.4,448h42.7c-0.5-129.5-105.3-234.3-234.8-234.6l0,42.4 C127.3,255.6,213.3,342,213.4,448z"></path></svg>'
      },
      338: n => {
        n.exports =
          '<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 32 32"><path d="M27.090 0.131h-22.731c-2.354 0-4.262 1.839-4.262 4.109v16.401c0 2.269 1.908 4.109 4.262 4.109h4.262v-2.706h8.469l-8.853 8.135 1.579 1.451 7.487-6.88h9.787c2.353 0 4.262-1.84 4.262-4.109v-16.401c0-2.27-1.909-4.109-4.262-4.109v0zM28.511 19.304c0 1.512-1.272 2.738-2.841 2.738h-8.425l-0.076-0.070-0.076 0.070h-11.311c-1.569 0-2.841-1.226-2.841-2.738v-13.696c0-1.513 1.272-2.739 2.841-2.739h19.889c1.569 0 2.841-0.142 2.841 1.37v15.064z"></path></svg>'
      },
      807: n => {
        n.exports =
          '<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 32 32"><path d="M27.128 0.38h-22.553c-2.336 0-4.229 1.825-4.229 4.076v16.273c0 2.251 1.893 4.076 4.229 4.076h4.229v-2.685h8.403l-8.784 8.072 1.566 1.44 7.429-6.827h9.71c2.335 0 4.229-1.825 4.229-4.076v-16.273c0-2.252-1.894-4.076-4.229-4.076zM28.538 19.403c0 1.5-1.262 2.717-2.819 2.717h-8.36l-0.076-0.070-0.076 0.070h-11.223c-1.557 0-2.819-1.217-2.819-2.717v-13.589c0-1.501 1.262-2.718 2.819-2.718h19.734c1.557 0 2.819-0.141 2.819 1.359v14.947zM9.206 10.557c-1.222 0-2.215 0.911-2.215 2.036s0.992 2.035 2.215 2.035c1.224 0 2.216-0.911 2.216-2.035s-0.992-2.036-2.216-2.036zM22.496 10.557c-1.224 0-2.215 0.911-2.215 2.036s0.991 2.035 2.215 2.035c1.224 0 2.215-0.911 2.215-2.035s-0.991-2.036-2.215-2.036zM15.852 10.557c-1.224 0-2.215 0.911-2.215 2.036s0.991 2.035 2.215 2.035c1.222 0 2.215-0.911 2.215-2.035s-0.992-2.036-2.215-2.036z"></path></svg>'
      },
      415: n => {
        n.exports =
          '<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 32 33"><path d="M24.965 24.38h-18.132c-1.366 0-2.478-1.113-2.478-2.478v-11.806c0-1.364 1.111-2.478 2.478-2.478h18.132c1.366 0 2.478 1.113 2.478 2.478v11.806c0 1.364-1.11 2.478-2.478 2.478zM6.833 10.097v11.806h18.134l-0.002-11.806h-18.132zM2.478 28.928h5.952c0.684 0 1.238-0.554 1.238-1.239 0-0.684-0.554-1.238-1.238-1.238h-5.952v-5.802c0-0.684-0.554-1.239-1.238-1.239s-1.239 0.556-1.239 1.239v5.802c0 1.365 1.111 2.478 2.478 2.478zM30.761 19.412c-0.684 0-1.238 0.554-1.238 1.238v5.801h-5.951c-0.686 0-1.239 0.554-1.239 1.238 0 0.686 0.554 1.239 1.239 1.239h5.951c1.366 0 2.478-1.111 2.478-2.478v-5.801c0-0.683-0.554-1.238-1.239-1.238zM0 5.55v5.802c0 0.683 0.554 1.238 1.238 1.238s1.238-0.555 1.238-1.238v-5.802h5.952c0.684 0 1.238-0.554 1.238-1.238s-0.554-1.238-1.238-1.238h-5.951c-1.366-0.001-2.478 1.111-2.478 2.476zM32 11.35v-5.801c0-1.365-1.11-2.478-2.478-2.478h-5.951c-0.686 0-1.239 0.554-1.239 1.238s0.554 1.238 1.239 1.238h5.951v5.801c0 0.683 0.554 1.237 1.238 1.237 0.686 0.002 1.239-0.553 1.239-1.236z"></path></svg>'
      },
      574: n => {
        n.exports =
          '<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 32 33"><path d="M6.667 28h-5.333c-0.8 0-1.333-0.533-1.333-1.333v-5.333c0-0.8 0.533-1.333 1.333-1.333s1.333 0.533 1.333 1.333v4h4c0.8 0 1.333 0.533 1.333 1.333s-0.533 1.333-1.333 1.333zM30.667 28h-5.333c-0.8 0-1.333-0.533-1.333-1.333s0.533-1.333 1.333-1.333h4v-4c0-0.8 0.533-1.333 1.333-1.333s1.333 0.533 1.333 1.333v5.333c0 0.8-0.533 1.333-1.333 1.333zM30.667 12c-0.8 0-1.333-0.533-1.333-1.333v-4h-4c-0.8 0-1.333-0.533-1.333-1.333s0.533-1.333 1.333-1.333h5.333c0.8 0 1.333 0.533 1.333 1.333v5.333c0 0.8-0.533 1.333-1.333 1.333zM1.333 12c-0.8 0-1.333-0.533-1.333-1.333v-5.333c0-0.8 0.533-1.333 1.333-1.333h5.333c0.8 0 1.333 0.533 1.333 1.333s-0.533 1.333-1.333 1.333h-4v4c0 0.8-0.533 1.333-1.333 1.333z"></path></svg>'
      },
      182: n => {
        n.exports =
          '<svg version="1.1" viewBox="0 0 22 22"><svg x="7" y="1"><circle class="diplayer-loading-dot diplayer-loading-dot-0" cx="4" cy="4" r="2"></circle></svg><svg x="11" y="3"><circle class="diplayer-loading-dot diplayer-loading-dot-1" cx="4" cy="4" r="2"></circle></svg><svg x="13" y="7"><circle class="diplayer-loading-dot diplayer-loading-dot-2" cx="4" cy="4" r="2"></circle></svg><svg x="11" y="11"><circle class="diplayer-loading-dot diplayer-loading-dot-3" cx="4" cy="4" r="2"></circle></svg><svg x="7" y="13"><circle class="diplayer-loading-dot diplayer-loading-dot-4" cx="4" cy="4" r="2"></circle></svg><svg x="3" y="11"><circle class="diplayer-loading-dot diplayer-loading-dot-5" cx="4" cy="4" r="2"></circle></svg><svg x="1" y="7"><circle class="diplayer-loading-dot diplayer-loading-dot-6" cx="4" cy="4" r="2"></circle></svg><svg x="3" y="3"><circle class="diplayer-loading-dot diplayer-loading-dot-7" cx="4" cy="4" r="2"></circle></svg></svg>'
      },
      965: n => {
        n.exports =
          '<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 32 32"><path d="M19.357 2.88c1.749 0 3.366 0.316 4.851 0.946 1.485 0.632 2.768 1.474 3.845 2.533s1.922 2.279 2.532 3.661c0.611 1.383 0.915 2.829 0.915 4.334 0 1.425-0.304 2.847-0.915 4.271-0.611 1.425-1.587 2.767-2.928 4.028-0.855 0.813-1.811 1.607-2.869 2.38s-2.136 1.465-3.233 2.075c-1.099 0.61-2.198 1.098-3.296 1.465-1.098 0.366-2.115 0.549-3.051 0.549-1.343 0-2.441-0.438-3.296-1.311-0.854-0.876-1.281-2.41-1.281-4.608 0-0.366 0.020-0.773 0.060-1.221s0.062-0.895 0.062-1.343c0-0.773-0.183-1.353-0.55-1.738-0.366-0.387-0.793-0.58-1.281-0.58-0.652 0-1.21 0.295-1.678 0.886s-0.926 1.23-1.373 1.921c-0.447 0.693-0.905 1.334-1.372 1.923s-1.028 0.886-1.679 0.886c-0.529 0-1.048-0.427-1.556-1.282s-0.763-2.259-0.763-4.212c0-2.197 0.529-4.241 1.587-6.133s2.462-3.529 4.21-4.912c1.75-1.383 3.762-2.471 6.041-3.264 2.277-0.796 4.617-1.212 7.018-1.253zM7.334 15.817c0.569 0 1.047-0.204 1.434-0.611s0.579-0.875 0.579-1.404c0-0.569-0.193-1.047-0.579-1.434s-0.864-0.579-1.434-0.579c-0.529 0-0.987 0.193-1.373 0.579s-0.58 0.864-0.58 1.434c0 0.53 0.194 0.998 0.58 1.404 0.388 0.407 0.845 0.611 1.373 0.611zM12.216 11.79c0.691 0 1.292-0.254 1.8-0.763s0.762-1.107 0.762-1.8c0-0.732-0.255-1.343-0.762-1.831-0.509-0.489-1.109-0.732-1.8-0.732-0.732 0-1.342 0.244-1.831 0.732-0.488 0.488-0.732 1.098-0.732 1.831 0 0.693 0.244 1.292 0.732 1.8s1.099 0.763 1.831 0.763zM16.366 25.947c0.692 0 1.282-0.214 1.77-0.64s0.732-0.987 0.732-1.678-0.244-1.261-0.732-1.709c-0.489-0.448-1.078-0.671-1.77-0.671-0.65 0-1.21 0.223-1.678 0.671s-0.702 1.018-0.702 1.709c0 0.692 0.234 1.25 0.702 1.678s1.027 0.64 1.678 0.64zM19.113 9.592c0.651 0 1.129-0.203 1.433-0.611 0.305-0.406 0.459-0.874 0.459-1.404 0-0.488-0.154-0.947-0.459-1.373-0.304-0.427-0.782-0.641-1.433-0.641-0.529 0-1.008 0.193-1.434 0.58s-0.64 0.865-0.64 1.434c0 0.571 0.213 1.049 0.64 1.434 0.427 0.389 0.905 0.581 1.434 0.581zM24.848 12.826c0.57 0 1.067-0.213 1.495-0.64 0.427-0.427 0.64-0.947 0.64-1.556 0-0.57-0.214-1.068-0.64-1.495-0.428-0.427-0.927-0.64-1.495-0.64-0.611 0-1.129 0.213-1.555 0.64-0.428 0.427-0.642 0.926-0.642 1.495 0 0.611 0.213 1.129 0.642 1.556s0.947 0.64 1.555 0.64z"></path></svg>'
      },
      74: n => {//暂停图标
        n.exports =
          '<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 17 32"><path d="M14.080 4.8q2.88 0 2.88 2.048v18.24q0 2.112-2.88 2.112t-2.88-2.112v-18.24q0-2.048 2.88-2.048zM2.88 4.8q2.88 0 2.88 2.048v18.24q0 2.112-2.88 2.112t-2.88-2.112v-18.24q0-2.048 2.88-2.048z"></path></svg>'
      },
      730: n => {//播放图标
        n.exports =
          '<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 16 32"><path d="M15.552 15.168q0.448 0.32 0.448 0.832 0 0.448-0.448 0.768l-13.696 8.512q-0.768 0.512-1.312 0.192t-0.544-1.28v-16.448q0-0.96 0.544-1.28t1.312 0.192z"></path></svg>'
      },
      428: n => {
        n.exports =
          '<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 32 32"><path d="M22 16l-10.105-10.6-1.895 1.987 8.211 8.613-8.211 8.612 1.895 1.988 8.211-8.613z"></path></svg>'
      },
      254: n => {
        n.exports =
          '<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 32 32"><path d="M13.725 30l3.9-5.325-3.9-1.125v6.45zM0 17.5l11.050 3.35 13.6-11.55-10.55 12.425 11.8 3.65 6.1-23.375-32 15.5z"></path></svg>'
      },
      934: n => {
        n.exports =
          '<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 32 28"><path d="M28.633 17.104c0.035 0.21 0.026 0.463-0.026 0.76s-0.14 0.598-0.262 0.904c-0.122 0.306-0.271 0.581-0.445 0.825s-0.367 0.419-0.576 0.524c-0.209 0.105-0.393 0.157-0.55 0.157s-0.332-0.035-0.524-0.105c-0.175-0.052-0.393-0.1-0.655-0.144s-0.528-0.052-0.799-0.026c-0.271 0.026-0.541 0.083-0.812 0.17s-0.502 0.236-0.694 0.445c-0.419 0.437-0.664 0.934-0.734 1.493s0.009 1.092 0.236 1.598c0.175 0.349 0.148 0.699-0.079 1.048-0.105 0.14-0.271 0.284-0.498 0.432s-0.476 0.284-0.747 0.406-0.555 0.218-0.851 0.288c-0.297 0.070-0.559 0.105-0.786 0.105-0.157 0-0.306-0.061-0.445-0.183s-0.236-0.253-0.288-0.393h-0.026c-0.192-0.541-0.52-1.009-0.982-1.402s-1-0.589-1.611-0.589c-0.594 0-1.131 0.197-1.611 0.589s-0.816 0.851-1.009 1.375c-0.087 0.21-0.218 0.362-0.393 0.458s-0.367 0.144-0.576 0.144c-0.244 0-0.52-0.044-0.825-0.131s-0.611-0.197-0.917-0.327c-0.306-0.131-0.581-0.284-0.825-0.458s-0.428-0.349-0.55-0.524c-0.087-0.122-0.135-0.266-0.144-0.432s0.057-0.397 0.197-0.694c0.192-0.402 0.266-0.86 0.223-1.375s-0.266-0.991-0.668-1.428c-0.244-0.262-0.541-0.432-0.891-0.511s-0.681-0.109-0.995-0.092c-0.367 0.017-0.742 0.087-1.127 0.21-0.244 0.070-0.489 0.052-0.734-0.052-0.192-0.070-0.371-0.231-0.537-0.485s-0.314-0.533-0.445-0.838c-0.131-0.306-0.231-0.62-0.301-0.943s-0.087-0.59-0.052-0.799c0.052-0.384 0.227-0.629 0.524-0.734 0.524-0.21 0.995-0.555 1.415-1.035s0.629-1.017 0.629-1.611c0-0.611-0.21-1.144-0.629-1.598s-0.891-0.786-1.415-0.996c-0.157-0.052-0.288-0.179-0.393-0.38s-0.157-0.406-0.157-0.616c0-0.227 0.035-0.48 0.105-0.76s0.162-0.55 0.275-0.812 0.244-0.502 0.393-0.72c0.148-0.218 0.31-0.38 0.485-0.485 0.14-0.087 0.275-0.122 0.406-0.105s0.275 0.052 0.432 0.105c0.524 0.21 1.070 0.275 1.637 0.197s1.070-0.327 1.506-0.747c0.21-0.209 0.362-0.467 0.458-0.773s0.157-0.607 0.183-0.904c0.026-0.297 0.026-0.568 0-0.812s-0.048-0.419-0.065-0.524c-0.035-0.105-0.066-0.227-0.092-0.367s-0.013-0.262 0.039-0.367c0.105-0.244 0.293-0.458 0.563-0.642s0.563-0.336 0.878-0.458c0.314-0.122 0.62-0.214 0.917-0.275s0.533-0.092 0.707-0.092c0.227 0 0.406 0.074 0.537 0.223s0.223 0.301 0.275 0.458c0.192 0.471 0.507 0.886 0.943 1.244s0.952 0.537 1.546 0.537c0.611 0 1.153-0.17 1.624-0.511s0.803-0.773 0.996-1.297c0.070-0.14 0.179-0.284 0.327-0.432s0.301-0.223 0.458-0.223c0.244 0 0.511 0.035 0.799 0.105s0.572 0.166 0.851 0.288c0.279 0.122 0.537 0.279 0.773 0.472s0.423 0.402 0.563 0.629c0.087 0.14 0.113 0.293 0.079 0.458s-0.070 0.284-0.105 0.354c-0.227 0.506-0.297 1.039-0.21 1.598s0.341 1.048 0.76 1.467c0.419 0.419 0.934 0.651 1.546 0.694s1.179-0.057 1.703-0.301c0.14-0.087 0.31-0.122 0.511-0.105s0.371 0.096 0.511 0.236c0.262 0.244 0.493 0.616 0.694 1.113s0.336 1 0.406 1.506c0.035 0.297-0.013 0.528-0.144 0.694s-0.266 0.275-0.406 0.327c-0.542 0.192-1.004 0.528-1.388 1.009s-0.576 1.026-0.576 1.637c0 0.594 0.162 1.113 0.485 1.559s0.747 0.764 1.27 0.956c0.122 0.070 0.227 0.14 0.314 0.21 0.192 0.157 0.323 0.358 0.393 0.602v0zM16.451 19.462c0.786 0 1.528-0.149 2.227-0.445s1.305-0.707 1.821-1.231c0.515-0.524 0.921-1.131 1.218-1.821s0.445-1.428 0.445-2.214c0-0.786-0.148-1.524-0.445-2.214s-0.703-1.292-1.218-1.808c-0.515-0.515-1.122-0.921-1.821-1.218s-1.441-0.445-2.227-0.445c-0.786 0-1.524 0.148-2.214 0.445s-1.292 0.703-1.808 1.218c-0.515 0.515-0.921 1.118-1.218 1.808s-0.445 1.428-0.445 2.214c0 0.786 0.149 1.524 0.445 2.214s0.703 1.297 1.218 1.821c0.515 0.524 1.118 0.934 1.808 1.231s1.428 0.445 2.214 0.445v0z"></path></svg>'
      },
      410: n => {
        n.exports =
          '<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 32 32"><path d="M26.667 5.333h-21.333c-0 0-0.001 0-0.001 0-1.472 0-2.666 1.194-2.666 2.666 0 0 0 0.001 0 0.001v-0 16c0 0 0 0.001 0 0.001 0 1.472 1.194 2.666 2.666 2.666 0 0 0.001 0 0.001 0h21.333c0 0 0.001 0 0.001 0 1.472 0 2.666-1.194 2.666-2.666 0-0 0-0.001 0-0.001v0-16c0-0 0-0.001 0-0.001 0-1.472-1.194-2.666-2.666-2.666-0 0-0.001 0-0.001 0h0zM5.333 16h5.333v2.667h-5.333v-2.667zM18.667 24h-13.333v-2.667h13.333v2.667zM26.667 24h-5.333v-2.667h5.333v2.667zM26.667 18.667h-13.333v-2.667h13.333v2.667z"></path></svg>'
      },
      644: n => {
        n.exports =
          '<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 21 32"><path d="M13.728 6.272v19.456q0 0.448-0.352 0.8t-0.8 0.32-0.8-0.32l-5.952-5.952h-4.672q-0.48 0-0.8-0.352t-0.352-0.8v-6.848q0-0.48 0.352-0.8t0.8-0.352h4.672l5.952-5.952q0.32-0.32 0.8-0.32t0.8 0.32 0.352 0.8zM20.576 16q0 1.344-0.768 2.528t-2.016 1.664q-0.16 0.096-0.448 0.096-0.448 0-0.8-0.32t-0.32-0.832q0-0.384 0.192-0.64t0.544-0.448 0.608-0.384 0.512-0.64 0.192-1.024-0.192-1.024-0.512-0.64-0.608-0.384-0.544-0.448-0.192-0.64q0-0.48 0.32-0.832t0.8-0.32q0.288 0 0.448 0.096 1.248 0.48 2.016 1.664t0.768 2.528z"></path></svg>'
      },
      324: n => {
        n.exports =
          '<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 21 32"><path d="M13.728 6.272v19.456q0 0.448-0.352 0.8t-0.8 0.32-0.8-0.32l-5.952-5.952h-4.672q-0.48 0-0.8-0.352t-0.352-0.8v-6.848q0-0.48 0.352-0.8t0.8-0.352h4.672l5.952-5.952q0.32-0.32 0.8-0.32t0.8 0.32 0.352 0.8z"></path></svg>'
      },
      437: n => {
        n.exports =
          '<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 21 32"><path d="M13.728 6.272v19.456q0 0.448-0.352 0.8t-0.8 0.32-0.8-0.32l-5.952-5.952h-4.672q-0.48 0-0.8-0.352t-0.352-0.8v-6.848q0-0.48 0.352-0.8t0.8-0.352h4.672l5.952-5.952q0.32-0.32 0.8-0.32t0.8 0.32 0.352 0.8zM20.576 16q0 1.344-0.768 2.528t-2.016 1.664q-0.16 0.096-0.448 0.096-0.448 0-0.8-0.32t-0.32-0.832q0-0.384 0.192-0.64t0.544-0.448 0.608-0.384 0.512-0.64 0.192-1.024-0.192-1.024-0.512-0.64-0.608-0.384-0.544-0.448-0.192-0.64q0-0.48 0.32-0.832t0.8-0.32q0.288 0 0.448 0.096 1.248 0.48 2.016 1.664t0.768 2.528zM25.152 16q0 2.72-1.536 5.056t-4 3.36q-0.256 0.096-0.448 0.096-0.48 0-0.832-0.352t-0.32-0.8q0-0.704 0.672-1.056 1.024-0.512 1.376-0.8 1.312-0.96 2.048-2.4t0.736-3.104-0.736-3.104-2.048-2.4q-0.352-0.288-1.376-0.8-0.672-0.352-0.672-1.056 0-0.448 0.32-0.8t0.8-0.352q0.224 0 0.48 0.096 2.496 1.056 4 3.36t1.536 5.056z"></path></svg>'
      },
      175: (n, e, t) => {
        "use strict";
        var a = "undefined" != typeof self ? self : "undefined" != typeof window ? window : void 0 !== t.g ? t
          .g : {},
          o = Object.create(a),
          r = /["&'<>]/;

        function i(n) {
          return "string" != typeof n && (n = null == n ? "" : "function" == typeof n ? i(n.call(n)) : JSON
            .stringify(n)), n
        }
        o.$escape = function(n) {
          return function(n) {
            var e = "" + n,
              t = r.exec(e);
            if (!t) return n;
            var a = "",
              o = void 0,
              i = void 0,
              l = void 0;
            for (o = t.index, i = 0; o < e.length; o++) {
              switch (e.charCodeAt(o)) {
                case 34:
                  l = "&#34;";
                  break;
                case 38:
                  l = "&#38;";
                  break;
                case 39:
                  l = "&#39;";
                  break;
                case 60:
                  l = "&#60;";
                  break;
                case 62:
                  l = "&#62;";
                  break;
                default:
                  continue
              }
              i !== o && (a += e.substring(i, o)), i = o + 1, a += l
            }
            return i !== o ? a + e.substring(i, o) : a
          }(i(n))
        }, o.$each = function(n, e) {
          if (Array.isArray(n))
            for (var t = 0, a = n.length; t < a; t++) e(n[t], t);
          else
            for (var o in n) e(n[o], o)
        }, n.exports = o
      },
      140: (n, e, t) => {
        "use strict";
        n.exports = t(175)
      },
      291: n => {
        "use strict";
        n.exports = function(n) {
          var e = [];
          return e.toString = function() {
            return this.map((function(e) {
              var t = "",
                a = void 0 !== e[5];
              return e[4] && (t += "@supports (".concat(e[4], ") {")), e[2] && (t += "@media ".concat(
                e[2], " {")), a && (t += "@layer".concat(e[5].length > 0 ? " ".concat(e[5]) : "",
                " {")), t += n(e), a && (t += "}"), e[2] && (t += "}"), e[4] && (t += "}"), t
            })).join("")
          }, e.i = function(n, t, a, o, r) {
            "string" == typeof n && (n = [
              [null, n, void 0]
            ]);
            var i = {};
            if (a)
              for (var l = 0; l < this.length; l++) {
                var s = this[l][0];
                null != s && (i[s] = !0)
              }
            for (var p = 0; p < n.length; p++) {
              var d = [].concat(n[p]);
              a && i[d[0]] || (void 0 !== r && (void 0 === d[5] || (d[1] = "@layer".concat(d[5].length > 0 ?
                " ".concat(d[5]) : "", " {").concat(d[1], "}")), d[5] = r), t && (d[2] ? (d[1] =
                "@media ".concat(d[2], " {").concat(d[1], "}"), d[2] = t) : d[2] = t), o && (d[4] ? (d[
                  1] = "@supports (".concat(d[4], ") {").concat(d[1], "}"), d[4] = o) : d[4] = ""
                .concat(o)), e.push(d))
            }
          }, e
        }
      },
      943: n => {
        "use strict";
        n.exports = function(n, e) {
          return e || (e = {}), n ? (n = String(n.__esModule ? n.default : n), /^['"].*['"]$/.test(n) && (n =
              n.slice(1, -1)), e.hash && (n += e.hash), /["'() \t\n]|(%20)/.test(n) || e.needQuotes ? '"'
            .concat(n.replace(/"/g, '\\"').replace(/\n/g, "\\n"), '"') : n) : n
        }
      },
      189: n => {
        "use strict";
        n.exports = function(n) {
          var e = n[1],
            t = n[3];
          if (!t) return e;
          if ("function" == typeof btoa) {
            var a = btoa(unescape(encodeURIComponent(JSON.stringify(t)))),
              o = "sourceMappingURL=data:application/json;charset=utf-8;base64,".concat(a),
              r = "/*# ".concat(o, " */");
            return [e].concat([r]).join("\n")
          }
          return [e].join("\n")
        }
      },
      831: n => {
        "use strict";
        n.exports =
          "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAADGCAYAAAAT+OqFAAAAdklEQVQoz42QQQ7AIAgEF/T/D+kbq/RWAlnQyyazA4aoAB4FsBSA/bFjuF1EOL7VbrIrBuusmrt4ZZORfb6ehbWdnRHEIiITaEUKa5EJqUakRSaEYBJSCY2dEstQY7AuxahwXFrvZmWl2rh4JZ07z9dLtesfNj5q0FU3A5ObbwAAAABJRU5ErkJggg=="
      }
    },
    e = {};

  function t(a) {
    var o = e[a];
    if (void 0 !== o) return o.exports;
    var r = e[a] = {
      id: a,
      exports: {}
    };
    return n[a](r, r.exports, t), r.exports
  }
  t.m = n, t.n = n => {
      var e = n && n.__esModule ? () => n.default : () => n;
      return t.d(e, {
        a: e
      }), e
    }, t.d = (n, e) => {
      for (var a in e) t.o(e, a) && !t.o(n, a) && Object.defineProperty(n, a, {
        enumerable: !0,
        get: e[a]
      })
    }, t.g = function() {
      if ("object" == typeof globalThis) return globalThis;
      try {
        return this || new Function("return this")()
      } catch (n) {
        if ("object" == typeof window) return window
      }
    }(), t.o = (n, e) => Object.prototype.hasOwnProperty.call(n, e), t.b = document.baseURI || self.location.href,
    t.nc = void 0;
  var a = {};
  return (() => {
    "use strict";
    t.d(a, {
      default: () => _a
    });
    var n = t(72),
      e = t.n(n),
      o = t(298),
      r = t.n(o),
      i = t(976),
      l = t.n(i),
      s = t(147),
      p = t.n(s),
      d = t(566),
      c = t.n(d),
      A = t(396),
      u = t.n(A),
      y = t(336),
      m = {};
    m.styleTagTransform = u(), m.setAttributes = p(), m.insert = l().bind(null, "head"), m.domAPI = r(), m
      .insertStyleElement = c(), e()(y.Z, m), y.Z && y.Z.locals && y.Z.locals;

    function h(n) {
      return h = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(n) {
        return typeof n
      } : function(n) {
        return n && "function" == typeof Symbol && n.constructor === Symbol && n !== Symbol.prototype ?
          "symbol" : typeof n
      }, h(n)
    }

    function f(n, e) {
      this.name = "AggregateError", this.errors = n, this.message = e || ""
    }
    f.prototype = Error.prototype;

    function b(n) {
      return b = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(n) {
        return typeof n
      } : function(n) {
        return n && "function" == typeof Symbol && n.constructor === Symbol && n !== Symbol.prototype ?
          "symbol" : typeof n
      }, b(n)
    }
    var g = setTimeout;

    function v(n) {
      return Boolean(n && void 0 !== n.length)
    }

    function x() {}

    function E(n) {
      if (!(this instanceof E)) throw new TypeError("Promises must be constructed via new");
      if ("function" != typeof n) throw new TypeError("not a function");
      this._state = 0, this._handled = !1, this._value = void 0, this._deferreds = [], I(n, this)
    }

    function w(n, e) {
      for (; 3 === n._state;) n = n._value;
      0 !== n._state ? (n._handled = !0, E._immediateFn((function() {
        var t = 1 === n._state ? e.onFulfilled : e.onRejected;
        if (null !== t) {
          var a;
          try {
            a = t(n._value)
          } catch (n) {
            return void k(e.promise, n)
          }
          C(e.promise, a)
        } else(1 === n._state ? C : k)(e.promise, n._value)
      }))) : n._deferreds.push(e)
    }

    function C(n, e) {
      try {
        if (e === n) throw new TypeError("A promise cannot be resolved with itself.");
        if (e && ("object" === b(e) || "function" == typeof e)) {
          var t = e.then;
          if (e instanceof E) return n._state = 3, n._value = e, void B(n);
          if ("function" == typeof t) return void I((a = t, o = e, function() {
            a.apply(o, arguments)
          }), n)
        }
        n._state = 1, n._value = e, B(n)
      } catch (e) {
        k(n, e)
      }
      var a, o
    }

    function k(n, e) {
      n._state = 2, n._value = e, B(n)
    }

    function B(n) {
      2 === n._state && 0 === n._deferreds.length && E._immediateFn((function() {
        n._handled || E._unhandledRejectionFn(n._value)
      }));
      for (var e = 0, t = n._deferreds.length; e < t; e++) w(n, n._deferreds[e]);
      n._deferreds = null
    }

    function S(n, e, t) {
      this.onFulfilled = "function" == typeof n ? n : null, this.onRejected = "function" == typeof e ? e :
        null, this.promise = t
    }

    function I(n, e) {
      var t = !1;
      try {
        n((function(n) {
          t || (t = !0, C(e, n))
        }), (function(n) {
          t || (t = !0, k(e, n))
        }))
      } catch (n) {
        if (t) return;
        t = !0, k(e, n)
      }
    }
    E.prototype.catch = function(n) {
      return this.then(null, n)
    }, E.prototype.then = function(n, e) {
      var t = new this.constructor(x);
      return w(this, new S(n, e, t)), t
    }, E.prototype.finally = function(n) {
      var e = this.constructor;
      return this.then((function(t) {
        return e.resolve(n()).then((function() {
          return t
        }))
      }), (function(t) {
        return e.resolve(n()).then((function() {
          return e.reject(t)
        }))
      }))
    }, E.all = function(n) {
      return new E((function(e, t) {
        if (!v(n)) return t(new TypeError("Promise.all accepts an array"));
        var a = Array.prototype.slice.call(n);
        if (0 === a.length) return e([]);
        var o = a.length;

        function r(n, i) {
          try {
            if (i && ("object" === b(i) || "function" == typeof i)) {
              var l = i.then;
              if ("function" == typeof l) return void l.call(i, (function(e) {
                r(n, e)
              }), t)
            }
            a[n] = i, 0 == --o && e(a)
          } catch (n) {
            t(n)
          }
        }
        for (var i = 0; i < a.length; i++) r(i, a[i])
      }))
    }, E.any = function(n) {
      var e = this;
      return new e((function(t, a) {
        if (!n || void 0 === n.length) return a(new TypeError("Promise.any accepts an array"));
        var o = Array.prototype.slice.call(n);
        if (0 === o.length) return a();
        for (var r = [], i = 0; i < o.length; i++) try {
          e.resolve(o[i]).then(t).catch((function(n) {
            r.push(n), r.length === o.length && a(new f(r, "All promises were rejected"))
          }))
        } catch (n) {
          a(n)
        }
      }))
    }, E.allSettled = function(n) {
      return new this((function(e, t) {
        if (!n || void 0 === n.length) return t(new TypeError(h(n) + " " + n +
          " is not iterable(cannot read property Symbol(Symbol.iterator))"));
        var a = Array.prototype.slice.call(n);
        if (0 === a.length) return e([]);
        var o = a.length;

        function r(n, t) {
          if (t && ("object" === h(t) || "function" == typeof t)) {
            var i = t.then;
            if ("function" == typeof i) return void i.call(t, (function(e) {
              r(n, e)
            }), (function(t) {
              a[n] = {
                status: "rejected",
                reason: t
              }, 0 == --o && e(a)
            }))
          }
          a[n] = {
            status: "fulfilled",
            value: t
          }, 0 == --o && e(a)
        }
        for (var i = 0; i < a.length; i++) r(i, a[i])
      }))
    }, E.resolve = function(n) {
      return n && "object" === b(n) && n.constructor === E ? n : new E((function(e) {
        e(n)
      }))
    }, E.reject = function(n) {
      return new E((function(e, t) {
        t(n)
      }))
    }, E.race = function(n) {
      return new E((function(e, t) {
        if (!v(n)) return t(new TypeError("Promise.race accepts an array"));
        for (var a = 0, o = n.length; a < o; a++) E.resolve(n[a]).then(e, t)
      }))
    }, E._immediateFn = "function" == typeof setImmediate && function(n) {
      setImmediate(n)
    } || function(n) {
      g(n, 0)
    }, E._unhandledRejectionFn = function(n) {
      "undefined" != typeof console && console && console.warn("Possible Unhandled Promise Rejection:", n)
    };
    const z = E;
    var T = /mobile/i.test(window.navigator.userAgent);
    const q = {
      secondToTime: function(n) {
        if (0 === (n = n || 0) || n === 1 / 0 || "NaN" === n.toString()) return "00:00";
        var e = Math.floor(n / 3600),
          t = Math.floor((n - 3600 * e) / 60),
          a = Math.floor(n - 3600 * e - 60 * t);
        return (e > 0 ? [e, t, a] : [t, a]).map((function(n) {
          return n < 10 ? "0" + n : "" + n
        })).join(":")
      },
      getElementViewLeft: function(n) {
        var e = n.offsetLeft,
          t = n.offsetParent,
          a = document.body.scrollLeft + document.documentElement.scrollLeft;
        if (document.fullscreenElement || document.mozFullScreenElement || document
          .webkitFullscreenElement)
          for (; null !== t && t !== n;) e += t.offsetLeft, t = t.offsetParent;
        else
          for (; null !== t;) e += t.offsetLeft, t = t.offsetParent;
        return e - a
      },
      getBoundingClientRectViewLeft: function(n) {
        var e = window.scrollY || window.pageYOffset || document.body.scrollTop + (document
          .documentElement && document.documentElement.scrollTop || 0);
        if (n.getBoundingClientRect) {
          if ("number" != typeof this.getBoundingClientRectViewLeft.offset) {
            var t = document.createElement("div");
            t.style.cssText = "position:absolute;top:0;left:0;", document.body.appendChild(t), this
              .getBoundingClientRectViewLeft.offset = -t.getBoundingClientRect().top - e, document.body
              .removeChild(t), t = null
          }
          var a = n.getBoundingClientRect(),
            o = this.getBoundingClientRectViewLeft.offset;
          return a.left + o
        }
        return this.getElementViewLeft(n)
      },
      getScrollPosition: function() {
        return {
          left: window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft ||
            0,
          top: window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0
        }
      },
      setScrollPosition: function(n) {
        var e = n.left,
          t = void 0 === e ? 0 : e,
          a = n.top,
          o = void 0 === a ? 0 : a;
        this.isFirefox ? (document.documentElement.scrollLeft = t, document.documentElement.scrollTop =
          o) : window.scrollTo(t, o)
      },
      isMobile: T,
      isFirefox: /firefox/i.test(window.navigator.userAgent),
      isChrome: /chrome/i.test(window.navigator.userAgent),
      isSafari: /safari/i.test(window.navigator.userAgent),
      storage: {
        set: function(n, e) {
          localStorage.setItem(n, e)
        },
        get: function(n) {
          return localStorage.getItem(n)
        }
      },
      nameMap: {
        dragStart: T ? "touchstart" : "mousedown",
        dragMove: T ? "touchmove" : "mousemove",
        dragEnd: T ? "touchend" : "mouseup"
      },
      color2Number: function(n) {
        return "#" === n[0] && (n = n.substr(1)), 3 === n.length && (n = "".concat(n[0]).concat(n[0])
          .concat(n[1]).concat(n[1]).concat(n[2]).concat(n[2])), parseInt(n, 16) + 0 & 16777215
      },
      number2Color: function(n) {
        return "#" + ("00000" + n.toString(16)).slice(-6)
      },
      number2Type: function(n) {
        switch (n) {
          case 0:
          default:
            return "right";
          case 1:
            return "top";
          case 2:
            return "bottom"
        }
      }
    };

    function L(n, e) {
      return function() {
        return n.apply(e, arguments)
      }
    }

    function O(n) {
      return O = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(n) {
        return typeof n
      } : function(n) {
        return n && "function" == typeof Symbol && n.constructor === Symbol && n !== Symbol.prototype ?
          "symbol" : typeof n
      }, O(n)
    }
    var j, D = Object.prototype.toString,
      P = Object.getPrototypeOf,
      R = (j = Object.create(null), function(n) {
        var e = D.call(n);
        return j[e] || (j[e] = e.slice(8, -1).toLowerCase())
      }),
      Y = function(n) {
        return n = n.toLowerCase(),
          function(e) {
            return R(e) === n
          }
      },
      M = function(n) {
        return function(e) {
          return O(e) === n
        }
      },
      F = Array.isArray,
      W = M("undefined"),
      U = Y("ArrayBuffer"),
      N = M("string"),
      J = M("function"),
      H = M("number"),
      Q = function(n) {
        return null !== n && "object" === O(n)
      },
      _ = function(n) {
        if ("object" !== R(n)) return !1;
        var e = P(n);
        return !(null !== e && e !== Object.prototype && null !== Object.getPrototypeOf(e) || Symbol
          .toStringTag in n || Symbol.iterator in n)
      },
      Z = Y("Date"),
      V = Y("File"),
      X = Y("Blob"),
      K = Y("FileList"),
      G = Y("URLSearchParams");

    function $(n, e) {
      var t, a, o = (arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {}).allOwnKeys,
        r = void 0 !== o && o;
      if (null != n)
        if ("object" !== O(n) && (n = [n]), F(n))
          for (t = 0, a = n.length; t < a; t++) e.call(null, n[t], t, n);
        else {
          var i, l = r ? Object.getOwnPropertyNames(n) : Object.keys(n),
            s = l.length;
          for (t = 0; t < s; t++) i = l[t], e.call(null, n[i], i, n)
        }
    }

    function nn(n, e) {
      e = e.toLowerCase();
      for (var t, a = Object.keys(n), o = a.length; o-- > 0;)
        if (e === (t = a[o]).toLowerCase()) return t;
      return null
    }
    var en, tn, an = "undefined" != typeof globalThis ? globalThis : "undefined" != typeof self ? self :
      "undefined" != typeof window ? window : global,
      on = function(n) {
        return !W(n) && n !== an
      },
      rn = (en = "undefined" != typeof Uint8Array && P(Uint8Array), function(n) {
        return en && n instanceof en
      }),
      ln = Y("HTMLFormElement"),
      sn = (tn = Object.prototype.hasOwnProperty, function(n, e) {
        return tn.call(n, e)
      }),
      pn = Y("RegExp"),
      dn = function(n, e) {
        var t = Object.getOwnPropertyDescriptors(n),
          a = {};
        $(t, (function(t, o) {
          !1 !== e(t, o, n) && (a[o] = t)
        })), Object.defineProperties(n, a)
      },
      cn = "abcdefghijklmnopqrstuvwxyz",
      An = "0123456789",
      un = {
        DIGIT: An,
        ALPHA: cn,
        ALPHA_DIGIT: cn + cn.toUpperCase() + An
      },
      yn = Y("AsyncFunction");
    const mn = {
      isArray: F,
      isArrayBuffer: U,
      isBuffer: function(n) {
        return null !== n && !W(n) && null !== n.constructor && !W(n.constructor) && J(n.constructor
          .isBuffer) && n.constructor.isBuffer(n)
      },
      isFormData: function(n) {
        var e;
        return n && ("function" == typeof FormData && n instanceof FormData || J(n.append) && (
          "formdata" === (e = R(n)) || "object" === e && J(n.toString) && "[object FormData]" === n
          .toString()))
      },
      isArrayBufferView: function(n) {
        return "undefined" != typeof ArrayBuffer && ArrayBuffer.isView ? ArrayBuffer.isView(n) : n && n
          .buffer && U(n.buffer)
      },
      isString: N,
      isNumber: H,
      isBoolean: function(n) {
        return !0 === n || !1 === n
      },
      isObject: Q,
      isPlainObject: _,
      isUndefined: W,
      isDate: Z,
      isFile: V,
      isBlob: X,
      isRegExp: pn,
      isFunction: J,
      isStream: function(n) {
        return Q(n) && J(n.pipe)
      },
      isURLSearchParams: G,
      isTypedArray: rn,
      isFileList: K,
      forEach: $,
      merge: function n() {
        for (var e = (on(this) && this || {}).caseless, t = {}, a = function(a, o) {
            var r = e && nn(t, o) || o;
            _(t[r]) && _(a) ? t[r] = n(t[r], a) : _(a) ? t[r] = n({}, a) : F(a) ? t[r] = a.slice() : t[
              r] = a
          }, o = 0, r = arguments.length; o < r; o++) arguments[o] && $(arguments[o], a);
        return t
      },
      extend: function(n, e, t) {
        return $(e, (function(e, a) {
          t && J(e) ? n[a] = L(e, t) : n[a] = e
        }), {
          allOwnKeys: (arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : {}).allOwnKeys
        }), n
      },
      trim: function(n) {
        return n.trim ? n.trim() : n.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g, "")
      },
      stripBOM: function(n) {
        return 65279 === n.charCodeAt(0) && (n = n.slice(1)), n
      },
      inherits: function(n, e, t, a) {
        n.prototype = Object.create(e.prototype, a), n.prototype.constructor = n, Object.defineProperty(n,
          "super", {
            value: e.prototype
          }), t && Object.assign(n.prototype, t)
      },
      toFlatObject: function(n, e, t, a) {
        var o, r, i, l = {};
        if (e = e || {}, null == n) return e;
        do {
          for (r = (o = Object.getOwnPropertyNames(n)).length; r-- > 0;) i = o[r], a && !a(i, n, e) || l[
            i] || (e[i] = n[i], l[i] = !0);
          n = !1 !== t && P(n)
        } while (n && (!t || t(n, e)) && n !== Object.prototype);
        return e
      },
      kindOf: R,
      kindOfTest: Y,
      endsWith: function(n, e, t) {
        n = String(n), (void 0 === t || t > n.length) && (t = n.length), t -= e.length;
        var a = n.indexOf(e, t);
        return -1 !== a && a === t
      },
      toArray: function(n) {
        if (!n) return null;
        if (F(n)) return n;
        var e = n.length;
        if (!H(e)) return null;
        for (var t = new Array(e); e-- > 0;) t[e] = n[e];
        return t
      },
      forEachEntry: function(n, e) {
        for (var t, a = (n && n[Symbol.iterator]).call(n);
          (t = a.next()) && !t.done;) {
          var o = t.value;
          e.call(n, o[0], o[1])
        }
      },
      matchAll: function(n, e) {
        for (var t, a = []; null !== (t = n.exec(e));) a.push(t);
        return a
      },
      isHTMLForm: ln,
      hasOwnProperty: sn,
      hasOwnProp: sn,
      reduceDescriptors: dn,
      freezeMethods: function(n) {
        dn(n, (function(e, t) {
          if (J(n) && -1 !== ["arguments", "caller", "callee"].indexOf(t)) return !1;
          var a = n[t];
          J(a) && (e.enumerable = !1, "writable" in e ? e.writable = !1 : e.set || (e.set =
            function() {
              throw Error("Can not rewrite read-only method '" + t + "'")
            }))
        }))
      },
      toObjectSet: function(n, e) {
        var t = {},
          a = function(n) {
            n.forEach((function(n) {
              t[n] = !0
            }))
          };
        return F(n) ? a(n) : a(String(n).split(e)), t
      },
      toCamelCase: function(n) {
        return n.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g, (function(n, e, t) {
          return e.toUpperCase() + t
        }))
      },
      noop: function() {},
      toFiniteNumber: function(n, e) {
        return n = +n, Number.isFinite(n) ? n : e
      },
      findKey: nn,
      global: an,
      isContextDefined: on,
      ALPHABET: un,
      generateString: function() {
        for (var n = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 16, e = arguments
            .length > 1 && void 0 !== arguments[1] ? arguments[1] : un.ALPHA_DIGIT, t = "", a = e
            .length; n--;) t += e[Math.random() * a | 0];
        return t
      },
      isSpecCompliantForm: function(n) {
        return !!(n && J(n.append) && "FormData" === n[Symbol.toStringTag] && n[Symbol.iterator])
      },
      toJSONObject: function(n) {
        var e = new Array(10);
        return function n(t, a) {
          if (Q(t)) {
            if (e.indexOf(t) >= 0) return;
            if (!("toJSON" in t)) {
              e[a] = t;
              var o = F(t) ? [] : {};
              return $(t, (function(e, t) {
                var r = n(e, a + 1);
                !W(r) && (o[t] = r)
              })), e[a] = void 0, o
            }
          }
          return t
        }(n, 0)
      },
      isAsyncFn: yn,
      isThenable: function(n) {
        return n && (Q(n) || J(n)) && J(n.then) && J(n.catch)
      }
    };

    function hn(n, e, t, a, o) {
      Error.call(this), Error.captureStackTrace ? Error.captureStackTrace(this, this.constructor) : this
        .stack = (new Error).stack, this.message = n, this.name = "AxiosError", e && (this.code = e), t && (
          this.config = t), a && (this.request = a), o && (this.response = o)
    }
    mn.inherits(hn, Error, {
      toJSON: function() {
        return {
          message: this.message,
          name: this.name,
          description: this.description,
          number: this.number,
          fileName: this.fileName,
          lineNumber: this.lineNumber,
          columnNumber: this.columnNumber,
          stack: this.stack,
          config: mn.toJSONObject(this.config),
          code: this.code,
          status: this.response && this.response.status ? this.response.status : null
        }
      }
    });
    var fn = hn.prototype,
      bn = {};
    ["ERR_BAD_OPTION_VALUE", "ERR_BAD_OPTION", "ECONNABORTED", "ETIMEDOUT", "ERR_NETWORK",
      "ERR_FR_TOO_MANY_REDIRECTS", "ERR_DEPRECATED", "ERR_BAD_RESPONSE", "ERR_BAD_REQUEST", "ERR_CANCELED",
      "ERR_NOT_SUPPORT", "ERR_INVALID_URL"
    ].forEach((function(n) {
      bn[n] = {
        value: n
      }
    })), Object.defineProperties(hn, bn), Object.defineProperty(fn, "isAxiosError", {
      value: !0
    }), hn.from = function(n, e, t, a, o, r) {
      var i = Object.create(fn);
      return mn.toFlatObject(n, i, (function(n) {
        return n !== Error.prototype
      }), (function(n) {
        return "isAxiosError" !== n
      })), hn.call(i, n.message, e, t, a, o), i.cause = n, i.name = n.name, r && Object.assign(i, r), i
    };
    const gn = hn;

    function vn(n) {
      return vn = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(n) {
        return typeof n
      } : function(n) {
        return n && "function" == typeof Symbol && n.constructor === Symbol && n !== Symbol.prototype ?
          "symbol" : typeof n
      }, vn(n)
    }

    function xn(n) {
      return mn.isPlainObject(n) || mn.isArray(n)
    }

    function En(n) {
      return mn.endsWith(n, "[]") ? n.slice(0, -2) : n
    }

    function wn(n, e, t) {
      return n ? n.concat(e).map((function(n, e) {
        return n = En(n), !t && e ? "[" + n + "]" : n
      })).join(t ? "." : "") : e
    }
    var Cn = mn.toFlatObject(mn, {}, null, (function(n) {
      return /^is[A-Z]/.test(n)
    }));
    const kn = function(n, e, t) {
      if (!mn.isObject(n)) throw new TypeError("target must be an object");
      e = e || new FormData;
      var a = (t = mn.toFlatObject(t, {
          metaTokens: !0,
          dots: !1,
          indexes: !1
        }, !1, (function(n, e) {
          return !mn.isUndefined(e[n])
        }))).metaTokens,
        o = t.visitor || p,
        r = t.dots,
        i = t.indexes,
        l = (t.Blob || "undefined" != typeof Blob && Blob) && mn.isSpecCompliantForm(e);
      if (!mn.isFunction(o)) throw new TypeError("visitor must be a function");

      function s(n) {
        if (null === n) return "";
        if (mn.isDate(n)) return n.toISOString();
        if (!l && mn.isBlob(n)) throw new gn("Blob is not supported. Use a Buffer instead.");
        return mn.isArrayBuffer(n) || mn.isTypedArray(n) ? l && "function" == typeof Blob ? new Blob([n]) :
          Buffer.from(n) : n
      }

      function p(n, t, o) {
        var l = n;
        if (n && !o && "object" === vn(n))
          if (mn.endsWith(t, "{}")) t = a ? t : t.slice(0, -2), n = JSON.stringify(n);
          else if (mn.isArray(n) && function(n) {
            return mn.isArray(n) && !n.some(xn)
          }(n) || (mn.isFileList(n) || mn.endsWith(t, "[]")) && (l = mn.toArray(n))) return t = En(t), l
          .forEach((function(n, a) {
            !mn.isUndefined(n) && null !== n && e.append(!0 === i ? wn([t], a, r) : null === i ? t :
              t + "[]", s(n))
          })), !1;
        return !!xn(n) || (e.append(wn(o, t, r), s(n)), !1)
      }
      var d = [],
        c = Object.assign(Cn, {
          defaultVisitor: p,
          convertValue: s,
          isVisitable: xn
        });
      if (!mn.isObject(n)) throw new TypeError("data must be an object");
      return function n(t, a) {
        if (!mn.isUndefined(t)) {
          if (-1 !== d.indexOf(t)) throw Error("Circular reference detected in " + a.join("."));
          d.push(t), mn.forEach(t, (function(t, r) {
            !0 === (!(mn.isUndefined(t) || null === t) && o.call(e, t, mn.isString(r) ? r.trim() :
              r, a, c)) && n(t, a ? a.concat(r) : [r])
          })), d.pop()
        }
      }(n), e
    };

    function Bn(n) {
      var e = {
        "!": "%21",
        "'": "%27",
        "(": "%28",
        ")": "%29",
        "~": "%7E",
        "%20": "+",
        "%00": "\0"
      };
      return encodeURIComponent(n).replace(/[!'()~]|%20|%00/g, (function(n) {
        return e[n]
      }))
    }

    function Sn(n, e) {
      this._pairs = [], n && kn(n, this, e)
    }
    var In = Sn.prototype;
    In.append = function(n, e) {
      this._pairs.push([n, e])
    }, In.toString = function(n) {
      var e = n ? function(e) {
        return n.call(this, e, Bn)
      } : Bn;
      return this._pairs.map((function(n) {
        return e(n[0]) + "=" + e(n[1])
      }), "").join("&")
    };
    const zn = Sn;

    function Tn(n) {
      return encodeURIComponent(n).replace(/%3A/gi, ":").replace(/%24/g, "$").replace(/%2C/gi, ",").replace(
        /%20/g, "+").replace(/%5B/gi, "[").replace(/%5D/gi, "]")
    }

    function qn(n, e, t) {
      if (!e) return n;
      var a, o = t && t.encode || Tn,
        r = t && t.serialize;
      if (a = r ? r(e, t) : mn.isURLSearchParams(e) ? e.toString() : new zn(e, t).toString(o)) {
        var i = n.indexOf("#"); - 1 !== i && (n = n.slice(0, i)), n += (-1 === n.indexOf("?") ? "?" : "&") + a
      }
      return n
    }

    function Ln(n) {
      return Ln = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(n) {
        return typeof n
      } : function(n) {
        return n && "function" == typeof Symbol && n.constructor === Symbol && n !== Symbol.prototype ?
          "symbol" : typeof n
      }, Ln(n)
    }

    function On(n, e) {
      for (var t = 0; t < e.length; t++) {
        var a = e[t];
        a.enumerable = a.enumerable || !1, a.configurable = !0, "value" in a && (a.writable = !0), Object
          .defineProperty(n, (void 0, o = function(n, e) {
            if ("object" !== Ln(n) || null === n) return n;
            var t = n[Symbol.toPrimitive];
            if (void 0 !== t) {
              var a = t.call(n, "string");
              if ("object" !== Ln(a)) return a;
              throw new TypeError("@@toPrimitive must return a primitive value.")
            }
            return String(n)
          }(a.key), "symbol" === Ln(o) ? o : String(o)), a)
      }
      var o
    }
    var jn = function() {
      function n() {
        ! function(n, e) {
          if (!(n instanceof e)) throw new TypeError("Cannot call a class as a function")
        }(this, n), this.handlers = []
      }
      var e, t;
      return e = n, t = [{
        key: "use",
        value: function(n, e, t) {
          return this.handlers.push({
            fulfilled: n,
            rejected: e,
            synchronous: !!t && t.synchronous,
            runWhen: t ? t.runWhen : null
          }), this.handlers.length - 1
        }
      }, {
        key: "eject",
        value: function(n) {
          this.handlers[n] && (this.handlers[n] = null)
        }
      }, {
        key: "clear",
        value: function() {
          this.handlers && (this.handlers = [])
        }
      }, {
        key: "forEach",
        value: function(n) {
          mn.forEach(this.handlers, (function(e) {
            null !== e && n(e)
          }))
        }
      }], t && On(e.prototype, t), Object.defineProperty(e, "prototype", {
        writable: !1
      }), n
    }();
    const Dn = jn,
      Pn = {
        silentJSONParsing: !0,
        forcedJSONParsing: !0,
        clarifyTimeoutError: !1
      };
    var Rn;
    const Yn = {
        isBrowser: !0,
        classes: {
          URLSearchParams: "undefined" != typeof URLSearchParams ? URLSearchParams : zn,
          FormData: "undefined" != typeof FormData ? FormData : null,
          Blob: "undefined" != typeof Blob ? Blob : null
        },
        isStandardBrowserEnv: ("undefined" == typeof navigator || "ReactNative" !== (Rn = navigator
            .product) && "NativeScript" !== Rn && "NS" !== Rn) && "undefined" != typeof window &&
          "undefined" != typeof document,
        isStandardBrowserWebWorkerEnv: "undefined" != typeof WorkerGlobalScope &&
          self instanceof WorkerGlobalScope && "function" == typeof self.importScripts,
        protocols: ["http", "https", "file", "blob", "url", "data"]
      },
      Mn = function(n) {
        function e(n, t, a, o) {
          var r = n[o++],
            i = Number.isFinite(+r),
            l = o >= n.length;
          return r = !r && mn.isArray(a) ? a.length : r, l ? (mn.hasOwnProp(a, r) ? a[r] = [a[r], t] : a[r] =
            t, !i) : (a[r] && mn.isObject(a[r]) || (a[r] = []), e(n, t, a[r], o) && mn.isArray(a[r]) && (a[
            r] = function(n) {
            var e, t, a = {},
              o = Object.keys(n),
              r = o.length;
            for (e = 0; e < r; e++) a[t = o[e]] = n[t];
            return a
          }(a[r])), !i)
        }
        if (mn.isFormData(n) && mn.isFunction(n.entries)) {
          var t = {};
          return mn.forEachEntry(n, (function(n, a) {
            e(function(n) {
              return mn.matchAll(/\w+|\[(\w*)]/g, n).map((function(n) {
                return "[]" === n[0] ? "" : n[1] || n[0]
              }))
            }(n), a, t, 0)
          })), t
        }
        return null
      };
    var Fn = {
        "Content-Type": void 0
      },
      Wn = {
        transitional: Pn,
        adapter: ["xhr", "http"],
        transformRequest: [function(n, e) {
          var t, a = e.getContentType() || "",
            o = a.indexOf("application/json") > -1,
            r = mn.isObject(n);
          if (r && mn.isHTMLForm(n) && (n = new FormData(n)), mn.isFormData(n)) return o && o ? JSON
            .stringify(Mn(n)) : n;
          if (mn.isArrayBuffer(n) || mn.isBuffer(n) || mn.isStream(n) || mn.isFile(n) || mn.isBlob(n))
            return n;
          if (mn.isArrayBufferView(n)) return n.buffer;
          if (mn.isURLSearchParams(n)) return e.setContentType(
            "application/x-www-form-urlencoded;charset=utf-8", !1), n.toString();
          if (r) {
            if (a.indexOf("application/x-www-form-urlencoded") > -1) return function(n, e) {
              return kn(n, new Yn.classes.URLSearchParams, Object.assign({
                visitor: function(n, e, t, a) {
                  return Yn.isNode && mn.isBuffer(n) ? (this.append(e, n.toString("base64")),
                    !1) : a.defaultVisitor.apply(this, arguments)
                }
              }, e))
            }(n, this.formSerializer).toString();
            if ((t = mn.isFileList(n)) || a.indexOf("multipart/form-data") > -1) {
              var i = this.env && this.env.FormData;
              return kn(t ? {
                "files[]": n
              } : n, i && new i, this.formSerializer)
            }
          }
          return r || o ? (e.setContentType("application/json", !1), function(n, e, t) {
            if (mn.isString(n)) try {
              return (0, JSON.parse)(n), mn.trim(n)
            } catch (n) {
              if ("SyntaxError" !== n.name) throw n
            }
            return (0, JSON.stringify)(n)
          }(n)) : n
        }],
        transformResponse: [function(n) {
          var e = this.transitional || Wn.transitional,
            t = e && e.forcedJSONParsing,
            a = "json" === this.responseType;
          if (n && mn.isString(n) && (t && !this.responseType || a)) {
            var o = !(e && e.silentJSONParsing) && a;
            try {
              return JSON.parse(n)
            } catch (n) {
              if (o) {
                if ("SyntaxError" === n.name) throw gn.from(n, gn.ERR_BAD_RESPONSE, this, null, this
                  .response);
                throw n
              }
            }
          }
          return n
        }],
        timeout: 0,
        xsrfCookieName: "XSRF-TOKEN",
        xsrfHeaderName: "X-XSRF-TOKEN",
        maxContentLength: -1,
        maxBodyLength: -1,
        env: {
          FormData: Yn.classes.FormData,
          Blob: Yn.classes.Blob
        },
        validateStatus: function(n) {
          return n >= 200 && n < 300
        },
        headers: {
          common: {
            Accept: "application/json, text/plain, */*"
          }
        }
      };
    mn.forEach(["delete", "get", "head"], (function(n) {
      Wn.headers[n] = {}
    })), mn.forEach(["post", "put", "patch"], (function(n) {
      Wn.headers[n] = mn.merge(Fn)
    }));
    const Un = Wn;
    var Nn = mn.toObjectSet(["age", "authorization", "content-length", "content-type", "etag", "expires",
      "from", "host", "if-modified-since", "if-unmodified-since", "last-modified", "location",
      "max-forwards", "proxy-authorization", "referer", "retry-after", "user-agent"
    ]);

    function Jn(n) {
      return Jn = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(n) {
        return typeof n
      } : function(n) {
        return n && "function" == typeof Symbol && n.constructor === Symbol && n !== Symbol.prototype ?
          "symbol" : typeof n
      }, Jn(n)
    }

    function Hn(n, e) {
      (null == e || e > n.length) && (e = n.length);
      for (var t = 0, a = new Array(e); t < e; t++) a[t] = n[t];
      return a
    }

    function Qn(n, e) {
      for (var t = 0; t < e.length; t++) {
        var a = e[t];
        a.enumerable = a.enumerable || !1, a.configurable = !0, "value" in a && (a.writable = !0), Object
          .defineProperty(n, (void 0, o = function(n, e) {
            if ("object" !== Jn(n) || null === n) return n;
            var t = n[Symbol.toPrimitive];
            if (void 0 !== t) {
              var a = t.call(n, "string");
              if ("object" !== Jn(a)) return a;
              throw new TypeError("@@toPrimitive must return a primitive value.")
            }
            return String(n)
          }(a.key), "symbol" === Jn(o) ? o : String(o)), a)
      }
      var o
    }
    var _n = Symbol("internals");

    function Zn(n) {
      return n && String(n).trim().toLowerCase()
    }

    function Vn(n) {
      return !1 === n || null == n ? n : mn.isArray(n) ? n.map(Vn) : String(n)
    }

    function Xn(n, e, t, a, o) {
      return mn.isFunction(a) ? a.call(this, e, t) : (o && (e = t), mn.isString(e) ? mn.isString(a) ? -1 !== e
        .indexOf(a) : mn.isRegExp(a) ? a.test(e) : void 0 : void 0)
    }
    var Kn = function(n, e) {
      function t(n) {
        ! function(n, e) {
          if (!(n instanceof e)) throw new TypeError("Cannot call a class as a function")
        }(this, t), n && this.set(n)
      }
      var a, o, r;
      return a = t, o = [{
        key: "set",
        value: function(n, e, t) {
          var a = this;

          function o(n, e, t) {
            var o = Zn(e);
            if (!o) throw new Error("header name must be a non-empty string");
            var r = mn.findKey(a, o);
            (!r || void 0 === a[r] || !0 === t || void 0 === t && !1 !== a[r]) && (a[r || e] = Vn(n))
          }
          var r, i, l, s, p, d = function(n, e) {
            return mn.forEach(n, (function(n, t) {
              return o(n, t, e)
            }))
          };
          return mn.isPlainObject(n) || n instanceof this.constructor ? d(n, e) : mn.isString(n) && (
            n = n.trim()) && !/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(n.trim()) ? d((p = {}, (r =
            n) && r.split("\n").forEach((function(n) {
              s = n.indexOf(":"), i = n.substring(0, s).trim().toLowerCase(), l = n.substring(
                s + 1).trim(), !i || p[i] && Nn[i] || ("set-cookie" === i ? p[i] ? p[i]
                .push(l) : p[i] = [l] : p[i] = p[i] ? p[i] + ", " + l : l)
            })), p), e) : null != n && o(e, n, t), this
        }
      }, {
        key: "get",
        value: function(n, e) {
          if (n = Zn(n)) {
            var t = mn.findKey(this, n);
            if (t) {
              var a = this[t];
              if (!e) return a;
              if (!0 === e) return function(n) {
                for (var e, t = Object.create(null), a = /([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g; e = a
                  .exec(n);) t[e[1]] = e[2];
                return t
              }(a);
              if (mn.isFunction(e)) return e.call(this, a, t);
              if (mn.isRegExp(e)) return e.exec(a);
              throw new TypeError("parser must be boolean|regexp|function")
            }
          }
        }
      }, {
        key: "has",
        value: function(n, e) {
          if (n = Zn(n)) {
            var t = mn.findKey(this, n);
            return !(!t || void 0 === this[t] || e && !Xn(0, this[t], t, e))
          }
          return !1
        }
      }, {
        key: "delete",
        value: function(n, e) {
          var t = this,
            a = !1;

          function o(n) {
            if (n = Zn(n)) {
              var o = mn.findKey(t, n);
              !o || e && !Xn(0, t[o], o, e) || (delete t[o], a = !0)
            }
          }
          return mn.isArray(n) ? n.forEach(o) : o(n), a
        }
      }, {
        key: "clear",
        value: function(n) {
          for (var e = Object.keys(this), t = e.length, a = !1; t--;) {
            var o = e[t];
            n && !Xn(0, this[o], o, n, !0) || (delete this[o], a = !0)
          }
          return a
        }
      }, {
        key: "normalize",
        value: function(n) {
          var e = this,
            t = {};
          return mn.forEach(this, (function(a, o) {
            var r = mn.findKey(t, o);
            if (r) return e[r] = Vn(a), void delete e[o];
            var i = n ? function(n) {
              return n.trim().toLowerCase().replace(/([a-z\d])(\w*)/g, (function(n, e, t) {
                return e.toUpperCase() + t
              }))
            }(o) : String(o).trim();
            i !== o && delete e[o], e[i] = Vn(a), t[i] = !0
          })), this
        }
      }, {
        key: "concat",
        value: function() {
          for (var n, e = arguments.length, t = new Array(e), a = 0; a < e; a++) t[a] = arguments[a];
          return (n = this.constructor).concat.apply(n, [this].concat(t))
        }
      }, {
        key: "toJSON",
        value: function(n) {
          var e = Object.create(null);
          return mn.forEach(this, (function(t, a) {
            null != t && !1 !== t && (e[a] = n && mn.isArray(t) ? t.join(", ") : t)
          })), e
        }
      }, {
        key: Symbol.iterator,
        value: function() {
          return Object.entries(this.toJSON())[Symbol.iterator]()
        }
      }, {
        key: "toString",
        value: function() {
          return Object.entries(this.toJSON()).map((function(n) {
            var e, t, a = (t = 2, function(n) {
              if (Array.isArray(n)) return n
            }(e = n) || function(n, e) {
              var t = null == n ? null : "undefined" != typeof Symbol && n[Symbol
                .iterator] || n["@@iterator"];
              if (null != t) {
                var a, o, r, i, l = [],
                  s = !0,
                  p = !1;
                try {
                  if (r = (t = t.call(n)).next, 0 === e) {
                    if (Object(t) !== t) return;
                    s = !1
                  } else
                    for (; !(s = (a = r.call(t)).done) && (l.push(a.value), l.length !==
                      e); s = !0);
                } catch (n) {
                  p = !0, o = n
                } finally {
                  try {
                    if (!s && null != t.return && (i = t.return(), Object(i) !== i)) return
                  } finally {
                    if (p) throw o
                  }
                }
                return l
              }
            }(e, t) || function(n, e) {
              if (n) {
                if ("string" == typeof n) return Hn(n, e);
                var t = Object.prototype.toString.call(n).slice(8, -1);
                return "Object" === t && n.constructor && (t = n.constructor.name),
                  "Map" === t || "Set" === t ? Array.from(n) : "Arguments" === t ||
                  /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? Hn(n, e) : void 0
              }
            }(e, t) || function() {
              throw new TypeError(
                "Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."
                )
            }());
            return a[0] + ": " + a[1]
          })).join("\n")
        }
      }, {
        key: Symbol.toStringTag,
        get: function() {
          return "AxiosHeaders"
        }
      }], r = [{
        key: "from",
        value: function(n) {
          return n instanceof this ? n : new this(n)
        }
      }, {
        key: "concat",
        value: function(n) {
          for (var e = new this(n), t = arguments.length, a = new Array(t > 1 ? t - 1 : 0), o = 1; o <
            t; o++) a[o - 1] = arguments[o];
          return a.forEach((function(n) {
            return e.set(n)
          })), e
        }
      }, {
        key: "accessor",
        value: function(n) {
          var e = (this[_n] = this[_n] = {
              accessors: {}
            }).accessors,
            t = this.prototype;

          function a(n) {
            var a = Zn(n);
            e[a] || (function(n, e) {
              var t = mn.toCamelCase(" " + e);
              ["get", "set", "has"].forEach((function(a) {
                Object.defineProperty(n, a + t, {
                  value: function(n, t, o) {
                    return this[a].call(this, e, n, t, o)
                  },
                  configurable: !0
                })
              }))
            }(t, n), e[a] = !0)
          }
          return mn.isArray(n) ? n.forEach(a) : a(n), this
        }
      }], o && Qn(a.prototype, o), r && Qn(a, r), Object.defineProperty(a, "prototype", {
        writable: !1
      }), t
    }();
    Kn.accessor(["Content-Type", "Content-Length", "Accept", "Accept-Encoding", "User-Agent",
      "Authorization"]), mn.freezeMethods(Kn.prototype), mn.freezeMethods(Kn);
    const Gn = Kn;

    function $n(n, e) {
      var t = this || Un,
        a = e || t,
        o = Gn.from(a.headers),
        r = a.data;
      return mn.forEach(n, (function(n) {
        r = n.call(t, r, o.normalize(), e ? e.status : void 0)
      })), o.normalize(), r
    }

    function ne(n) {
      return !(!n || !n.__CANCEL__)
    }

    function ee(n, e, t) {
      gn.call(this, null == n ? "canceled" : n, gn.ERR_CANCELED, e, t), this.name = "CanceledError"
    }
    mn.inherits(ee, gn, {
      __CANCEL__: !0
    });
    const te = ee,
      ae = Yn.isStandardBrowserEnv ? {
        write: function(n, e, t, a, o, r) {
          var i = [];
          i.push(n + "=" + encodeURIComponent(e)), mn.isNumber(t) && i.push("expires=" + new Date(t)
            .toGMTString()), mn.isString(a) && i.push("path=" + a), mn.isString(o) && i.push("domain=" +
            o), !0 === r && i.push("secure"), document.cookie = i.join("; ")
        },
        read: function(n) {
          var e = document.cookie.match(new RegExp("(^|;\\s*)(" + n + ")=([^;]*)"));
          return e ? decodeURIComponent(e[3]) : null
        },
        remove: function(n) {
          this.write(n, "", Date.now() - 864e5)
        }
      } : {
        write: function() {},
        read: function() {
          return null
        },
        remove: function() {}
      };

    function oe(n, e) {
      return n && !/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e) ? function(n, e) {
        return e ? n.replace(/\/+$/, "") + "/" + e.replace(/^\/+/, "") : n
      }(n, e) : e
    }
    const re = Yn.isStandardBrowserEnv ? function() {
      var n, e = /(msie|trident)/i.test(navigator.userAgent),
        t = document.createElement("a");

      function a(n) {
        var a = n;
        return e && (t.setAttribute("href", a), a = t.href), t.setAttribute("href", a), {
          href: t.href,
          protocol: t.protocol ? t.protocol.replace(/:$/, "") : "",
          host: t.host,
          search: t.search ? t.search.replace(/^\?/, "") : "",
          hash: t.hash ? t.hash.replace(/^#/, "") : "",
          hostname: t.hostname,
          port: t.port,
          pathname: "/" === t.pathname.charAt(0) ? t.pathname : "/" + t.pathname
        }
      }
      return n = a(window.location.href),
        function(e) {
          var t = mn.isString(e) ? a(e) : e;
          return t.protocol === n.protocol && t.host === n.host
        }
    }() : function() {
      return !0
    };

    function ie(n, e) {
      var t = 0,
        a = function(n, e) {
          n = n || 10;
          var t, a = new Array(n),
            o = new Array(n),
            r = 0,
            i = 0;
          return e = void 0 !== e ? e : 1e3,
            function(l) {
              var s = Date.now(),
                p = o[i];
              t || (t = s), a[r] = l, o[r] = s;
              for (var d = i, c = 0; d !== r;) c += a[d++], d %= n;
              if ((r = (r + 1) % n) === i && (i = (i + 1) % n), !(s - t < e)) {
                var A = p && s - p;
                return A ? Math.round(1e3 * c / A) : void 0
              }
            }
        }(50, 250);
      return function(o) {
        var r = o.loaded,
          i = o.lengthComputable ? o.total : void 0,
          l = r - t,
          s = a(l);
        t = r;
        var p = {
          loaded: r,
          total: i,
          progress: i ? r / i : void 0,
          bytes: l,
          rate: s || void 0,
          estimated: s && i && r <= i ? (i - r) / s : void 0,
          event: o
        };
        p[e ? "download" : "upload"] = !0, n(p)
      }
    }
    const le = "undefined" != typeof XMLHttpRequest && function(n) {
      return new Promise((function(e, t) {
        var a, o = n.data,
          r = Gn.from(n.headers).normalize(),
          i = n.responseType;

        function l() {
          n.cancelToken && n.cancelToken.unsubscribe(a), n.signal && n.signal.removeEventListener(
            "abort", a)
        }
        mn.isFormData(o) && (Yn.isStandardBrowserEnv || Yn.isStandardBrowserWebWorkerEnv ? r
          .setContentType(!1) : r.setContentType("multipart/form-data;", !1));
        var s = new XMLHttpRequest;
        if (n.auth) {
          var p = n.auth.username || "",
            d = n.auth.password ? unescape(encodeURIComponent(n.auth.password)) : "";
          r.set("Authorization", "Basic " + btoa(p + ":" + d))
        }
        var c = oe(n.baseURL, n.url);

        function A() {
          if (s) {
            var a = Gn.from("getAllResponseHeaders" in s && s.getAllResponseHeaders());
            ! function(n, e, t) {
              var a = t.config.validateStatus;
              t.status && a && !a(t.status) ? e(new gn("Request failed with status code " + t.status,
                [gn.ERR_BAD_REQUEST, gn.ERR_BAD_RESPONSE][Math.floor(t.status / 100) - 4], t
                .config, t.request, t)) : n(t)
            }((function(n) {
              e(n), l()
            }), (function(n) {
              t(n), l()
            }), {
              data: i && "text" !== i && "json" !== i ? s.response : s.responseText,
              status: s.status,
              statusText: s.statusText,
              headers: a,
              config: n,
              request: s
            }), s = null
          }
        }
        if (s.open(n.method.toUpperCase(), qn(c, n.params, n.paramsSerializer), !0), s.timeout = n
          .timeout, "onloadend" in s ? s.onloadend = A : s.onreadystatechange = function() {
            s && 4 === s.readyState && (0 !== s.status || s.responseURL && 0 === s.responseURL
              .indexOf("file:")) && setTimeout(A)
          }, s.onabort = function() {
            s && (t(new gn("Request aborted", gn.ECONNABORTED, n, s)), s = null)
          }, s.onerror = function() {
            t(new gn("Network Error", gn.ERR_NETWORK, n, s)), s = null
          }, s.ontimeout = function() {
            var e = n.timeout ? "timeout of " + n.timeout + "ms exceeded" : "timeout exceeded",
              a = n.transitional || Pn;
            n.timeoutErrorMessage && (e = n.timeoutErrorMessage), t(new gn(e, a.clarifyTimeoutError ?
              gn.ETIMEDOUT : gn.ECONNABORTED, n, s)), s = null
          }, Yn.isStandardBrowserEnv) {
          var u = (n.withCredentials || re(c)) && n.xsrfCookieName && ae.read(n.xsrfCookieName);
          u && r.set(n.xsrfHeaderName, u)
        }
        void 0 === o && r.setContentType(null), "setRequestHeader" in s && mn.forEach(r.toJSON(), (
            function(n, e) {
              s.setRequestHeader(e, n)
            })), mn.isUndefined(n.withCredentials) || (s.withCredentials = !!n.withCredentials), i &&
          "json" !== i && (s.responseType = n.responseType), "function" == typeof n
          .onDownloadProgress && s.addEventListener("progress", ie(n.onDownloadProgress, !0)),
          "function" == typeof n.onUploadProgress && s.upload && s.upload.addEventListener("progress",
            ie(n.onUploadProgress)), (n.cancelToken || n.signal) && (a = function(e) {
            s && (t(!e || e.type ? new te(null, n, s) : e), s.abort(), s = null)
          }, n.cancelToken && n.cancelToken.subscribe(a), n.signal && (n.signal.aborted ? a() : n
            .signal.addEventListener("abort", a)));
        var y, m = (y = /^([-+\w]{1,25})(:?\/\/|:)/.exec(c)) && y[1] || "";
        m && -1 === Yn.protocols.indexOf(m) ? t(new gn("Unsupported protocol " + m + ":", gn
          .ERR_BAD_REQUEST, n)) : s.send(o || null)
      }))
    };
    var se = {
      http: null,
      xhr: le
    };
    mn.forEach(se, (function(n, e) {
      if (n) {
        try {
          Object.defineProperty(n, "name", {
            value: e
          })
        } catch (n) {}
        Object.defineProperty(n, "adapterName", {
          value: e
        })
      }
    }));

    function pe(n) {
      if (n.cancelToken && n.cancelToken.throwIfRequested(), n.signal && n.signal.aborted) throw new te(null,
        n)
    }

    function de(n) {
      return pe(n), n.headers = Gn.from(n.headers), n.data = $n.call(n, n.transformRequest), -1 !== ["post",
          "put", "patch"
        ].indexOf(n.method) && n.headers.setContentType("application/x-www-form-urlencoded", !1),
        function(n) {
          for (var e, t, a = (n = mn.isArray(n) ? n : [n]).length, o = 0; o < a && (e = n[o], !(t = mn
              .isString(e) ? se[e.toLowerCase()] : e)); o++);
          if (!t) {
            if (!1 === t) throw new gn("Adapter ".concat(e, " is not supported by the environment"),
              "ERR_NOT_SUPPORT");
            throw new Error(mn.hasOwnProp(se, e) ? "Adapter '".concat(e, "' is not available in the build") :
              "Unknown adapter '".concat(e, "'"))
          }
          if (!mn.isFunction(t)) throw new TypeError("adapter is not a function");
          return t
        }(n.adapter || Un.adapter)(n).then((function(e) {
          return pe(n), e.data = $n.call(n, n.transformResponse, e), e.headers = Gn.from(e.headers), e
        }), (function(e) {
          return ne(e) || (pe(n), e && e.response && (e.response.data = $n.call(n, n.transformResponse, e
            .response), e.response.headers = Gn.from(e.response.headers))), Promise.reject(e)
        }))
    }
    var ce = function(n) {
      return n instanceof Gn ? n.toJSON() : n
    };

    function Ae(n, e) {
      e = e || {};
      var t = {};

      function a(n, e, t) {
        return mn.isPlainObject(n) && mn.isPlainObject(e) ? mn.merge.call({
          caseless: t
        }, n, e) : mn.isPlainObject(e) ? mn.merge({}, e) : mn.isArray(e) ? e.slice() : e
      }

      function o(n, e, t) {
        return mn.isUndefined(e) ? mn.isUndefined(n) ? void 0 : a(void 0, n, t) : a(n, e, t)
      }

      function r(n, e) {
        if (!mn.isUndefined(e)) return a(void 0, e)
      }

      function i(n, e) {
        return mn.isUndefined(e) ? mn.isUndefined(n) ? void 0 : a(void 0, n) : a(void 0, e)
      }

      function l(t, o, r) {
        return r in e ? a(t, o) : r in n ? a(void 0, t) : void 0
      }
      var s = {
        url: r,
        method: r,
        data: r,
        baseURL: i,
        transformRequest: i,
        transformResponse: i,
        paramsSerializer: i,
        timeout: i,
        timeoutMessage: i,
        withCredentials: i,
        adapter: i,
        responseType: i,
        xsrfCookieName: i,
        xsrfHeaderName: i,
        onUploadProgress: i,
        onDownloadProgress: i,
        decompress: i,
        maxContentLength: i,
        maxBodyLength: i,
        beforeRedirect: i,
        transport: i,
        httpAgent: i,
        httpsAgent: i,
        cancelToken: i,
        socketPath: i,
        responseEncoding: i,
        validateStatus: l,
        headers: function(n, e) {
          return o(ce(n), ce(e), !0)
        }
      };
      return mn.forEach(Object.keys(Object.assign({}, n, e)), (function(a) {
        var r = s[a] || o,
          i = r(n[a], e[a], a);
        mn.isUndefined(i) && r !== l || (t[a] = i)
      })), t
    }

    function ue(n) {
      return ue = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(n) {
        return typeof n
      } : function(n) {
        return n && "function" == typeof Symbol && n.constructor === Symbol && n !== Symbol.prototype ?
          "symbol" : typeof n
      }, ue(n)
    }
    var ye = {};
    ["object", "boolean", "number", "function", "string", "symbol"].forEach((function(n, e) {
      ye[n] = function(t) {
        return ue(t) === n || "a" + (e < 1 ? "n " : " ") + n
      }
    }));
    var me = {};
    ye.transitional = function(n, e, t) {
      function a(n, e) {
        return "[Axios v1.4.0] Transitional option '" + n + "'" + e + (t ? ". " + t : "")
      }
      return function(t, o, r) {
        if (!1 === n) throw new gn(a(o, " has been removed" + (e ? " in " + e : "")), gn.ERR_DEPRECATED);
        return e && !me[o] && (me[o] = !0, console.warn(a(o, " has been deprecated since v" + e +
          " and will be removed in the near future"))), !n || n(t, o, r)
      }
    };
    const he = {
      assertOptions: function(n, e, t) {
        if ("object" !== ue(n)) throw new gn("options must be an object", gn.ERR_BAD_OPTION_VALUE);
        for (var a = Object.keys(n), o = a.length; o-- > 0;) {
          var r = a[o],
            i = e[r];
          if (i) {
            var l = n[r],
              s = void 0 === l || i(l, r, n);
            if (!0 !== s) throw new gn("option " + r + " must be " + s, gn.ERR_BAD_OPTION_VALUE)
          } else if (!0 !== t) throw new gn("Unknown option " + r, gn.ERR_BAD_OPTION)
        }
      },
      validators: ye
    };

    function fe(n) {
      return fe = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(n) {
        return typeof n
      } : function(n) {
        return n && "function" == typeof Symbol && n.constructor === Symbol && n !== Symbol.prototype ?
          "symbol" : typeof n
      }, fe(n)
    }

    function be(n, e) {
      for (var t = 0; t < e.length; t++) {
        var a = e[t];
        a.enumerable = a.enumerable || !1, a.configurable = !0, "value" in a && (a.writable = !0), Object
          .defineProperty(n, (void 0, o = function(n, e) {
            if ("object" !== fe(n) || null === n) return n;
            var t = n[Symbol.toPrimitive];
            if (void 0 !== t) {
              var a = t.call(n, "string");
              if ("object" !== fe(a)) return a;
              throw new TypeError("@@toPrimitive must return a primitive value.")
            }
            return String(n)
          }(a.key), "symbol" === fe(o) ? o : String(o)), a)
      }
      var o
    }
    var ge = he.validators,
      ve = function() {
        function n(e) {
          ! function(n, e) {
            if (!(n instanceof e)) throw new TypeError("Cannot call a class as a function")
          }(this, n), this.defaults = e, this.interceptors = {
            request: new Dn,
            response: new Dn
          }
        }
        var e, t;
        return e = n, (t = [{
          key: "request",
          value: function(n, e) {
            "string" == typeof n ? (e = e || {}).url = n : e = n || {};
            var t, a = e = Ae(this.defaults, e),
              o = a.transitional,
              r = a.paramsSerializer,
              i = a.headers;
            void 0 !== o && he.assertOptions(o, {
              silentJSONParsing: ge.transitional(ge.boolean),
              forcedJSONParsing: ge.transitional(ge.boolean),
              clarifyTimeoutError: ge.transitional(ge.boolean)
            }, !1), null != r && (mn.isFunction(r) ? e.paramsSerializer = {
              serialize: r
            } : he.assertOptions(r, {
              encode: ge.function,
              serialize: ge.function
            }, !0)), e.method = (e.method || this.defaults.method || "get").toLowerCase(), (t = i &&
              mn.merge(i.common, i[e.method])) && mn.forEach(["delete", "get", "head", "post",
              "put", "patch", "common"
            ], (function(n) {
              delete i[n]
            })), e.headers = Gn.concat(t, i);
            var l = [],
              s = !0;
            this.interceptors.request.forEach((function(n) {
              "function" == typeof n.runWhen && !1 === n.runWhen(e) || (s = s && n.synchronous,
                l.unshift(n.fulfilled, n.rejected))
            }));
            var p, d = [];
            this.interceptors.response.forEach((function(n) {
              d.push(n.fulfilled, n.rejected)
            }));
            var c, A = 0;
            if (!s) {
              var u = [de.bind(this), void 0];
              for (u.unshift.apply(u, l), u.push.apply(u, d), c = u.length, p = Promise.resolve(
                e); A < c;) p = p.then(u[A++], u[A++]);
              return p
            }
            c = l.length;
            var y = e;
            for (A = 0; A < c;) {
              var m = l[A++],
                h = l[A++];
              try {
                y = m(y)
              } catch (n) {
                h.call(this, n);
                break
              }
            }
            try {
              p = de.call(this, y)
            } catch (n) {
              return Promise.reject(n)
            }
            for (A = 0, c = d.length; A < c;) p = p.then(d[A++], d[A++]);
            return p
          }
        }, {
          key: "getUri",
          value: function(n) {
            return qn(oe((n = Ae(this.defaults, n)).baseURL, n.url), n.params, n.paramsSerializer)
          }
        }]) && be(e.prototype, t), Object.defineProperty(e, "prototype", {
          writable: !1
        }), n
      }();
    mn.forEach(["delete", "get", "head", "options"], (function(n) {
      ve.prototype[n] = function(e, t) {
        return this.request(Ae(t || {}, {
          method: n,
          url: e,
          data: (t || {}).data
        }))
      }
    })), mn.forEach(["post", "put", "patch"], (function(n) {
      function e(e) {
        return function(t, a, o) {
          return this.request(Ae(o || {}, {
            method: n,
            headers: e ? {
              "Content-Type": "multipart/form-data"
            } : {},
            url: t,
            data: a
          }))
        }
      }
      ve.prototype[n] = e(), ve.prototype[n + "Form"] = e(!0)
    }));
    const xe = ve;

    function Ee(n) {
      return Ee = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(n) {
        return typeof n
      } : function(n) {
        return n && "function" == typeof Symbol && n.constructor === Symbol && n !== Symbol.prototype ?
          "symbol" : typeof n
      }, Ee(n)
    }

    function we(n, e) {
      for (var t = 0; t < e.length; t++) {
        var a = e[t];
        a.enumerable = a.enumerable || !1, a.configurable = !0, "value" in a && (a.writable = !0), Object
          .defineProperty(n, (void 0, o = function(n, e) {
            if ("object" !== Ee(n) || null === n) return n;
            var t = n[Symbol.toPrimitive];
            if (void 0 !== t) {
              var a = t.call(n, "string");
              if ("object" !== Ee(a)) return a;
              throw new TypeError("@@toPrimitive must return a primitive value.")
            }
            return String(n)
          }(a.key), "symbol" === Ee(o) ? o : String(o)), a)
      }
      var o
    }
    var Ce = function() {
      function n(e) {
        if (function(n, e) {
            if (!(n instanceof e)) throw new TypeError("Cannot call a class as a function")
          }(this, n), "function" != typeof e) throw new TypeError("executor must be a function.");
        var t;
        this.promise = new Promise((function(n) {
          t = n
        }));
        var a = this;
        this.promise.then((function(n) {
          if (a._listeners) {
            for (var e = a._listeners.length; e-- > 0;) a._listeners[e](n);
            a._listeners = null
          }
        })), this.promise.then = function(n) {
          var e, t = new Promise((function(n) {
            a.subscribe(n), e = n
          })).then(n);
          return t.cancel = function() {
            a.unsubscribe(e)
          }, t
        }, e((function(n, e, o) {
          a.reason || (a.reason = new te(n, e, o), t(a.reason))
        }))
      }
      var e, t, a;
      return e = n, t = [{
        key: "throwIfRequested",
        value: function() {
          if (this.reason) throw this.reason
        }
      }, {
        key: "subscribe",
        value: function(n) {
          this.reason ? n(this.reason) : this._listeners ? this._listeners.push(n) : this
            ._listeners = [n]
        }
      }, {
        key: "unsubscribe",
        value: function(n) {
          if (this._listeners) {
            var e = this._listeners.indexOf(n); - 1 !== e && this._listeners.splice(e, 1)
          }
        }
      }], a = [{
        key: "source",
        value: function() {
          var e;
          return {
            token: new n((function(n) {
              e = n
            })),
            cancel: e
          }
        }
      }], t && we(e.prototype, t), a && we(e, a), Object.defineProperty(e, "prototype", {
        writable: !1
      }), n
    }();
    const ke = Ce;

    function Be(n, e) {
      (null == e || e > n.length) && (e = n.length);
      for (var t = 0, a = new Array(e); t < e; t++) a[t] = n[t];
      return a
    }
    var Se = {
      Continue: 100,
      SwitchingProtocols: 101,
      Processing: 102,
      EarlyHints: 103,
      Ok: 200,
      Created: 201,
      Accepted: 202,
      NonAuthoritativeInformation: 203,
      NoContent: 204,
      ResetContent: 205,
      PartialContent: 206,
      MultiStatus: 207,
      AlreadyReported: 208,
      ImUsed: 226,
      MultipleChoices: 300,
      MovedPermanently: 301,
      Found: 302,
      SeeOther: 303,
      NotModified: 304,
      UseProxy: 305,
      Unused: 306,
      TemporaryRedirect: 307,
      PermanentRedirect: 308,
      BadRequest: 400,
      Unauthorized: 401,
      PaymentRequired: 402,
      Forbidden: 403,
      NotFound: 404,
      MethodNotAllowed: 405,
      NotAcceptable: 406,
      ProxyAuthenticationRequired: 407,
      RequestTimeout: 408,
      Conflict: 409,
      Gone: 410,
      LengthRequired: 411,
      PreconditionFailed: 412,
      PayloadTooLarge: 413,
      UriTooLong: 414,
      UnsupportedMediaType: 415,
      RangeNotSatisfiable: 416,
      ExpectationFailed: 417,
      ImATeapot: 418,
      MisdirectedRequest: 421,
      UnprocessableEntity: 422,
      Locked: 423,
      FailedDependency: 424,
      TooEarly: 425,
      UpgradeRequired: 426,
      PreconditionRequired: 428,
      TooManyRequests: 429,
      RequestHeaderFieldsTooLarge: 431,
      UnavailableForLegalReasons: 451,
      InternalServerError: 500,
      NotImplemented: 501,
      BadGateway: 502,
      ServiceUnavailable: 503,
      GatewayTimeout: 504,
      HttpVersionNotSupported: 505,
      VariantAlsoNegotiates: 506,
      InsufficientStorage: 507,
      LoopDetected: 508,
      NotExtended: 510,
      NetworkAuthenticationRequired: 511
    };
    Object.entries(Se).forEach((function(n) {
      var e, t, a = (t = 2, function(n) {
          if (Array.isArray(n)) return n
        }(e = n) || function(n, e) {
          var t = null == n ? null : "undefined" != typeof Symbol && n[Symbol.iterator] || n[
            "@@iterator"];
          if (null != t) {
            var a, o, r, i, l = [],
              s = !0,
              p = !1;
            try {
              if (r = (t = t.call(n)).next, 0 === e) {
                if (Object(t) !== t) return;
                s = !1
              } else
                for (; !(s = (a = r.call(t)).done) && (l.push(a.value), l.length !== e); s = !0);
            } catch (n) {
              p = !0, o = n
            } finally {
              try {
                if (!s && null != t.return && (i = t.return(), Object(i) !== i)) return
              } finally {
                if (p) throw o
              }
            }
            return l
          }
        }(e, t) || function(n, e) {
          if (n) {
            if ("string" == typeof n) return Be(n, e);
            var t = Object.prototype.toString.call(n).slice(8, -1);
            return "Object" === t && n.constructor && (t = n.constructor.name), "Map" === t ||
              "Set" === t ? Array.from(n) : "Arguments" === t ||
              /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? Be(n, e) : void 0
          }
        }(e, t) || function() {
          throw new TypeError(
            "Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."
            )
        }()),
        o = a[0],
        r = a[1];
      Se[r] = o
    }));
    const Ie = Se;
    var ze = function n(e) {
      var t = new xe(e),
        a = L(xe.prototype.request, t);
      return mn.extend(a, xe.prototype, t, {
        allOwnKeys: !0
      }), mn.extend(a, t, null, {
        allOwnKeys: !0
      }), a.create = function(t) {
        return n(Ae(e, t))
      }, a
    }(Un);
    ze.Axios = xe, ze.CanceledError = te, ze.CancelToken = ke, ze.isCancel = ne, ze.VERSION = "1.4.0", ze
      .toFormData = kn, ze.AxiosError = gn, ze.Cancel = ze.CanceledError, ze.all = function(n) {
        return Promise.all(n)
      }, ze.spread = function(n) {
        return function(e) {
          return n.apply(null, e)
        }
      }, ze.isAxiosError = function(n) {
        return mn.isObject(n) && !0 === n.isAxiosError
      }, ze.mergeConfig = Ae, ze.AxiosHeaders = Gn, ze.formToJSON = function(n) {
        return Mn(mn.isHTMLForm(n) ? new FormData(n) : n)
      }, ze.HttpStatusCode = Ie, ze.default = ze;
    const Te = ze,
      qe = {
        send: function(n) {
          Te.post(n.url, n.data).then((function(e) {
            var t = e.data;
            t && 0 === t.code ? n.success && n.success(t) : n.error && n.error(t && t.msg)
          })).catch((function(e) {
            console.error(e), n.error && n.error()
          }))
        },
        read: function(n) {
          Te.get(n.url).then((function(e) {
            var t = e.data;
            t && 0 === t.code ? n.success && n.success(t.data.map((function(n) {
              return {
                time: n[0],
                type: n[1],
                color: n[2],
                author: n[3],
                text: n[4]
              }
            }))) : n.error && n.error(t && t.msg)
          })).catch((function(e) {
            console.error(e), n.error && n.error()
          }))
        }
      };

    function Le(n) {
      return Le = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(n) {
        return typeof n
      } : function(n) {
        return n && "function" == typeof Symbol && n.constructor === Symbol && n !== Symbol.prototype ?
          "symbol" : typeof n
      }, Le(n)
    }

    function Oe(n) {
      var e = this;
      this.lang = n, this.fallbackLang = this.lang.includes("-") ? this.lang.split("-")[0] : this.lang, this
        .tran = function(n) {
          return n = n.toLowerCase(), De[e.lang] && De[e.lang][n] ? De[e.lang][n] : De[e.fallbackLang] && De[e
            .fallbackLang][n] ? De[e.fallbackLang][n] : je[n] ? je[n] : n
        }
    }
    var je = {
        "danmaku-loading": "Danmaku is loading",
        top: "Top",
        bottom: "Bottom",
        rolling: "Rolling",
        "input-danmaku-enter": "Input danmaku, hit Enter",
        "about-author": "About author",
        "dplayer-feedback": "DPlayer feedback",
        "about-dplayer": "About DPlayer",
        loop: "Loop",
        speed: "Speed",
        "opacity-danmaku": "Opacity for danmaku",
        normal: "Normal",
        "please-input-danmaku": "Please input danmaku content!",
        "set-danmaku-color": "Set danmaku color",
        "set-danmaku-type": "Set danmaku type",
        "show-danmaku": "Show danmaku",
        "video-failed": "Video load failed",
        "danmaku-failed": "Danmaku load failed",
        "danmaku-send-failed": "Danmaku send failed",
        "switching-quality": "Switching to %q quality",
        "switched-quality": "Switched to %q quality",
        ff: "FF %s s",
        rew: "REW %s s",
        "unlimited-danmaku": "Unlimited danmaku",
        "send-danmaku": "Send danmaku",
        setting: "Setting",
        fullscreen: "Full screen",
        "web-fullscreen": "Web full screen",
        send: "Send",
        screenshot: "Screenshot",
        airplay: "AirPlay",
        chromecast: "ChromeCast",
        subtitle: "Subtitle",
        off: "Off",
        "show-subs": "Show subtitle",
        "hide-subs": "Hide subtitle",
        volume: "Volume",
        live: "Live",
        "video-info": "Video info"
      },
      De = {
        en: je,
        "zh-cn": {
          "danmaku-loading": "弹幕加载中",
          top: "顶部",
          bottom: "底部",
          rolling: "滚动",
          "input-danmaku-enter": "输入弹幕，回车发送",
          "about-author": "关于作者",
          "dplayer-feedback": "播放器意见反馈",
          "about-dplayer": "关于 DPlayer 播放器",
          loop: "循环播放",
          speed: "速度",
          "opacity-danmaku": "弹幕透明度",
          normal: "正常",
          "please-input-danmaku": "要输入弹幕内容啊喂！",
          "set-danmaku-color": "设置弹幕颜色",
          "set-danmaku-type": "设置弹幕类型",
          "show-danmaku": "显示弹幕",
          "video-failed": "视频加载失败",
          "danmaku-failed": "弹幕加载失败",
          "danmaku-send-failed": "弹幕发送失败",
          "switching-quality": "正在切换至 %q 画质",
          "switched-quality": "已经切换至 %q 画质",
          ff: "快进 %s 秒",
          rew: "快退 %s 秒",
          "unlimited-danmaku": "海量弹幕",
          "send-danmaku": "发送弹幕",
          setting: "设置",
          fullscreen: "全屏",
          "web-fullscreen": "页面全屏",
          send: "发送",
          screenshot: "截图",
          airplay: "无线投屏",
          chromecast: "ChromeCast",
          subtitle: "字幕",
          off: "关闭",
          "show-subs": "显示字幕",
          "hide-subs": "隐藏字幕",
          volume: "音量",
          live: "直播",
          "video-info": "视频统计信息"
        }
      },
      Pe = t(730),
      Re = t.n(Pe),
      Ye = t(74),
      Me = t.n(Ye),
      Fe = t(437),
      We = t.n(Fe),
      Ue = t(644),
      Ne = t.n(Ue),
      Je = t(324),
      He = t.n(Je),
      Qe = t(574),
      _e = t.n(Qe),
      Ze = t(415),
      Ve = t.n(Ze),
      Xe = t(934),
      Ke = t.n(Xe),
      Ge = t(428),
      $e = t.n(Ge),
      nt = t(807),
      et = t.n(nt),
      tt = t(338),
      at = t.n(tt),
      ot = t(254),
      rt = t.n(ot),
      it = t(965),
      lt = t.n(it),
      st = t(113),
      pt = t.n(st),
      dt = t(251),
      ct = t.n(dt),
      At = t(410),
      ut = t.n(At),
      yt = t(182),
      mt = t.n(yt),
      ht = t(193),
      ft = t.n(ht);
    const bt = {
      play: Re(),
      pause: Me(),
      volumeUp: We(),
      volumeDown: Ne(),
      volumeOff: He(),
      full: _e(),
      fullWeb: Ve(),
      setting: Ke(),
      right: $e(),
      comment: et(),
      commentOff: at(),
      send: rt(),
      pallette: lt(),
      camera: pt(),
      subtitle: ut(),
      loading: mt(),
      airplay: ct(),
      chromecast: ft()
    };
    var gt = t(916),
      vt = t.n(gt);

    function xt(n) {
      return xt = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(n) {
        return typeof n
      } : function(n) {
        return n && "function" == typeof Symbol && n.constructor === Symbol && n !== Symbol.prototype ?
          "symbol" : typeof n
      }, xt(n)
    }

    function Et(n, e) {
      for (var t = 0; t < e.length; t++) {
        var a = e[t];
        a.enumerable = a.enumerable || !1, a.configurable = !0, "value" in a && (a.writable = !0), Object
          .defineProperty(n, (void 0, o = function(n, e) {
            if ("object" !== xt(n) || null === n) return n;
            var t = n[Symbol.toPrimitive];
            if (void 0 !== t) {
              var a = t.call(n, "string");
              if ("object" !== xt(a)) return a;
              throw new TypeError("@@toPrimitive must return a primitive value.")
            }
            return String(n)
          }(a.key), "symbol" === xt(o) ? o : String(o)), a)
      }
      var o
    }
    var wt = function() {
      function n(e) {
        ! function(n, e) {
          if (!(n instanceof e)) throw new TypeError("Cannot call a class as a function")
        }(this, n), this.container = e.container, this.options = e.options, this.index = e.index, this
          .tran = e.tran, this.init()
      }
      var e, t, a;
      return e = n, a = [{
        key: "NewNotice",
        value: function(n, e, t) {
          var a = document.createElement("div");
          return a.classList.add("dplayer-notice"), a.style.opacity = e, a.innerText = n, t && (a.id =
            "dplayer-notice-".concat(t)), a
        }
      }], (t = [{
        key: "init",
        value: function() {
          this.container.innerHTML = vt()({
              options: this.options,
              index: this.index,
              tran: this.tran,
              icons: bt,
              mobile: q.isMobile,
              video: {
                current: !0,
                pic: this.options.video.pic,
                screenshot: this.options.screenshot,
                airplay: !(!q.isSafari || q.isChrome) && this.options.airplay,
                chromecast: this.options.chromecast,
                preload: this.options.preload,
                url: this.options.video.url,
                subtitle: this.options.subtitle
              }
            }), this.volumeBar = this.container.querySelector(".dplayer-volume-bar-inner"), this
            .volumeBarWrap = this.container.querySelector(".dplayer-volume-bar"), this
            .volumeBarWrapWrap = this.container.querySelector(".dplayer-volume-bar-wrap"), this
            .volumeButton = this.container.querySelector(".dplayer-volume"), this.volumeButtonIcon =
            this.container.querySelector(".dplayer-volume-icon"), this.volumeIcon = this.container
            .querySelector(".dplayer-volume-icon .dplayer-icon-content"), this.playedBar = this
            .container.querySelector(".dplayer-played"), this.loadedBar = this.container
            .querySelector(".dplayer-loaded"), this.playedBarWrap = this.container.querySelector(
              ".dplayer-bar-wrap"), this.playedBarTime = this.container.querySelector(
              ".dplayer-bar-time"), this.danmaku = this.container.querySelector(".dplayer-danmaku"),
            this.danmakuLoading = this.container.querySelector(".dplayer-danloading"), this.video =
            this.container.querySelector(".dplayer-video-current"), this.bezel = this.container
            .querySelector(".dplayer-bezel-icon"), this.playButton = this.container.querySelector(
              ".dplayer-play-icon"), this.mobilePlayButton = this.container.querySelector(
              ".dplayer-mobile-play"), this.videoWrap = this.container.querySelector(
              ".dplayer-video-wrap"), this.controllerMask = this.container.querySelector(
              ".dplayer-controller-mask"), this.ptime = this.container.querySelector(
              ".dplayer-ptime"), this.settingButton = this.container.querySelector(
              ".dplayer-setting-icon"), this.settingBox = this.container.querySelector(
              ".dplayer-setting-box"), this.mask = this.container.querySelector(".dplayer-mask"),
            this.loop = this.container.querySelector(".dplayer-setting-loop"), this.loopToggle =
            this.container.querySelector(".dplayer-setting-loop .dplayer-toggle-setting-input"),
            this.showDanmaku = this.container.querySelector(".dplayer-setting-showdan"), this
            .showDanmakuToggle = this.container.querySelector(".dplayer-showdan-setting-input"),
            this.unlimitDanmaku = this.container.querySelector(".dplayer-setting-danunlimit"), this
            .unlimitDanmakuToggle = this.container.querySelector(
              ".dplayer-danunlimit-setting-input"), this.speed = this.container.querySelector(
              ".dplayer-setting-speed"), this.speedItem = this.container.querySelectorAll(
              ".dplayer-setting-speed-item"), this.danmakuOpacityBar = this.container.querySelector(
              ".dplayer-danmaku-bar-inner"), this.danmakuOpacityBarWrap = this.container
            .querySelector(".dplayer-danmaku-bar"), this.danmakuOpacityBarWrapWrap = this.container
            .querySelector(".dplayer-danmaku-bar-wrap"), this.danmakuOpacityBox = this.container
            .querySelector(".dplayer-setting-danmaku"), this.dtime = this.container.querySelector(
              ".dplayer-dtime"), this.controller = this.container.querySelector(
              ".dplayer-controller"), this.commentInput = this.container.querySelector(
              ".dplayer-comment-input"), this.commentButton = this.container.querySelector(
              ".dplayer-comment-icon"), this.commentSettingBox = this.container.querySelector(
              ".dplayer-comment-setting-box"), this.commentSettingButton = this.container
            .querySelector(".dplayer-comment-setting-icon"), this.commentSettingFill = this
            .container.querySelector(".dplayer-comment-setting-icon path"), this.commentSendButton =
            this.container.querySelector(".dplayer-send-icon"), this.commentSendFill = this
            .container.querySelector(".dplayer-send-icon path"), this.commentColorSettingBox = this
            .container.querySelector(".dplayer-comment-setting-color"), this.browserFullButton =
            this.container.querySelector(".dplayer-full-icon"), this.menu = this.container.querySelector(
              ".dplayer-menu"), this.menuItem = this.container.querySelectorAll(
              ".dplayer-menu-item"), this.qualityList = this.container.querySelector(
              ".dplayer-quality-list"), this.camareButton = this.container.querySelector(
              ".dplayer-camera-icon"), this.chromecastButton = this.container.querySelector(
              ".dplayer-chromecast-icon"), this.subtitleButton = this.container.querySelector(
              ".dplayer-subtitle-icon"), this.subtitleButtonInner = this.container.querySelector(
              ".dplayer-subtitle-icon .dplayer-icon-content"), this.subtitlesButton = this.container
            .querySelector(".dplayer-subtitles-icon"), this.subtitlesBox = this.container
            .querySelector(".dplayer-subtitles-box"), this.subtitlesItem = this.container
            .querySelectorAll(".dplayer-subtitles-item"), this.subtitle = this.container
            .querySelector(".dplayer-subtitle"), this.subtrack = this.container.querySelector(
              ".dplayer-subtrack"), this.qualityButton = this.container.querySelector(
              ".dplayer-quality-icon"), this.barPreview = this.container.querySelector(
              ".dplayer-bar-preview"), this.barWrap = this.container.querySelector(
              ".dplayer-bar-wrap"), this.noticeList = this.container.querySelector(
              ".dplayer-notice-list"), this.infoPanel = this.container.querySelector(
              ".dplayer-info-panel"), this.infoPanelClose = this.container.querySelector(
              ".dplayer-info-panel-close"), this.infoVersion = this.container.querySelector(
              ".dplayer-info-panel-item-version .dplayer-info-panel-item-data"), this.infoFPS = this
            .container.querySelector(".dplayer-info-panel-item-fps .dplayer-info-panel-item-data"),
            this.infoType = this.container.querySelector(
              ".dplayer-info-panel-item-type .dplayer-info-panel-item-data"), this.infoUrl = this
            .container.querySelector(".dplayer-info-panel-item-url .dplayer-info-panel-item-data"),
            this.infoResolution = this.container.querySelector(
              ".dplayer-info-panel-item-resolution .dplayer-info-panel-item-data"), this
            .infoDuration = this.container.querySelector(
              ".dplayer-info-panel-item-duration .dplayer-info-panel-item-data"), this
            .infoDanmakuId = this.container.querySelector(
              ".dplayer-info-panel-item-danmaku-id .dplayer-info-panel-item-data"), this
            .infoDanmakuApi = this.container.querySelector(
              ".dplayer-info-panel-item-danmaku-api .dplayer-info-panel-item-data"), this
            .infoDanmakuAmount = this.container.querySelector(
              ".dplayer-info-panel-item-danmaku-amount .dplayer-info-panel-item-data")
        }
      }]) && Et(e.prototype, t), a && Et(e, a), Object.defineProperty(e, "prototype", {
        writable: !1
      }), n
    }();
    const Ct = wt;

    function kt(n) {
      return kt = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(n) {
        return typeof n
      } : function(n) {
        return n && "function" == typeof Symbol && n.constructor === Symbol && n !== Symbol.prototype ?
          "symbol" : typeof n
      }, kt(n)
    }

    function Bt(n, e) {
      for (var t = 0; t < e.length; t++) {
        var a = e[t];
        a.enumerable = a.enumerable || !1, a.configurable = !0, "value" in a && (a.writable = !0), Object
          .defineProperty(n, (void 0, o = function(n, e) {
            if ("object" !== kt(n) || null === n) return n;
            var t = n[Symbol.toPrimitive];
            if (void 0 !== t) {
              var a = t.call(n, "string");
              if ("object" !== kt(a)) return a;
              throw new TypeError("@@toPrimitive must return a primitive value.")
            }
            return String(n)
          }(a.key), "symbol" === kt(o) ? o : String(o)), a)
      }
      var o
    }
    var St = function() {
      function n(e) {
        ! function(n, e) {
          if (!(n instanceof e)) throw new TypeError("Cannot call a class as a function")
        }(this, n), this.options = e, this.player = this.options.player, this.container = this.options
          .container, this.danTunnel = {
            right: {},
            top: {},
            bottom: {}
          }, this.danIndex = 0, this.dan = [], this.showing = !0, this._opacity = this.options.opacity, this
          .events = this.options.events, this.unlimited = this.options.unlimited, this._measure(""), this
          .load()
      }
      var e, t;
      return e = n, t = [{
        key: "load",
        value: function() {
          var n, e = this;
          n = this.options.api.maximum ? "".concat(this.options.api.address, "v3/?id=").concat(this
            .options.api.id, "&max=").concat(this.options.api.maximum) : "".concat(this.options.api
            .address, "v3/?id=").concat(this.options.api.id);
          var t = (this.options.api.addition || []).slice(0);
          t.push(n), this.events && this.events.trigger("danmaku_load_start", t), this
            ._readAllEndpoints(t, (function(n) {
              e.dan = [].concat.apply([], n).sort((function(n, e) {
                return n.time - e.time
              })), window.requestAnimationFrame((function() {
                e.frame()
              })), e.options.callback(), e.events && e.events.trigger("danmaku_load_end")
            }))
        }
      }, {
        key: "reload",
        value: function(n) {
          this.options.api = n, this.dan = [], this.clear(), this.load()
        }
      }, {
        key: "_readAllEndpoints",
        value: function(n, e) {
          for (var t = this, a = [], o = 0, r = function(r) {
              t.options.apiBackend.read({
                url: n[r],
                success: function(t) {
                  a[r] = t, ++o === n.length && e(a)
                },
                error: function(i) {
                  t.options.error(i || t.options.tran("danmaku-failed")), a[r] = [], ++o === n
                    .length && e(a)
                }
              })
            }, i = 0; i < n.length; ++i) r(i)
        }
      }, {
        key: "send",
        value: function(n, e) {
          var t = this,
            a = {
              token: this.options.api.token,
              id: this.options.api.id,
              author: this.options.api.user,
              time: this.options.time(),
              text: n.text,
              color: n.color,
              type: n.type
            };
          this.options.apiBackend.send({
            url: this.options.api.address + "v3/",
            data: a,
            success: e,
            error: function(n) {
              t.options.error(n || t.options.tran("danmaku-failed"))
            }
          }), this.dan.splice(this.danIndex, 0, a), this.danIndex++;
          var o = {
            text: this.htmlEncode(a.text),
            color: a.color,
            type: a.type,
            border: "2px solid ".concat(this.options.borderColor)
          };
          this.draw(o), this.events && this.events.trigger("danmaku_send", a)
        }
      }, {
        key: "frame",
        value: function() {
          var n = this;
          if (this.dan.length && !this.paused && this.showing) {
            for (var e = this.dan[this.danIndex], t = []; e && this.options.time() > parseFloat(e
                .time);) t.push(e), e = this.dan[++this.danIndex];
            this.draw(t)
          }
          window.requestAnimationFrame((function() {
            n.frame()
          }))
        }
      }, {
        key: "opacity",
        value: function(n) {
          if (void 0 !== n) {
            for (var e = this.container.getElementsByClassName("dplayer-danmaku-item"), t = 0; t < e
              .length; t++) e[t].style.opacity = n;
            this._opacity = n, this.events && this.events.trigger("danmaku_opacity", this._opacity)
          }
          return this._opacity
        }
      }, {
        key: "draw",
        value: function(n) {
          var e = this;
          if (this.showing) {
            var t = this.options.height,
              a = this.container.offsetWidth,
              o = this.container.offsetHeight,
              r = parseInt(o / t),
              i = function(n) {
                var t = n.offsetWidth || parseInt(n.style.width),
                  a = n.getBoundingClientRect().right || e.container.getBoundingClientRect().right +
                  t;
                return e.container.getBoundingClientRect().right - a
              },
              l = function(n) {
                return (a + n) / 5
              },
              s = function(n, t, o) {
                for (var s, p = a / l(o), d = function(o) {
                    var s = e.danTunnel[t][o + ""];
                    if (!s || !s.length) return e.danTunnel[t][o + ""] = [n], n.addEventListener(
                      "animationend", (function() {
                        e.danTunnel[t][o + ""].splice(0, 1)
                      })), {
                      v: o % r
                    };
                    if ("right" !== t) return 0;
                    for (var d = 0; d < s.length; d++) {
                      var c = i(s[d]) - 10;
                      if (c <= a - p * l(parseInt(s[d].style.width)) || c <= 0) break;
                      if (d === s.length - 1) return e.danTunnel[t][o + ""].push(n), n
                        .addEventListener("animationend", (function() {
                          e.danTunnel[t][o + ""].splice(0, 1)
                        })), {
                          v: o % r
                        }
                    }
                  }, c = 0; e.unlimited || c < r; c++)
                  if (0 !== (s = d(c)) && s) return s.v;
                return -1
              };
            "[object Array]" !== Object.prototype.toString.call(n) && (n = [n]);
            for (var p = document.createDocumentFragment(), d = function() {
                n[c].type = q.number2Type(n[c].type), n[c].color || (n[c].color = 16777215);
                var o = document.createElement("div");
                o.classList.add("dplayer-danmaku-item"), o.classList.add("dplayer-danmaku-".concat(
                    n[c].type)), n[c].border ? o.innerHTML = '<span style="border:'.concat(n[c]
                    .border, '">').concat(n[c].text, "</span>") : o.innerHTML = n[c].text, o.style
                  .opacity = e._opacity, o.style.color = q.number2Color(n[c].color), o
                  .addEventListener("animationend", (function() {
                    e.container.removeChild(o)
                  }));
                var r, i = e._measure(n[c].text);
                switch (n[c].type) {
                  case "right":
                    (r = s(o, n[c].type, i)) >= 0 && (o.style.width = i + 1 + "px", o.style.top =
                      t * r + "px", o.style.transform = "translateX(-".concat(a, "px)"));
                    break;
                  case "top":
                    (r = s(o, n[c].type)) >= 0 && (o.style.top = t * r + "px");
                    break;
                  case "bottom":
                    (r = s(o, n[c].type)) >= 0 && (o.style.bottom = t * r + "px");
                    break;
                  default:
                    console.error("Can't handled danmaku type: ".concat(n[c].type))
                }
                r >= 0 && (o.classList.add("dplayer-danmaku-move"), o.style.animationDuration = e
                  ._danAnimation(n[c].type), p.appendChild(o))
              }, c = 0; c < n.length; c++) d();
            return this.container.appendChild(p), p
          }
        }
      }, {
        key: "play",
        value: function() {
          this.paused = !1
        }
      }, {
        key: "pause",
        value: function() {
          this.paused = !0
        }
      }, {
        key: "_measure",
        value: function(n) {
          if (!this.context) {
            var e = getComputedStyle(this.container.getElementsByClassName("dplayer-danmaku-item")[0],
              null);
            this.context = document.createElement("canvas").getContext("2d"), this.context.font = e
              .getPropertyValue("font")
          }
          return this.context.measureText(n).width
        }
      }, {
        key: "seek",
        value: function() {
          this.clear();
          for (var n = 0; n < this.dan.length; n++) {
            if (this.dan[n].time >= this.options.time()) {
              this.danIndex = n;
              break
            }
            this.danIndex = this.dan.length
          }
        }
      }, {
        key: "clear",
        value: function() {
          this.danTunnel = {
              right: {},
              top: {},
              bottom: {}
            }, this.danIndex = 0, this.options.container.innerHTML = "", this.events && this.events
            .trigger("danmaku_clear")
        }
      }, {
        key: "htmlEncode",
        value: function(n) {
          return n.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g,
            "&quot;").replace(/'/g, "&#x27;").replace(/\//g, "&#x2f;")
        }
      }, {
        key: "resize",
        value: function() {
          for (var n = this.container.offsetWidth, e = this.container.getElementsByClassName(
              "dplayer-danmaku-item"), t = 0; t < e.length; t++) e[t].style.transform = "translateX(-"
            .concat(n, "px)")
        }
      }, {
        key: "hide",
        value: function() {
          this.showing = !1, this.pause(), this.clear(), this.events && this.events.trigger(
            "danmaku_hide")
        }
      }, {
        key: "show",
        value: function() {
          this.seek(), this.showing = !0, this.play(), this.events && this.events.trigger(
            "danmaku_show")
        }
      }, {
        key: "unlimit",
        value: function(n) {
          this.unlimited = n
        }
      }, {
        key: "speed",
        value: function(n) {
          this.options.api.speedRate = n
        }
      }, {
        key: "_danAnimation",
        value: function(n) {
          var e = this.options.api.speedRate || 1,
            t = !!this.player.fullScreen.isFullScreen();
          return {
            top: "".concat((t ? 6 : 4) / e, "s"),
            right: "".concat((t ? 8 : 5) / e, "s"),
            bottom: "".concat((t ? 6 : 4) / e, "s")
          } [n]
        }
      }], t && Bt(e.prototype, t), Object.defineProperty(e, "prototype", {
        writable: !1
      }), n
    }();
    const It = St;

    function zt(n) {
      return zt = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(n) {
        return typeof n
      } : function(n) {
        return n && "function" == typeof Symbol && n.constructor === Symbol && n !== Symbol.prototype ?
          "symbol" : typeof n
      }, zt(n)
    }

    function Tt(n, e) {
      for (var t = 0; t < e.length; t++) {
        var a = e[t];
        a.enumerable = a.enumerable || !1, a.configurable = !0, "value" in a && (a.writable = !0), Object
          .defineProperty(n, (void 0, o = function(n, e) {
            if ("object" !== zt(n) || null === n) return n;
            var t = n[Symbol.toPrimitive];
            if (void 0 !== t) {
              var a = t.call(n, "string");
              if ("object" !== zt(a)) return a;
              throw new TypeError("@@toPrimitive must return a primitive value.")
            }
            return String(n)
          }(a.key), "symbol" === zt(o) ? o : String(o)), a)
      }
      var o
    }
    const qt = function() {
      function n() {
        ! function(n, e) {
          if (!(n instanceof e)) throw new TypeError("Cannot call a class as a function")
        }(this, n), this.events = {}, this.videoEvents = ["abort", "canplay", "canplaythrough",
          "durationchange", "emptied", "ended", "error", "loadeddata", "loadedmetadata", "loadstart",
          "mozaudioavailable", "pause", "play", "playing", "progress", "ratechange", "seeked", "seeking",
          "stalled", "suspend", "timeupdate", "volumechange", "waiting"
        ], this.playerEvents = ["screenshot", "thumbnails_show", "thumbnails_hide", "danmaku_show",
          "danmaku_hide", "danmaku_clear", "danmaku_loaded", "danmaku_send", "danmaku_opacity",
          "contextmenu_show", "contextmenu_hide", "notice_show", "notice_hide", "quality_start",
          "quality_end", "destroy", "resize", "fullscreen", "fullscreen_cancel", "webfullscreen",
          "webfullscreen_cancel", "subtitle_show", "subtitle_hide", "subtitle_change"
        ]
      }
      var e, t;
      return e = n, (t = [{
        key: "on",
        value: function(n, e) {
          this.type(n) && "function" == typeof e && (this.events[n] || (this.events[n] = []), this
            .events[n].push(e))
        }
      }, {
        key: "trigger",
        value: function(n, e) {
          if (this.events[n] && this.events[n].length)
            for (var t = 0; t < this.events[n].length; t++) this.events[n][t](e)
        }
      }, {
        key: "type",
        value: function(n) {
          return -1 !== this.playerEvents.indexOf(n) ? "player" : -1 !== this.videoEvents.indexOf(
            n) ? "video" : (console.error("Unknown event name: ".concat(n)), null)
        }
      }]) && Tt(e.prototype, t), Object.defineProperty(e, "prototype", {
        writable: !1
      }), n
    }();

    function Lt(n) {
      return Lt = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(n) {
        return typeof n
      } : function(n) {
        return n && "function" == typeof Symbol && n.constructor === Symbol && n !== Symbol.prototype ?
          "symbol" : typeof n
      }, Lt(n)
    }

    function Ot(n, e) {
      for (var t = 0; t < e.length; t++) {
        var a = e[t];
        a.enumerable = a.enumerable || !1, a.configurable = !0, "value" in a && (a.writable = !0), Object
          .defineProperty(n, (void 0, o = function(n, e) {
            if ("object" !== Lt(n) || null === n) return n;
            var t = n[Symbol.toPrimitive];
            if (void 0 !== t) {
              var a = t.call(n, "string");
              if ("object" !== Lt(a)) return a;
              throw new TypeError("@@toPrimitive must return a primitive value.")
            }
            return String(n)
          }(a.key), "symbol" === Lt(o) ? o : String(o)), a)
      }
      var o
    }
    var jt = function() {
      function n(e) {
        var t = this;
        ! function(n, e) {
          if (!(n instanceof e)) throw new TypeError("Cannot call a class as a function")
        }(this, n), this.player = e, this.lastScrollPosition = {
          left: 0,
          top: 0
        }, this.player.events.on("webfullscreen", (function() {
          t.player.resize()
        })), this.player.events.on("webfullscreen_cancel", (function() {
          t.player.resize(), q.setScrollPosition(t.lastScrollPosition)
        })), this.fullscreenchange = function() {
          t.player.resize(), t.isFullScreen("browser") ? t.player.events.trigger("fullscreen") : (q
            .setScrollPosition(t.lastScrollPosition), t.player.events.trigger("fullscreen_cancel"))
        }, this.docfullscreenchange = function() {
          var n = document.fullscreenElement || document.mozFullScreenElement || document
            .msFullscreenElement;
          n && n !== t.player.container || (t.player.resize(), n ? t.player.events.trigger("fullscreen") :
            (q.setScrollPosition(t.lastScrollPosition), t.player.events.trigger("fullscreen_cancel")))
        }, /Firefox/.test(navigator.userAgent) ? (document.addEventListener("mozfullscreenchange", this
            .docfullscreenchange), document.addEventListener("fullscreenchange", this
          .docfullscreenchange)) : (this.player.container.addEventListener("fullscreenchange", this
            .fullscreenchange), this.player.container.addEventListener("webkitfullscreenchange", this
            .fullscreenchange), document.addEventListener("msfullscreenchange", this.docfullscreenchange),
          document.addEventListener("MSFullscreenChange", this.docfullscreenchange))
      }
      var e, t;
      return e = n, t = [{
        key: "isFullScreen",
        value: function() {
          switch (arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "browser") {
            case "browser":
              return document.fullscreenElement || document.mozFullScreenElement || document
                .webkitFullscreenElement || document.msFullscreenElement;
            case "web":
              return this.player.container.classList.contains("dplayer-fulled")
          }
        }
      }, {
        key: "request",
        value: function() {
          var n = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "browser",
            e = "browser" === n ? "web" : "browser",
            t = this.isFullScreen(e);
          switch (t || (this.lastScrollPosition = q.getScrollPosition()), n) {
            case "browser":
              this.player.container.requestFullscreen ? this.player.container.requestFullscreen() :
                this.player.container.mozRequestFullScreen ? this.player.container
                .mozRequestFullScreen() : this.player.container.webkitRequestFullscreen ? this.player
                .container.webkitRequestFullscreen() : this.player.video.webkitEnterFullscreen ? this
                .player.video.webkitEnterFullscreen() : this.player.video.webkitEnterFullScreen ? this
                .player.video.webkitEnterFullScreen() : this.player.container.msRequestFullscreen &&
                this.player.container.msRequestFullscreen();
              break;
            case "web":
              this.player.container.classList.add("dplayer-fulled"), document.body.classList.add(
                "dplayer-web-fullscreen-fix"), this.player.events.trigger("webfullscreen")
          }
          t && this.cancel(e)
        }
      }, {
        key: "cancel",
        value: function() {
          switch (arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "browser") {
            case "browser":
              document.cancelFullScreen ? document.cancelFullScreen() : document.mozCancelFullScreen ?
                document.mozCancelFullScreen() : document.webkitCancelFullScreen ? document
                .webkitCancelFullScreen() : document.webkitCancelFullscreen ? document
                .webkitCancelFullscreen() : document.msCancelFullScreen ? document
                .msCancelFullScreen() : document.msExitFullscreen && document.msExitFullscreen();
              break;
            case "web":
              this.player.container.classList.remove("dplayer-fulled"), document.body.classList
                .remove("dplayer-web-fullscreen-fix"), this.player.events.trigger(
                  "webfullscreen_cancel")
          }
        }
      }, {
        key: "toggle",
        value: function() {
          var n = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "browser";
          this.isFullScreen(n) ? this.cancel(n) : this.request(n)
        }
      }, {
        key: "destroy",
        value: function() {
          /Firefox/.test(navigator.userAgent) ? (document.removeEventListener("mozfullscreenchange",
            this.docfullscreenchange), document.removeEventListener("fullscreenchange", this
            .docfullscreenchange)) : (this.player.container.removeEventListener("fullscreenchange",
            this.fullscreenchange), this.player.container.removeEventListener(
            "webkitfullscreenchange", this.fullscreenchange), document.removeEventListener(
            "msfullscreenchange", this.docfullscreenchange), document.removeEventListener(
            "MSFullscreenChange", this.docfullscreenchange))
        }
      }], t && Ot(e.prototype, t), Object.defineProperty(e, "prototype", {
        writable: !1
      }), n
    }();
    const Dt = jt;

    function Pt(n) {
      return Pt = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(n) {
        return typeof n
      } : function(n) {
        return n && "function" == typeof Symbol && n.constructor === Symbol && n !== Symbol.prototype ?
          "symbol" : typeof n
      }, Pt(n)
    }

    function Rt(n, e) {
      for (var t = 0; t < e.length; t++) {
        var a = e[t];
        a.enumerable = a.enumerable || !1, a.configurable = !0, "value" in a && (a.writable = !0), Object
          .defineProperty(n, (void 0, o = function(n, e) {
            if ("object" !== Pt(n) || null === n) return n;
            var t = n[Symbol.toPrimitive];
            if (void 0 !== t) {
              var a = t.call(n, "string");
              if ("object" !== Pt(a)) return a;
              throw new TypeError("@@toPrimitive must return a primitive value.")
            }
            return String(n)
          }(a.key), "symbol" === Pt(o) ? o : String(o)), a)
      }
      var o
    }
    var Yt = function() {
      function n(e) {
        ! function(n, e) {
          if (!(n instanceof e)) throw new TypeError("Cannot call a class as a function")
        }(this, n), this.storageName = {
          opacity: "dplayer-danmaku-opacity",
          volume: "dplayer-volume",
          unlimited: "dplayer-danmaku-unlimited",
          danmaku: "dplayer-danmaku-show",
          subtitle: "dplayer-subtitle-show"
        }, this.default = {
          opacity: .7,
          volume: e.options.hasOwnProperty("volume") ? e.options.volume : .7,
          unlimited: (e.options.danmaku && e.options.danmaku.unlimited ? 1 : 0) || 0,
          danmaku: 1,
          subtitle: 1
        }, this.data = {}, this.init()
      }
      var e, t;
      return e = n, (t = [{
        key: "init",
        value: function() {
          for (var n in this.storageName) {
            var e = this.storageName[n];
            this.data[n] = parseFloat(q.storage.get(e) || this.default[n])
          }
        }
      }, {
        key: "get",
        value: function(n) {
          return this.data[n]
        }
      }, {
        key: "set",
        value: function(n, e) {
          this.data[n] = e, q.storage.set(this.storageName[n], e)
        }
      }]) && Rt(e.prototype, t), Object.defineProperty(e, "prototype", {
        writable: !1
      }), n
    }();
    const Mt = Yt;

    function Ft(n) {
      return Ft = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(n) {
        return typeof n
      } : function(n) {
        return n && "function" == typeof Symbol && n.constructor === Symbol && n !== Symbol.prototype ?
          "symbol" : typeof n
      }, Ft(n)
    }

    function Wt(n, e) {
      for (var t = 0; t < e.length; t++) {
        var a = e[t];
        a.enumerable = a.enumerable || !1, a.configurable = !0, "value" in a && (a.writable = !0), Object
          .defineProperty(n, (void 0, o = function(n, e) {
            if ("object" !== Ft(n) || null === n) return n;
            var t = n[Symbol.toPrimitive];
            if (void 0 !== t) {
              var a = t.call(n, "string");
              if ("object" !== Ft(a)) return a;
              throw new TypeError("@@toPrimitive must return a primitive value.")
            }
            return String(n)
          }(a.key), "symbol" === Ft(o) ? o : String(o)), a)
      }
      var o
    }
    var Ut = function() {
      function n(e, t, a, o) {
        ! function(n, e) {
          if (!(n instanceof e)) throw new TypeError("Cannot call a class as a function")
        }(this, n), this.container = e, this.video = t, this.options = a, this.events = o, this.init()
      }
      var e, t;
      return e = n, t = [{
        key: "init",
        value: function() {
          var n = this;
          if (this.container.style.fontSize = this.options.fontSize, this.container.style.bottom =
            this.options.bottom, this.container.style.color = this.options.color, this.video
            .textTracks && this.video.textTracks[0]) {
            var e = this.video.textTracks[0];
            e.oncuechange = function() {
              var t = e.activeCues[e.activeCues.length - 1];
              if (n.container.innerHTML = "", t) {
                var a = document.createElement("div");
                a.appendChild(t.getCueAsHTML());
                var o = a.innerHTML.split(/\r?\n/).map((function(n) {
                  return "<p>".concat(n, "</p>")
                })).join("");
                n.container.innerHTML = o
              }
              n.events.trigger("subtitle_change")
            }
          }
        }
      }, {
        key: "show",
        value: function() {
          this.container.classList.remove("dplayer-subtitle-hide"), this.events.trigger(
            "subtitle_show")
        }
      }, {
        key: "hide",
        value: function() {
          this.container.classList.add("dplayer-subtitle-hide"), this.events.trigger("subtitle_hide")
        }
      }, {
        key: "toggle",
        value: function() {
          this.container.classList.contains("dplayer-subtitle-hide") ? this.show() : this.hide()
        }
      }], t && Wt(e.prototype, t), Object.defineProperty(e, "prototype", {
        writable: !1
      }), n
    }();
    const Nt = Ut;

    function Jt(n) {
      return Jt = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(n) {
        return typeof n
      } : function(n) {
        return n && "function" == typeof Symbol && n.constructor === Symbol && n !== Symbol.prototype ?
          "symbol" : typeof n
      }, Jt(n)
    }

    function Ht(n, e) {
      for (var t = 0; t < e.length; t++) {
        var a = e[t];
        a.enumerable = a.enumerable || !1, a.configurable = !0, "value" in a && (a.writable = !0), Object
          .defineProperty(n, (void 0, o = function(n, e) {
            if ("object" !== Jt(n) || null === n) return n;
            var t = n[Symbol.toPrimitive];
            if (void 0 !== t) {
              var a = t.call(n, "string");
              if ("object" !== Jt(a)) return a;
              throw new TypeError("@@toPrimitive must return a primitive value.")
            }
            return String(n)
          }(a.key), "symbol" === Jt(o) ? o : String(o)), a)
      }
      var o
    }
    var Qt = function() {
      function n(e) {
        var t = this;
        ! function(n, e) {
          if (!(n instanceof e)) throw new TypeError("Cannot call a class as a function")
        }(this, n), this.player = e, this.player.template.mask.addEventListener("click", (function() {
          t.hide()
        })), this.player.template.subtitlesButton.addEventListener("click", (function() {
          t.adaptiveHeight(), t.show()
        }));
        for (var a = this.player.template.subtitlesItem.length - 1, o = function(n) {
            t.player.template.subtitlesItem[n].addEventListener("click", (function() {
              t.hide(), t.player.options.subtitle.index !== n && (t.player.template.subtitle
                .innerHTML = "<p></p>", t.player.template.subtrack.src = t.player.template
                .subtitlesItem[n].dataset.subtitle, t.player.options.subtitle.index = n, t.player
                .template.subtitle.classList.contains("dplayer-subtitle-hide") && t
                .subContainerShow())
            }))
          }, r = 0; r < a; r++) o(r);
        this.player.template.subtitlesItem[a].addEventListener("click", (function() {
          t.hide(), t.player.options.subtitle.index !== a && (t.player.template.subtitle.innerHTML =
            "<p></p>", t.player.template.subtrack.src = "", t.player.options.subtitle.index = a, t
            .subContainerHide())
        }))
      }
      var e, t;
      return e = n, (t = [{
        key: "subContainerShow",
        value: function() {
          this.player.template.subtitle.classList.remove("dplayer-subtitle-hide"), this.player
            .events.trigger("subtitle_show")
        }
      }, {
        key: "subContainerHide",
        value: function() {
          this.player.template.subtitle.classList.add("dplayer-subtitle-hide"), this.player.events
            .trigger("subtitle_hide")
        }
      }, {
        key: "hide",
        value: function() {
          this.player.template.subtitlesBox.classList.remove("dplayer-subtitles-box-open"), this
            .player.template.mask.classList.remove("dplayer-mask-show"), this.player.controller
            .disableAutoHide = !1
        }
      }, {
        key: "show",
        value: function() {
          this.player.template.subtitlesBox.classList.add("dplayer-subtitles-box-open"), this.player
            .template.mask.classList.add("dplayer-mask-show"), this.player.controller
            .disableAutoHide = !0
        }
      }, {
        key: "adaptiveHeight",
        value: function() {
          var n = 30 * this.player.template.subtitlesItem.length + 14,
            e = .8 * this.player.template.videoWrap.offsetHeight;
          n >= e - 50 ? (this.player.template.subtitlesBox.style.bottom = "8px", this.player
            .template.subtitlesBox.style["max-height"] = e - 8 + "px") : (this.player.template
            .subtitlesBox.style.bottom = "50px", this.player.template.subtitlesBox.style[
              "max-height"] = e - 50 + "px")
        }
      }]) && Ht(e.prototype, t), Object.defineProperty(e, "prototype", {
        writable: !1
      }), n
    }();
    const _t = Qt;

    function Zt(n) {
      return Zt = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(n) {
        return typeof n
      } : function(n) {
        return n && "function" == typeof Symbol && n.constructor === Symbol && n !== Symbol.prototype ?
          "symbol" : typeof n
      }, Zt(n)
    }

    function Vt(n, e) {
      for (var t = 0; t < e.length; t++) {
        var a = e[t];
        a.enumerable = a.enumerable || !1, a.configurable = !0, "value" in a && (a.writable = !0), Object
          .defineProperty(n, (void 0, o = function(n, e) {
            if ("object" !== Zt(n) || null === n) return n;
            var t = n[Symbol.toPrimitive];
            if (void 0 !== t) {
              var a = t.call(n, "string");
              if ("object" !== Zt(a)) return a;
              throw new TypeError("@@toPrimitive must return a primitive value.")
            }
            return String(n)
          }(a.key), "symbol" === Zt(o) ? o : String(o)), a)
      }
      var o
    }
    var Xt = function() {
      function n(e) {
        ! function(n, e) {
          if (!(n instanceof e)) throw new TypeError("Cannot call a class as a function")
        }(this, n), this.elements = {}, this.elements.volume = e.volumeBar, this.elements.played = e
          .playedBar, this.elements.loaded = e.loadedBar, this.elements.danmaku = e.danmakuOpacityBar
      }
      var e, t;
      return e = n, (t = [{
        key: "set",
        value: function(n, e, t) {
          e = Math.max(e, 0), e = Math.min(e, 1), this.elements[n].style[t] = 100 * e + "%"
        }
      }, {
        key: "get",
        value: function(n) {
          return parseFloat(this.elements[n].style.width) / 100
        }
      }]) && Vt(e.prototype, t), Object.defineProperty(e, "prototype", {
        writable: !1
      }), n
    }();
    const Kt = Xt;

    function Gt(n) {
      return Gt = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(n) {
        return typeof n
      } : function(n) {
        return n && "function" == typeof Symbol && n.constructor === Symbol && n !== Symbol.prototype ?
          "symbol" : typeof n
      }, Gt(n)
    }

    function $t(n, e) {
      for (var t = 0; t < e.length; t++) {
        var a = e[t];
        a.enumerable = a.enumerable || !1, a.configurable = !0, "value" in a && (a.writable = !0), Object
          .defineProperty(n, (void 0, o = function(n, e) {
            if ("object" !== Gt(n) || null === n) return n;
            var t = n[Symbol.toPrimitive];
            if (void 0 !== t) {
              var a = t.call(n, "string");
              if ("object" !== Gt(a)) return a;
              throw new TypeError("@@toPrimitive must return a primitive value.")
            }
            return String(n)
          }(a.key), "symbol" === Gt(o) ? o : String(o)), a)
      }
      var o
    }
    var na = function() {
      function n(e) {
        ! function(n, e) {
          if (!(n instanceof e)) throw new TypeError("Cannot call a class as a function")
        }(this, n), this.player = e, window.requestAnimationFrame = window.requestAnimationFrame || window
          .webkitRequestAnimationFrame || window.mozRequestAnimationFrame || window
          .oRequestAnimationFrame || window.msRequestAnimationFrame || function(n) {
            window.setTimeout(n, 1e3 / 60)
          }, this.types = ["loading", "info", "fps"], this.init()
      }
      var e, t;
      return e = n, (t = [{
        key: "init",
        value: function() {
          var n = this;
          this.types.map((function(e) {
            return "fps" !== e && n["init".concat(e, "Checker")](), e
          }))
        }
      }, {
        key: "initloadingChecker",
        value: function() {
          var n = this,
            e = 0,
            t = 0,
            a = !1;
          this.loadingChecker = setInterval((function() {
            n.enableloadingChecker && (t = n.player.video.currentTime, a || t !== e || n
              .player.video.paused || (n.player.container.classList.add("dplayer-loading"),
                a = !0), a && t > e && !n.player.video.paused && (n.player.container
                .classList.remove("dplayer-loading"), a = !1), e = t)
          }), 100)
        }
      }, {
        key: "initfpsChecker",
        value: function() {
          var n = this;
          window.requestAnimationFrame((function() {
            if (n.enablefpsChecker)
              if (n.initfpsChecker(), n.fpsStart) {
                n.fpsIndex++;
                var e = new Date;
                e - n.fpsStart > 1e3 && (n.player.infoPanel.fps(n.fpsIndex / (e - n
                  .fpsStart) * 1e3), n.fpsStart = new Date, n.fpsIndex = 0)
              } else n.fpsStart = new Date, n.fpsIndex = 0;
            else n.fpsStart = 0, n.fpsIndex = 0
          }))
        }
      }, {
        key: "initinfoChecker",
        value: function() {
          var n = this;
          this.infoChecker = setInterval((function() {
            n.enableinfoChecker && n.player.infoPanel.update()
          }), 1e3)
        }
      }, {
        key: "enable",
        value: function(n) {
          this["enable".concat(n, "Checker")] = !0, "fps" === n && this.initfpsChecker()
        }
      }, {
        key: "disable",
        value: function(n) {
          this["enable".concat(n, "Checker")] = !1
        }
      }, {
        key: "destroy",
        value: function() {
          var n = this;
          this.types.map((function(e) {
            return n["enable".concat(e, "Checker")] = !1, n["".concat(e, "Checker")] &&
              clearInterval(n["".concat(e, "Checker")]), e
          }))
        }
      }]) && $t(e.prototype, t), Object.defineProperty(e, "prototype", {
        writable: !1
      }), n
    }();
    const ea = na;

    function ta(n) {
      return ta = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(n) {
        return typeof n
      } : function(n) {
        return n && "function" == typeof Symbol && n.constructor === Symbol && n !== Symbol.prototype ?
          "symbol" : typeof n
      }, ta(n)
    }

    function aa(n, e) {
      for (var t = 0; t < e.length; t++) {
        var a = e[t];
        a.enumerable = a.enumerable || !1, a.configurable = !0, "value" in a && (a.writable = !0), Object
          .defineProperty(n, (void 0, o = function(n, e) {
            if ("object" !== ta(n) || null === n) return n;
            var t = n[Symbol.toPrimitive];
            if (void 0 !== t) {
              var a = t.call(n, "string");
              if ("object" !== ta(a)) return a;
              throw new TypeError("@@toPrimitive must return a primitive value.")
            }
            return String(n)
          }(a.key), "symbol" === ta(o) ? o : String(o)), a)
      }
      var o
    }
    const oa = function() {
      function n(e) {
        var t = this;
        ! function(n, e) {
          if (!(n instanceof e)) throw new TypeError("Cannot call a class as a function")
        }(this, n), this.container = e, this.container.addEventListener("animationend", (function() {
          t.container.classList.remove("dplayer-bezel-transition")
        }))
      }
      var e, t;
      return e = n, (t = [{
        key: "switch",
        value: function(n) {
          this.container.innerHTML = n, this.container.classList.add("dplayer-bezel-transition")
        }
      }]) && aa(e.prototype, t), Object.defineProperty(e, "prototype", {
        writable: !1
      }), n
    }();

    function ra(n) {
      return ra = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(n) {
        return typeof n
      } : function(n) {
        return n && "function" == typeof Symbol && n.constructor === Symbol && n !== Symbol.prototype ?
          "symbol" : typeof n
      }, ra(n)
    }

    function ia(n, e) {
      for (var t = 0; t < e.length; t++) {
        var a = e[t];
        a.enumerable = a.enumerable || !1, a.configurable = !0, "value" in a && (a.writable = !0), Object
          .defineProperty(n, (void 0, o = function(n, e) {
            if ("object" !== ra(n) || null === n) return n;
            var t = n[Symbol.toPrimitive];
            if (void 0 !== t) {
              var a = t.call(n, "string");
              if ("object" !== ra(a)) return a;
              throw new TypeError("@@toPrimitive must return a primitive value.")
            }
            return String(n)
          }(a.key), "symbol" === ra(o) ? o : String(o)), a)
      }
      var o
    }
    var la = function() {
      function n(e) {
        ! function(n, e) {
          if (!(n instanceof e)) throw new TypeError("Cannot call a class as a function")
        }(this, n), this.container = e.container, this.barWidth = e.barWidth, this.container.style
          .backgroundImage = "url('".concat(e.url, "')"), this.events = e.events
      }
      var e, t;
      return e = n, (t = [{
        key: "resize",
        value: function(n, e, t) {
          this.container.style.width = "".concat(n, "px"), this.container.style.height = "".concat(
            e, "px"), this.container.style.top = "".concat(2 - e, "px"), this.barWidth = t
        }
      }, {
        key: "show",
        value: function() {
          this.container.style.display = "block", this.events && this.events.trigger(
            "thumbnails_show")
        }
      }, {
        key: "move",
        value: function(n) {
          this.container.style.backgroundPosition = "-".concat(160 * (Math.ceil(n / this.barWidth *
            100) - 1), "px 0"), this.container.style.left = "".concat(Math.min(Math.max(n - this
            .container.offsetWidth / 2, -10), this.barWidth - 150), "px")
        }
      }, {
        key: "hide",
        value: function() {
          this.container.style.display = "none", this.events && this.events.trigger(
            "thumbnails_hide")
        }
      }]) && ia(e.prototype, t), Object.defineProperty(e, "prototype", {
        writable: !1
      }), n
    }();
    const sa = la;

    function pa(n) {
      return pa = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(n) {
        return typeof n
      } : function(n) {
        return n && "function" == typeof Symbol && n.constructor === Symbol && n !== Symbol.prototype ?
          "symbol" : typeof n
      }, pa(n)
    }

    function da(n, e) {
      for (var t = 0; t < e.length; t++) {
        var a = e[t];
        a.enumerable = a.enumerable || !1, a.configurable = !0, "value" in a && (a.writable = !0), Object
          .defineProperty(n, (void 0, o = function(n, e) {
            if ("object" !== pa(n) || null === n) return n;
            var t = n[Symbol.toPrimitive];
            if (void 0 !== t) {
              var a = t.call(n, "string");
              if ("object" !== pa(a)) return a;
              throw new TypeError("@@toPrimitive must return a primitive value.")
            }
            return String(n)
          }(a.key), "symbol" === pa(o) ? o : String(o)), a)
      }
      var o
    }
    var ca, Aa = !0,
      ua = !1,
      ya = function() {
        function n(e) {
          ! function(n, e) {
            if (!(n instanceof e)) throw new TypeError("Cannot call a class as a function")
          }(this, n), this.player = e, this.autoHideTimer = 0, q.isMobile || (this.setAutoHideHandler = this
              .setAutoHide.bind(this), this.player.container.addEventListener("mousemove", this
                .setAutoHideHandler), this.player.container.addEventListener("click", this
              .setAutoHideHandler), this.player.on("play", this.setAutoHideHandler), this.player.on("pause",
                this.setAutoHideHandler)), this.initPlayButton(), this.initThumbnails(), this.initPlayedBar(),
            this.initFullButton(), this.initQualityButton(), this.initScreenshotButton(), this.player.options
            .subtitle && "string" == typeof this.player.options.subtitle.url && this.initSubtitleButton(),
            this.initHighlights(),this.initChromecastButton(), q.isMobile || this
            .initVolumeButton()
        }
        var e, t;
        return e = n, (t = [{
          key: "initPlayButton",
          value: function() {
            var n = this;
            this.player.template.playButton.addEventListener("click", (function() {
              n.player.toggle()
            })), this.player.template.mobilePlayButton.addEventListener("click", (function() {
              n.player.toggle()
            })), q.isMobile ? (this.player.template.videoWrap.addEventListener("click", (
          function() {
              n.toggle()
            })), this.player.template.controllerMask.addEventListener("click", (function() {
              n.toggle()
            }))) : this.player.options.preventClickToggle || (this.player.template.videoWrap
              .addEventListener("click", (function() {
                n.player.toggle()
              })), this.player.template.controllerMask.addEventListener("click", (function() {
                n.player.toggle()
              })))
          }
        }, {
          key: "initHighlights",
          value: function() {
            var n = this;
            this.player.on("durationchange", (function() {
              if (1 !== n.player.video.duration && n.player.video.duration !== 1 / 0 && n.player
                .options.highlight) {
                var e = n.player.template.playedBarWrap.querySelectorAll(".dplayer-highlight");
                [].slice.call(e, 0).forEach((function(e) {
                  n.player.template.playedBarWrap.removeChild(e)
                }));
                for (var t = 0; t < n.player.options.highlight.length; t++)
                  if (n.player.options.highlight[t].text && n.player.options.highlight[t]
                    .time) {
                    var a = document.createElement("div");
                    a.classList.add("dplayer-highlight"), a.style.left = n.player.options
                      .highlight[t].time / n.player.video.duration * 100 + "%", a.innerHTML =
                      '<span class="dplayer-highlight-text">' + n.player.options.highlight[t]
                      .text + "</span>", n.player.template.playedBarWrap.insertBefore(a, n
                        .player.template.playedBarTime)
                  }
              }
            }))
          }
        }, {
          key: "initThumbnails",
          value: function() {
            var n = this;
            this.player.options.video.thumbnails && (this.thumbnails = new sa({
              container: this.player.template.barPreview,
              barWidth: this.player.template.barWrap.offsetWidth,
              url: this.player.options.video.thumbnails,
              events: this.player.events
            }), this.player.on("loadedmetadata", (function() {
              n.thumbnails.resize(160, n.player.video.videoHeight / n.player.video
                .videoWidth * 160, n.player.template.barWrap.offsetWidth)
            })))
          }
        }, {
          key: "initPlayedBar",
          value: function() {
            var n = this,
              e = function(e) {
                var t = ((e.clientX || e.changedTouches[0].clientX) - q.getBoundingClientRectViewLeft(
                  n.player.template.playedBarWrap)) / n.player.template.playedBarWrap.clientWidth;
                t = Math.max(t, 0), t = Math.min(t, 1), n.player.bar.set("played", t, "width"), n
                  .player.template.ptime.innerHTML = q.secondToTime(t * n.player.video.duration)
              },
              t = function t(a) {
                document.removeEventListener(q.nameMap.dragEnd, t), document.removeEventListener(q
                  .nameMap.dragMove, e);
                var o = ((a.clientX || a.changedTouches[0].clientX) - q.getBoundingClientRectViewLeft(
                  n.player.template.playedBarWrap)) / n.player.template.playedBarWrap.clientWidth;
                o = Math.max(o, 0), o = Math.min(o, 1), n.player.bar.set("played", o, "width"), n
                  .player.seek(n.player.bar.get("played") * n.player.video.duration), n.player
                  .moveBar = !1
              };
            this.player.template.playedBarWrap.addEventListener(q.nameMap.dragStart, (function() {
              n.player.moveBar = !0, document.addEventListener(q.nameMap.dragMove, e), document
                .addEventListener(q.nameMap.dragEnd, t)
            })), this.player.template.playedBarWrap.addEventListener(q.nameMap.dragMove, (function(
              e) {
              if (n.player.video.duration) {
                var t = n.player.template.playedBarWrap.getBoundingClientRect().left,
                  a = (e.clientX || e.changedTouches[0].clientX) - t;
                if (a < 0 || a > n.player.template.playedBarWrap.offsetWidth) return;
                var o = n.player.video.duration * (a / n.player.template.playedBarWrap
                  .offsetWidth);
                q.isMobile && n.thumbnails && n.thumbnails.show(), n.thumbnails && n.thumbnails
                  .move(a), n.player.template.playedBarTime.style.left = "".concat(a - (o >=
                    3600 ? 25 : 20), "px"), n.player.template.playedBarTime.innerText = q
                  .secondToTime(o), n.player.template.playedBarTime.classList.remove("hidden")
              }
            })), this.player.template.playedBarWrap.addEventListener(q.nameMap.dragEnd, (
          function() {
              q.isMobile && n.thumbnails && n.thumbnails.hide()
            })), q.isMobile || (this.player.template.playedBarWrap.addEventListener("mouseenter", (
              function() {
                n.player.video.duration && (n.thumbnails && n.thumbnails.show(), n.player
                  .template.playedBarTime.classList.remove("hidden"))
              })), this.player.template.playedBarWrap.addEventListener("mouseleave", (function() {
                // // 改
                // let setting=document.querySelector('.dplayer-setting-box')
                // setting.addEventListener('mouseleave', function() {
                //   setting.className='dplayer-setting-box'
                // })
              n.player.video.duration && (n.thumbnails && n.thumbnails.hide(), n.player
                .template.playedBarTime.classList.add("hidden"))
            })))
          }
        }, {
          key: "initFullButton",
          value: function() {
            var n = this;
            this.player.template.browserFullButton.addEventListener("click", (function() {
              n.player.fullScreen.toggle("browser")
            }))
          }
        }, {
          key: "initVolumeButton",
          value: function() {
            var n = this,
              e = function(e) {
                var t = e || window.event,
                  a = ((t.clientX || t.changedTouches[0].clientX) - q.getBoundingClientRectViewLeft(n
                    .player.template.volumeBarWrap) - 5.5) / 35;
                n.player.volume(a)
              },
              t = function t() {
                document.removeEventListener(q.nameMap.dragEnd, t), document.removeEventListener(q
                  .nameMap.dragMove, e), n.player.template.volumeButton.classList.remove(
                  "dplayer-volume-active")
              };
            this.player.template.volumeBarWrapWrap.addEventListener("click", (function(e) {
              var t = e || window.event,
                a = ((t.clientX || t.changedTouches[0].clientX) - q
                  .getBoundingClientRectViewLeft(n.player.template.volumeBarWrap) - 5.5) / 35;
              n.player.volume(a)
            })), this.player.template.volumeBarWrapWrap.addEventListener(q.nameMap.dragStart, (
              function() {
                document.addEventListener(q.nameMap.dragMove, e), document.addEventListener(q
                  .nameMap.dragEnd, t), n.player.template.volumeButton.classList.add(
                  "dplayer-volume-active")
              })), this.player.template.volumeButtonIcon.addEventListener("click", (function() {
              n.player.video.muted ? (n.player.video.muted = !1, n.player.switchVolumeIcon(), n
                .player.bar.set("volume", n.player.volume(), "width")) : (n.player.video
                .muted = !0, n.player.template.volumeIcon.innerHTML = bt.volumeOff, n.player
                .bar.set("volume", 0, "width"))
            }))
          }
        }, {
          key: "initQualityButton",
          value: function() {
            var n = this;
            this.player.options.video.quality && this.player.template.qualityList.addEventListener(
              "click", (function(e) {
                e.target.classList.contains("dplayer-quality-item") && n.player.switchQuality(e
                  .target.dataset.index)
              }))
          }
        }, {
          key: "initScreenshotButton",
          value: function() {
            var n = this;
            this.player.options.screenshot && this.player.template.camareButton.addEventListener(
              "click", (function() {
                var e, t = document.createElement("canvas");
                t.width = n.player.video.videoWidth, t.height = n.player.video.videoHeight, t
                  .getContext("2d").drawImage(n.player.video, 0, 0, t.width, t.height), t.toBlob((
                    function(t) {
                      e = URL.createObjectURL(t);
                      var a = document.createElement("a");
                      a.href = e, a.download = "DPlayer.png", a.style.display = "none", document
                        .body.appendChild(a), a.click(), document.body.removeChild(a), URL
                        .revokeObjectURL(e), n.player.events.trigger("screenshot", e)
                    }))
              }))
          }
        }, {
          key: "initChromecast",
          value: function() {
            var n = window.document.createElement("script");
            n.setAttribute("type", "text/javascript"), n.setAttribute("src",
                "https://www.gstatic.com/cv/js/sender/v1/cast_sender.js?loadCastFramework=1"), window
              .document.body.appendChild(n), window.__onGCastApiAvailable = function(n) {
                if (n) {
                  var e = new(ca = window.chrome.cast).SessionRequest(ca.media
                      .DEFAULT_MEDIA_RECEIVER_APP_ID),
                    t = new ca.ApiConfig(e, (function() {}), (function(n) {
                      n === ca.ReceiverAvailability.AVAILABLE && console.log("chromecast: ", n)
                    }));
                  ca.initialize(t, (function() {}))
                }
              }
          }
        }, {
          key: "initChromecastButton",
          value: function() {
            var n = this;
            if (this.player.options.chromecast) {
              Aa && (Aa = !1, this.initChromecast());
              var e = function(e, t) {
                  n.currentMedia = t
                },
                t = function(n) {
                  console.error("Error launching media", n)
                };
              this.player.template.chromecastButton.addEventListener("click", (function() {
                ua ? (ua = !1, n.currentMedia.stop(), n.session.stop(), n.initChromecast()) : (
                  ua = !0, ca.requestSession((function(a) {
                    var o, r, i;
                    n.session = a, o = n.player.options.video.url, r = new ca.media
                      .MediaInfo(o), i = new ca.media.LoadRequest(r), n.session ? n
                      .session.loadMedia(i, e.bind(n, "loadMedia"), t).play() : window
                      .open(o)
                  }), (function(e) {
                    "cancel" === e.code ? n.session = void 0 : console.error(
                      "Error selecting a cast device", e)
                  })))
              }))
            }
          }
        }, {
          key: "initSubtitleButton",
          value: function() {
            var n = this;
            this.player.events.on("subtitle_show", (function() {
              n.player.template.subtitleButton.dataset.balloon = n.player.tran("hide-subs"), n
                .player.template.subtitleButtonInner.style.opacity = "", n.player.user.set(
                  "subtitle", 1)
            })), this.player.events.on("subtitle_hide", (function() {
              n.player.template.subtitleButton.dataset.balloon = n.player.tran("show-subs"), n
                .player.template.subtitleButtonInner.style.opacity = "0.4", n.player.user.set(
                  "subtitle", 0)
            })), this.player.template.subtitleButton.addEventListener("click", (function() {
              n.player.subtitle.toggle()
            }))
          }
        }, {
          key: "setAutoHide",
          value: function() {
            var n = this;
            this.show(), clearTimeout(this.autoHideTimer), this.autoHideTimer = setTimeout((
          function() {
              !n.player.video.played.length || n.player.paused || n.disableAutoHide || n.hide()
            }), 3e3)
          }
        }, {
          key: "show",
          value: function() {
            // 显示底部菜单
            this.player.container.classList.remove("dplayer-hide-controller")
            if (document.fullscreenElement) {
              //显示视频标题
              document.getElementById('fulltit').style.display='block'
            } else {
              //隐藏视频标题
              if(document.getElementById('fulltit')){
                document.getElementById('fulltit').style.display='none'
              }
            }
          }
        }, {
          key: "hide",
          value: function() {
            //改，底部菜单栏隐藏
            this.player.container.classList.add("dplayer-hide-controller"), this.player.setting
            .hide(), this.player.comment && this.player.comment.hide()
            //同时隐藏倍速，全屏title
            document.querySelector('.dplayer-setting-box').className = 'dplayer-setting-box'
            //隐藏视频标题
            if(document.getElementById('fulltit')){
              document.getElementById('fulltit').style.display='none'
            }
          }
        }, {
          key: "isShow",
          value: function() {
            return !this.player.container.classList.contains("dplayer-hide-controller")
          }
        }, {
          key: "toggle",
          value: function() {
            this.isShow() ? this.hide() : this.show()
          }
        }, {
          key: "destroy",
          value: function() {
            q.isMobile || (this.player.container.removeEventListener("mousemove", this
              .setAutoHideHandler), this.player.container.removeEventListener("click", this
              .setAutoHideHandler)), clearTimeout(this.autoHideTimer)
          }
        }]) && da(e.prototype, t), Object.defineProperty(e, "prototype", {
          writable: !1
        }), n
      }();
    const ma = ya;

    function ha(n) {
      return ha = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(n) {
        return typeof n
      } : function(n) {
        return n && "function" == typeof Symbol && n.constructor === Symbol && n !== Symbol.prototype ?
          "symbol" : typeof n
      }, ha(n)
    }

    function fa(n, e) {
      for (var t = 0; t < e.length; t++) {
        var a = e[t];
        a.enumerable = a.enumerable || !1, a.configurable = !0, "value" in a && (a.writable = !0), Object
          .defineProperty(n, (void 0, o = function(n, e) {
            if ("object" !== ha(n) || null === n) return n;
            var t = n[Symbol.toPrimitive];
            if (void 0 !== t) {
              var a = t.call(n, "string");
              if ("object" !== ha(a)) return a;
              throw new TypeError("@@toPrimitive must return a primitive value.")
            }
            return String(n)
          }(a.key), "symbol" === ha(o) ? o : String(o)), a)
      }
      var o
    }
    var ba = function() {
      function n(e) {
        var t = this;
        ! function(n, e) {
          if (!(n instanceof e)) throw new TypeError("Cannot call a class as a function")
        }(this, n), this.player = e, this.player.template.mask.addEventListener("click", (function() {
            t.hide()
          })), 
          // 改 注释倍速点击
          // this.player.template.settingButton.addEventListener("click", (function() {
          //   t.show()
          // })), 
          this.loop = this.player.options.loop
        for (var a = function(n) {
            t.player.template.speedItem[n].addEventListener("click", (function() {
              t.player.speed(t.player.template.speedItem[n].dataset.speed), t.hide()
            }))
          }, o = 0; o < this.player.template.speedItem.length; o++) a(o);
        if (this.player.danmaku) {
          this.player.on("danmaku_opacity", (function(n) {
            t.player.bar.set("danmaku", n, "width"), t.player.user.set("opacity", n)
          })), this.player.danmaku.opacity(this.player.user.get("opacity"));
          var r = function(n) {
              var e = n || window.event,
                a = ((e.clientX || e.changedTouches[0].clientX) - q.getBoundingClientRectViewLeft(t.player
                  .template.danmakuOpacityBarWrap)) / 130;
              a = Math.max(a, 0), a = Math.min(a, 1), t.player.danmaku.opacity(a)
            },
            i = function n() {
              document.removeEventListener(q.nameMap.dragEnd, n), document.removeEventListener(q.nameMap
                .dragMove, r), t.player.template.danmakuOpacityBox.classList.remove(
                "dplayer-setting-danmaku-active")
            };
          this.player.template.danmakuOpacityBarWrapWrap.addEventListener("click", (function(n) {
            var e = n || window.event,
              a = ((e.clientX || e.changedTouches[0].clientX) - q.getBoundingClientRectViewLeft(t
                .player.template.danmakuOpacityBarWrap)) / 130;
            a = Math.max(a, 0), a = Math.min(a, 1), t.player.danmaku.opacity(a)
          })), this.player.template.danmakuOpacityBarWrapWrap.addEventListener(q.nameMap.dragStart, (
            function() {
              document.addEventListener(q.nameMap.dragMove, r), document.addEventListener(q.nameMap
                .dragEnd, i), t.player.template.danmakuOpacityBox.classList.add(
                "dplayer-setting-danmaku-active")
            }))
        }
      }
      var e, t;
      return e = n, (t = [{
        key: "hide",
        value: function() {
          //改倍速弹窗延迟隐藏
          // var n = this;
          // this.player.template.settingBox.classList.remove("dplayer-setting-box-open"), this.player
          //   .template.mask.classList.remove("dplayer-mask-show"), setTimeout((function() {
          //     n.player.template.settingBox.classList.remove("dplayer-setting-box-narrow"), n
          //       .player.template.settingBox.classList.remove("dplayer-setting-box-speed")
          //   }), 300), this.player.controller.disableAutoHide = !1
        }
      }, {
        key: "show",
        value: function() {
          this.player.template.settingBox.classList.add("dplayer-setting-box-open"), this.player
            .template.mask.classList.add("dplayer-mask-show"), this.player.controller
            .disableAutoHide = !0
        }
      }]) && fa(e.prototype, t), Object.defineProperty(e, "prototype", {
        writable: !1
      }), n
    }();
    const ga = ba;

    function va(n) {
      return va = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(n) {
        return typeof n
      } : function(n) {
        return n && "function" == typeof Symbol && n.constructor === Symbol && n !== Symbol.prototype ?
          "symbol" : typeof n
      }, va(n)
    }

    function xa(n, e) {
      for (var t = 0; t < e.length; t++) {
        var a = e[t];
        a.enumerable = a.enumerable || !1, a.configurable = !0, "value" in a && (a.writable = !0), Object
          .defineProperty(n, (void 0, o = function(n, e) {
            if ("object" !== va(n) || null === n) return n;
            var t = n[Symbol.toPrimitive];
            if (void 0 !== t) {
              var a = t.call(n, "string");
              if ("object" !== va(a)) return a;
              throw new TypeError("@@toPrimitive must return a primitive value.")
            }
            return String(n)
          }(a.key), "symbol" === va(o) ? o : String(o)), a)
      }
      var o
    }
    var Ea = function() {
      function n(e) {
        var t = this;
        ! function(n, e) {
          if (!(n instanceof e)) throw new TypeError("Cannot call a class as a function")
        }(this, n), this.player = e, this.player.template.mask.addEventListener("click", (function() {
          t.hide()
        })), this.player.template.commentButton.addEventListener("click", (function() {
          t.show()
        })), this.player.template.commentSettingButton.addEventListener("click", (function() {
          t.toggleSetting()
        })), this.player.template.commentColorSettingBox.addEventListener("click", (function() {
          if (t.player.template.commentColorSettingBox.querySelector("input:checked+span")) {
            var n = t.player.template.commentColorSettingBox.querySelector("input:checked").value;
            t.player.template.commentSettingFill.style.fill = n, t.player.template.commentInput.style
              .color = n, t.player.template.commentSendFill.style.fill = n
          }
        })), this.player.template.commentInput.addEventListener("click", (function() {
          t.hideSetting()
        })), this.player.template.commentInput.addEventListener("keydown", (function(n) {
          13 === (n || window.event).keyCode && t.send()
        })), this.player.template.commentSendButton.addEventListener("click", (function() {
          t.send()
        }))
      }
      var e, t;
      return e = n, (t = [{
        key: "show",
        value: function() {
          this.player.controller.disableAutoHide = !0, this.player.template.controller.classList
            .add("dplayer-controller-comment"), this.player.template.mask.classList.add(
              "dplayer-mask-show"), this.player.container.classList.add("dplayer-show-controller"),
            this.player.template.commentInput.focus()
        }
      }, {
        key: "hide",
        value: function() {
          this.player.template.controller.classList.remove("dplayer-controller-comment"), this
            .player.template.mask.classList.remove("dplayer-mask-show"), this.player.container
            .classList.remove("dplayer-show-controller"), this.player.controller.disableAutoHide = !
            1, this.hideSetting()
        }
      }, {
        key: "showSetting",
        value: function() {
          this.player.template.commentSettingBox.classList.add("dplayer-comment-setting-open")
        }
      }, {
        key: "hideSetting",
        value: function() {
          this.player.template.commentSettingBox.classList.remove("dplayer-comment-setting-open")
        }
      }, {
        key: "toggleSetting",
        value: function() {
          this.player.template.commentSettingBox.classList.contains(
            "dplayer-comment-setting-open") ? this.hideSetting() : this.showSetting()
        }
      }, {
        key: "send",
        value: function() {
          var n = this;
          this.player.template.commentInput.blur(), this.player.template.commentInput.value.replace(
            /^\s+|\s+$/g, "") ? this.player.danmaku.send({
            text: this.player.template.commentInput.value,
            color: q.color2Number(this.player.container.querySelector(
              ".dplayer-comment-setting-color input:checked").value),
            type: parseInt(this.player.container.querySelector(
              ".dplayer-comment-setting-type input:checked").value)
          }, (function() {
            n.player.template.commentInput.value = "", n.hide()
          })) : this.player.notice(this.player.tran("please-input-danmaku"))
        }
      }]) && xa(e.prototype, t), Object.defineProperty(e, "prototype", {
        writable: !1
      }), n
    }();
    const wa = Ea;

    function Ca(n) {
      return Ca = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(n) {
        return typeof n
      } : function(n) {
        return n && "function" == typeof Symbol && n.constructor === Symbol && n !== Symbol.prototype ?
          "symbol" : typeof n
      }, Ca(n)
    }

    function ka(n, e) {
      for (var t = 0; t < e.length; t++) {
        var a = e[t];
        a.enumerable = a.enumerable || !1, a.configurable = !0, "value" in a && (a.writable = !0), Object
          .defineProperty(n, (void 0, o = function(n, e) {
            if ("object" !== Ca(n) || null === n) return n;
            var t = n[Symbol.toPrimitive];
            if (void 0 !== t) {
              var a = t.call(n, "string");
              if ("object" !== Ca(a)) return a;
              throw new TypeError("@@toPrimitive must return a primitive value.")
            }
            return String(n)
          }(a.key), "symbol" === Ca(o) ? o : String(o)), a)
      }
      var o
    }
    var Ba = function() {
      function n(e) {
        ! function(n, e) {
          if (!(n instanceof e)) throw new TypeError("Cannot call a class as a function")
        }(this, n), this.player = e, this.doHotKeyHandler = this.doHotKey.bind(this), this
          .cancelFullScreenHandler = this.cancelFullScreen.bind(this), this.player.options.hotkey &&
          document.addEventListener("keydown", this.doHotKeyHandler), document.addEventListener("keydown",
            this.cancelFullScreenHandler)
      }
      var e, t;
      return e = n, (t = [{
        key: "doHotKey",
        value: function(n) {
          if (this.player.focus) {
            var e = document.activeElement.tagName.toUpperCase(),
              t = document.activeElement.getAttribute("contenteditable");
            if ("INPUT" !== e && "TEXTAREA" !== e && "" !== t && "true" !== t) {
              var a, o = n || window.event;
              switch (o.keyCode) {
                case 32:
                  o.preventDefault(), this.player.toggle();
                  break;
                case 37:
                  if (o.preventDefault(), this.player.options.live) break;
                  this.player.seek(this.player.video.currentTime - 5), this.player.controller
                    .setAutoHide();
                  break;
                case 39:
                  if (o.preventDefault(), this.player.options.live) break;
                  this.player.seek(this.player.video.currentTime + 5), this.player.controller
                    .setAutoHide();
                  break;
                case 38:
                  o.preventDefault(), a = this.player.volume() + .1, this.player.volume(a);
                  break;
                case 40:
                  o.preventDefault(), a = this.player.volume() - .1, this.player.volume(a)
              }
            }
          }
        }
      }, {
        key: "cancelFullScreen",
        value: function(n) {
          27 === (n || window.event).keyCode && this.player.fullScreen.isFullScreen("web") && this
            .player.fullScreen.cancel("web")
        }
      }, {
        key: "destroy",
        value: function() {
          this.player.options.hotkey && document.removeEventListener("keydown", this
            .doHotKeyHandler), document.removeEventListener("keydown", this
            .cancelFullScreenHandler)
        }
      }]) && ka(e.prototype, t), Object.defineProperty(e, "prototype", {
        writable: !1
      }), n
    }();
    const Sa = Ba;

    function Ia(n) {
      return Ia = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(n) {
        return typeof n
      } : function(n) {
        return n && "function" == typeof Symbol && n.constructor === Symbol && n !== Symbol.prototype ?
          "symbol" : typeof n
      }, Ia(n)
    }

    function za(n, e) {
      for (var t = 0; t < e.length; t++) {
        var a = e[t];
        a.enumerable = a.enumerable || !1, a.configurable = !0, "value" in a && (a.writable = !0), Object
          .defineProperty(n, (void 0, o = function(n, e) {
            if ("object" !== Ia(n) || null === n) return n;
            var t = n[Symbol.toPrimitive];
            if (void 0 !== t) {
              var a = t.call(n, "string");
              if ("object" !== Ia(a)) return a;
              throw new TypeError("@@toPrimitive must return a primitive value.")
            }
            return String(n)
          }(a.key), "symbol" === Ia(o) ? o : String(o)), a)
      }
      var o
    }
    var Ta = function() {
      function n(e) {
        var t = this;
        ! function(n, e) {
          if (!(n instanceof e)) throw new TypeError("Cannot call a class as a function")
        }(this, n), this.player = e, this.shown = !1, Array.prototype.slice.call(this.player.template
          .menuItem).forEach((function(n, e) {
          t.player.options.contextmenu[e].click && n.addEventListener("click", (function() {
            t.player.options.contextmenu[e].click(t.player), t.hide()
          }))
        })), this.contextmenuHandler = function(n) {
          if (t.shown) t.hide();
          else {
            var e = n || window.event;
            e.preventDefault();
            var a = t.player.container.getBoundingClientRect();
            t.show(e.clientX - a.left, e.clientY - a.top), t.player.template.mask.addEventListener(
              "click", (function() {
                t.hide()
              }))
          }
        }, this.player.container.addEventListener("contextmenu", this.contextmenuHandler)
      }
      var e, t;
      return e = n, (t = [{
        key: "show",
        value: function(n, e) {
          this.player.template.menu.classList.add("dplayer-menu-show");
          var t = this.player.container.getBoundingClientRect();
          n + this.player.template.menu.offsetWidth >= t.width ? (this.player.template.menu.style
            .right = t.width - n + "px", this.player.template.menu.style.left = "initial") : (this
            .player.template.menu.style.left = n + "px", this.player.template.menu.style.right =
            "initial"), e + this.player.template.menu.offsetHeight >= t.height ? (this.player
            .template.menu.style.bottom = t.height - e + "px", this.player.template.menu.style
            .top = "initial") : (this.player.template.menu.style.top = e + "px", this.player
            .template.menu.style.bottom = "initial"), this.player.template.mask.classList.add(
            "dplayer-mask-show"), this.shown = !0, this.player.events.trigger("contextmenu_show")
        }
      }, {
        key: "hide",
        value: function() {
          this.player.template.mask.classList.remove("dplayer-mask-show"), this.player.template.menu
            .classList.remove("dplayer-menu-show"), this.shown = !1, this.player.events.trigger(
              "contextmenu_hide")
        }
      }, {
        key: "destroy",
        value: function() {
          this.player.container.removeEventListener("contextmenu", this.contextmenuHandler)
        }
      }]) && za(e.prototype, t), Object.defineProperty(e, "prototype", {
        writable: !1
      }), n
    }();
    const qa = Ta;

    function La(n) {
      return La = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(n) {
        return typeof n
      } : function(n) {
        return n && "function" == typeof Symbol && n.constructor === Symbol && n !== Symbol.prototype ?
          "symbol" : typeof n
      }, La(n)
    }

    function Oa(n, e) {
      for (var t = 0; t < e.length; t++) {
        var a = e[t];
        a.enumerable = a.enumerable || !1, a.configurable = !0, "value" in a && (a.writable = !0), Object
          .defineProperty(n, (void 0, o = function(n, e) {
            if ("object" !== La(n) || null === n) return n;
            var t = n[Symbol.toPrimitive];
            if (void 0 !== t) {
              var a = t.call(n, "string");
              if ("object" !== La(a)) return a;
              throw new TypeError("@@toPrimitive must return a primitive value.")
            }
            return String(n)
          }(a.key), "symbol" === La(o) ? o : String(o)), a)
      }
      var o
    }
    var ja = function() {
      function n(e) {
        var t = this;
        ! function(n, e) {
          if (!(n instanceof e)) throw new TypeError("Cannot call a class as a function")
        }(this, n), this.container = e.template.infoPanel, this.template = e.template, this.video = e.video,
          this.player = e, this.template.infoPanelClose.addEventListener("click", (function() {
            t.hide()
          }))
      }
      var e, t;
      return e = n, (t = [{
        key: "show",
        value: function() {
          this.beginTime = Date.now(), this.update(), this.player.timer.enable("info"), this.player
            .timer.enable("fps"), this.container.classList.remove("dplayer-info-panel-hide")
        }
      }, {
        key: "hide",
        value: function() {
          this.player.timer.disable("info"), this.player.timer.disable("fps"), this.container
            .classList.add("dplayer-info-panel-hide")
        }
      }, {
        key: "triggle",
        value: function() {
          this.container.classList.contains("dplayer-info-panel-hide") ? this.show() : this.hide()
        }
      }, {
        key: "update",
        value: function() {
          this.template.infoVersion.innerHTML = "v".concat("1.27.1", " ").concat(
              "v1.27.1-3-gb0ba40b"), this.template.infoType.innerHTML = this.player.type, this
            .template.infoUrl.innerHTML = this.player.options.video.url, this.template
            .infoResolution.innerHTML = "".concat(this.player.video.videoWidth, " x ").concat(this
              .player.video.videoHeight), this.template.infoDuration.innerHTML = this.player.video
            .duration, this.player.options.danmaku && (this.template.infoDanmakuId.innerHTML = this
              .player.options.danmaku.id, this.template.infoDanmakuApi.innerHTML = this.player
              .options.danmaku.api, this.template.infoDanmakuAmount.innerHTML = this.player.danmaku
              .dan.length)
        }
      }, {
        key: "fps",
        value: function(n) {
          this.template.infoFPS.innerHTML = "".concat(n.toFixed(1))
        }
      }]) && Oa(e.prototype, t), Object.defineProperty(e, "prototype", {
        writable: !1
      }), n
    }();
    const Da = ja;
    var Pa = t(568),
      Ra = t.n(Pa);

    function Ya(n) {
      return Ya = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(n) {
        return typeof n
      } : function(n) {
        return n && "function" == typeof Symbol && n.constructor === Symbol && n !== Symbol.prototype ?
          "symbol" : typeof n
      }, Ya(n)
    }

    function Ma(n, e) {
      var t = Object.keys(n);
      if (Object.getOwnPropertySymbols) {
        var a = Object.getOwnPropertySymbols(n);
        e && (a = a.filter((function(e) {
          return Object.getOwnPropertyDescriptor(n, e).enumerable
        }))), t.push.apply(t, a)
      }
      return t
    }

    function Fa(n, e, t) {
      return (e = Ua(e)) in n ? Object.defineProperty(n, e, {
        value: t,
        enumerable: !0,
        configurable: !0,
        writable: !0
      }) : n[e] = t, n
    }

    function Wa(n, e) {
      for (var t = 0; t < e.length; t++) {
        var a = e[t];
        a.enumerable = a.enumerable || !1, a.configurable = !0, "value" in a && (a.writable = !0), Object
          .defineProperty(n, Ua(a.key), a)
      }
    }

    function Ua(n) {
      var e = function(n, e) {
        if ("object" !== Ya(n) || null === n) return n;
        var t = n[Symbol.toPrimitive];
        if (void 0 !== t) {
          var a = t.call(n, "string");
          if ("object" !== Ya(a)) return a;
          throw new TypeError("@@toPrimitive must return a primitive value.")
        }
        return String(n)
      }(n);
      return "symbol" === Ya(e) ? e : String(e)
    }
    var Na = 0,
      Ja = [],
      Ha = function() {
        function n(e) {
          var t = this;
          (function(n, e) {
            if (!(n instanceof e)) throw new TypeError("Cannot call a class as a function")
          })(this, n), this.options = function(n) {
              var e = {
                container: n.element || document.getElementsByClassName("dplayer")[0],
                live: !1,
                autoplay: !1,
                theme: "#b7daff",
                loop: !1,
                lang: (navigator.language || navigator.browserLanguage).toLowerCase(),
                screenshot: !1,
                airplay: !0,
                chromecast: !1,
                hotkey: !0,
                preload: "metadata",
                volume: .7,
                playbackSpeed: [.5, .75, 1, 1.25, 1.5, 2],
                apiBackend: qe,
                video: {},
                contextmenu: [],
                mutex: !0,
                pluginOptions: {
                  hls: {},
                  flv: {},
                  dash: {},
                  webtorrent: {}
                },
                preventClickToggle: !1
              };
              for (var t in e) e.hasOwnProperty(t) && !n.hasOwnProperty(t) && (n[t] = e[t]);
              return n.video && !n.video.type && (n.video.type = "auto"), "object" === Le(n.danmaku) && n
                .danmaku && !n.danmaku.user && (n.danmaku.user = "DIYgod"), n.subtitle && (!n.subtitle.type &&
                  (n.subtitle.type = "webvtt"), !n.subtitle.fontSize && (n.subtitle.fontSize = "20px"), !n
                  .subtitle.bottom && (n.subtitle.bottom = "40px"), !n.subtitle.color && (n.subtitle.color =
                    "#fff")), n.video.quality && (n.video.url = n.video.quality[n.video.defaultQuality].url),
                n.lang && (n.lang = n.lang.toLowerCase()), n.contextmenu = n.contextmenu.concat([{
                  key: "video-info",
                  click: function(n) {
                    n.infoPanel.triggle()
                  }
                }, {
                  key: "about-author",
                  link: "https://diygod.cc"
                }, {
                  text: "DPlayer v".concat("1.27.1"),
                  link: "https://github.com/MoePlayer/DPlayer"
                }]), n
            }(function(n) {
              for (var e = 1; e < arguments.length; e++) {
                var t = null != arguments[e] ? arguments[e] : {};
                e % 2 ? Ma(Object(t), !0).forEach((function(e) {
                  Fa(n, e, t[e])
                })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(n, Object
                  .getOwnPropertyDescriptors(t)) : Ma(Object(t)).forEach((function(e) {
                  Object.defineProperty(n, e, Object.getOwnPropertyDescriptor(t, e))
                }))
              }
              return n
            }({
              preload: "webtorrent" === e.video.type ? "none" : "metadata"
            }, e)), this.options.video.quality && (this.qualityIndex = this.options.video.defaultQuality, this
              .quality = this.options.video.quality[this.options.video.defaultQuality]), this.tran = new Oe(
              this.options.lang).tran, this.events = new qt, this.user = new Mt(this), this.container = this
            .options.container, this.noticeList = {}, this.container.classList.add("dplayer"), this.options
            .danmaku || this.container.classList.add("dplayer-no-danmaku"), this.options.live ? this.container
            .classList.add("dplayer-live") : this.container.classList.remove("dplayer-live"), q.isMobile &&
            this.container.classList.add("dplayer-mobile"), this.arrow = this.container.offsetWidth <= 500,
            this.arrow && this.container.classList.add("dplayer-arrow"), this.options.subtitle && Array
            .isArray(this.options.subtitle.url) && (this.options.subtitle.url.push({
              subtitle: "",
              lang: "off"
            }), this.options.subtitle.defaultSubtitle && ("string" == typeof this.options.subtitle
              .defaultSubtitle ? this.options.subtitle.index = this.options.subtitle.url.findIndex((
                function(n) {
                  return n.lang === t.options.subtitle.defaultSubtitle || n.name === t.options.subtitle
                    .defaultSubtitle
                })) : "number" == typeof this.options.subtitle.defaultSubtitle && (this.options.subtitle
                .index = this.options.subtitle.defaultSubtitle)), (-1 === this.options.subtitle.index || !
              this.options.subtitle.index || this.options.subtitle.index > this.options.subtitle.url
              .length - 1) && (this.options.subtitle.index = this.options.subtitle.url.findIndex((function(
              n) {
              return n.lang === t.options.lang
            }))), -1 === this.options.subtitle.index && (this.options.subtitle.index = this.options.subtitle
              .url.length - 1)), this.template = new Ct({
              container: this.container,
              options: this.options,
              index: Na,
              tran: this.tran
            }), this.video = this.template.video, this.bar = new Kt(this.template), this.bezel = new oa(this
              .template.bezel), this.fullScreen = new Dt(this), this.controller = new ma(this), this.options
            .danmaku && (this.danmaku = new It({
              player: this,
              container: this.template.danmaku,
              opacity: this.user.get("opacity"),
              callback: function() {
                setTimeout((function() {
                  t.template.danmakuLoading.style.display = "none", t.options.autoplay && t.play()
                }), 0)
              },
              error: function(n) {
                t.notice(n)
              },
              apiBackend: this.options.apiBackend,
              borderColor: this.options.theme,
              height: this.arrow ? 24 : 30,
              time: function() {
                return t.video.currentTime
              },
              unlimited: this.user.get("unlimited"),
              api: {
                id: this.options.danmaku.id,
                address: this.options.danmaku.api,
                token: this.options.danmaku.token,
                maximum: this.options.danmaku.maximum,
                addition: this.options.danmaku.addition,
                user: this.options.danmaku.user,
                speedRate: this.options.danmaku.speedRate
              },
              events: this.events,
              tran: function(n) {
                return t.tran(n)
              }
            }), this.comment = new wa(this)), this.setting = new ga(this), this.plugins = {}, this
            .docClickFun = function() {
              t.focus = !1
            }, this.containerClickFun = function() {
              t.focus = !0
            }, document.addEventListener("click", this.docClickFun, !0), this.container.addEventListener(
              "click", this.containerClickFun, !0), this.paused = !0, this.timer = new ea(this), this.hotkey =
            new Sa(this), this.contextmenu = new qa(this), this.initVideo(this.video, this.quality && this
              .quality.type || this.options.video.type), this.infoPanel = new Da(this), !this.danmaku && this
            .options.autoplay && this.play(), this.moveBar = !1, Na++, Ja.push(this)
        }
        var e, t, a;
        return e = n, t = [{
          key: "seek",
          value: function(n) {
            n = Math.max(n, 0), this.video.duration && (n = Math.min(n, this.video.duration)), 
            //改 不显示快退快进
            // this.video.currentTime < n ? this.notice("".concat(this.tran("ff").replace("%s", (n - this
            //     .video.currentTime).toFixed(0)))) : this.video.currentTime > n && this.notice("".concat(
            //     this.tran("rew").replace("%s", (this.video.currentTime - n).toFixed(0)))), 
            this.video.currentTime = n, this.danmaku && this.danmaku.seek(), this.bar.set("played", n / this
                .video.duration, "width"), this.template.ptime.innerHTML = q.secondToTime(n)
          }
        }, {
          key: "play",
          value: function(n) {
            var e = this;
            if (this.paused = !1, this.video.paused && !q.isMobile && this.bezel.switch(bt.play), this
              .template.playButton.innerHTML = bt.pause, this.template.mobilePlayButton.innerHTML = bt
              .pause, n || z.resolve(this.video.play()).catch((function() {
                e.pause()
              })).then((function() {})), this.timer.enable("loading"), this.container.classList.remove(
                "dplayer-paused"), this.container.classList.add("dplayer-playing"), this.danmaku && this
              .danmaku.play(), this.options.mutex)
              for (var t = 0; t < Ja.length; t++) this !== Ja[t] && Ja[t].pause()
          }
        }, {
          key: "pause",
          value: function(n) {
            this.paused = !0, this.container.classList.remove("dplayer-loading"), this.video.paused || q
              .isMobile || this.bezel.switch(bt.pause), this.template.playButton.innerHTML = bt.play,
              this.template.mobilePlayButton.innerHTML = bt.play, n || this.video.pause(), this.timer
              .disable("loading"), this.container.classList.remove("dplayer-playing"), this.container
              .classList.add("dplayer-paused"), this.danmaku && this.danmaku.pause()
          }
        }, {
          key: "switchVolumeIcon",
          value: function() {
            this.volume() >= .95 ? this.template.volumeIcon.innerHTML = bt.volumeUp : this.volume() >
              0 ? this.template.volumeIcon.innerHTML = bt.volumeDown : this.template.volumeIcon
              .innerHTML = bt.volumeOff
          }
        }, {
          key: "volume",
          value: function(n, e, t) {
            if (n = parseFloat(n), !isNaN(n)) {
              n = Math.max(n, 0), n = Math.min(n, 1), this.bar.set("volume", n, "width");
              var a = "".concat((100 * n).toFixed(0), "%");
              this.template.volumeBarWrapWrap.dataset.balloon = a, e || this.user.set("volume", n), t ||
                this.notice("".concat(this.tran("volume"), " ").concat((100 * n).toFixed(0), "%"),
                  void 0, void 0, "volume"), this.video.volume = n, this.video.muted && (this.video
                  .muted = !1), this.switchVolumeIcon()
            }
            return this.video.volume
          }
        }, {
          key: "toggle",
          value: function() {
            this.video.paused ? this.play() : this.pause()
          }
        }, {
          key: "on",
          value: function(n, e) {
            this.events.on(n, e)
          }
        }, {
          //切换视频
          key: "switchVideo",
          value: function(n, e) {
            this.pause(), this.video.poster = n.pic ? n.pic : "", this.video.src = n.url, this.initMSE(
              this.video, n.type || "auto"), e && (this.template.danmakuLoading.style.display =
              "block", this.bar.set("played", 0, "width"), this.bar.set("loaded", 0, "width"), this
              .template.ptime.innerHTML = "00:00", this.template.danmaku.innerHTML = "", this
              .danmaku && this.danmaku.reload({
                id: e.id,
                address: e.api,
                token: e.token,
                maximum: e.maximum,
                addition: e.addition,
                user: e.user
              }))
              //修改标题
              document.getElementById('fulltit').innerHTML=n.title
          }
        }, {
          key: "initMSE",
          value: function(n, e) {
            var t = this;
            if (this.type = e, this.options.video.customType && this.options.video.customType[e])
              "[object Function]" === Object.prototype.toString.call(this.options.video.customType[e]) ?
              this.options.video.customType[e](this.video, this) : console.error("Illegal customType: "
                .concat(e));
            else switch ("auto" === this.type && (/m3u8(#|\?|$)/i.exec(n.src) ? this.type = "hls" :
                /.flv(#|\?|$)/i.exec(n.src) ? this.type = "flv" : /.mpd(#|\?|$)/i.exec(n.src) ? this
                .type = "dash" : this.type = "normal"), "hls" === this.type && (n.canPlayType(
                "application/x-mpegURL") || n.canPlayType("application/vnd.apple.mpegURL")) && (this
                .type = "normal"), this.type) {
              case "hls":
                if (window.Hls)
                  if (window.Hls.isSupported()) {
                    var a = this.options.pluginOptions.hls,
                      o = new window.Hls(a);
                    this.plugins.hls = o, o.loadSource(n.src), o.attachMedia(n), this.events.on(
                      "destroy", (function() {
                        o.destroy(), delete t.plugins.hls
                      }))
                  } else this.notice("Error: Hls is not supported.");
                else this.notice("Error: Can't find Hls.");
                break;
              case "flv":
                if (window.flvjs)
                  if (window.flvjs.isSupported()) {
                    var r = window.flvjs.createPlayer(Object.assign(this.options.pluginOptions.flv
                      .mediaDataSource || {}, {
                        type: "flv",
                        url: n.src
                      }), this.options.pluginOptions.flv.config);
                    this.plugins.flvjs = r, r.attachMediaElement(n), r.load(), this.events.on(
                      "destroy", (function() {
                        r.unload(), r.detachMediaElement(), r.destroy(), delete t.plugins.flvjs
                      }))
                  } else this.notice("Error: flvjs is not supported.");
                else this.notice("Error: Can't find flvjs.");
                break;
              case "dash":
                if (window.dashjs) {
                  var i = window.dashjs.MediaPlayer().create().initialize(n, n.src, !1),
                    l = this.options.pluginOptions.dash;
                  i.updateSettings(l), this.plugins.dash = i, this.events.on("destroy", (function() {
                    window.dashjs.MediaPlayer().reset(), delete t.plugins.dash
                  }))
                } else this.notice("Error: Can't find dashjs.");
                break;
              case "webtorrent":
                if (window.WebTorrent)
                  if (window.WebTorrent.WEBRTC_SUPPORT) {
                    this.container.classList.add("dplayer-loading");
                    var s = this.options.pluginOptions.webtorrent,
                      p = new window.WebTorrent(s);
                    this.plugins.webtorrent = p;
                    var d = n.src;
                    n.src = "", n.preload = "metadata", n.addEventListener("durationchange", (
                      function() {
                        return t.container.classList.remove("dplayer-loading")
                      }), {
                      once: !0
                    }), p.add(d, (function(n) {
                      n.files.find((function(n) {
                        return n.name.endsWith(".mp4")
                      })).renderTo(t.video, {
                        autoplay: t.options.autoplay,
                        controls: !1
                      })
                    })), this.events.on("destroy", (function() {
                      p.remove(d), p.destroy(), delete t.plugins.webtorrent
                    }))
                  } else this.notice("Error: Webtorrent is not supported.");
                else this.notice("Error: Can't find Webtorrent.")
            }
          }
        }, {
          key: "initVideo",
          value: function(n, e) {
            var t = this;
            this.initMSE(n, e), this.on("durationchange", (function() {
              1 !== n.duration && n.duration !== 1 / 0 && (t.template.dtime.innerHTML = q
                .secondToTime(n.duration))
            })), this.on("progress", (function() {
              var e = n.buffered.length ? n.buffered.end(n.buffered.length - 1) / n.duration : 0;
              t.bar.set("loaded", e, "width")
            })), this.on("error", (function() {
              t.video.error && t.tran && t.notice && "webtorrent" !== t.type && t.notice(t.tran(
                "video-failed"))
            })), this.on("ended", (function() {
              t.bar.set("played", 1, "width"), t.setting.loop ? (t.seek(0), t.play()) : t.pause(),
                t.danmaku && (t.danmaku.danIndex = 0)
            })), this.on("play", (function() {
              t.paused && t.play(!0)
            })), this.on("pause", (function() {
              t.paused || t.pause(!0)
            })), this.on("timeupdate", (function() {
              t.moveBar || t.bar.set("played", t.video.currentTime / t.video.duration, "width");
              var n = q.secondToTime(t.video.currentTime);
              t.template.ptime.innerHTML !== n && (t.template.ptime.innerHTML = n)
            }));
            for (var a = function(e) {
                n.addEventListener(t.events.videoEvents[e], (function(n) {
                  t.events.trigger(t.events.videoEvents[e], n)
                }))
              }, o = 0; o < this.events.videoEvents.length; o++) a(o);
            this.volume(this.user.get("volume"), !0, !0), this.options.subtitle && (this.subtitle =
              new Nt(this.template.subtitle, this.video, this.options.subtitle, this.events), Array
              .isArray(this.options.subtitle.url) && (this.subtitles = new _t(this)), this.user.get(
                "subtitle") || this.subtitle.hide())
          }
        }, {
          key: "switchQuality",
          value: function(n) {
            var e = this;
            if (n = "string" == typeof n ? parseInt(n) : n, this.qualityIndex !== n && !this
              .switchingQuality) {
              this.prevIndex = this.qualityIndex, this.qualityIndex = n, this.switchingQuality = !0,
                this.quality = this.options.video.quality[n], this.template.qualityButton.innerHTML =
                this.quality.name;
              var t = this.video.paused;
              this.video.pause();
              var a = Ra()({
                  current: !1,
                  pic: null,
                  screenshot: this.options.screenshot,
                  preload: "auto",
                  url: this.quality.url,
                  subtitle: this.options.subtitle
                }),
                o = (new DOMParser).parseFromString(a, "text/html").body.firstChild;
              this.template.videoWrap.insertBefore(o, this.template.videoWrap.getElementsByTagName(
                  "div")[0]), this.prevVideo = this.video, this.video = o, this.initVideo(this.video,
                  this.quality.type || this.options.video.type), this.seek(this.prevVideo.currentTime),
                this.notice("".concat(this.tran("switching-quality").replace("%q", this.quality.name)),
                  -1, void 0, "switch-quality"), this.events.trigger("quality_start", this.quality),
                this.on("canplay", (function() {
                  if (e.prevVideo) {
                    if (e.video.currentTime !== e.prevVideo.currentTime) return void e.seek(e
                      .prevVideo.currentTime);
                    e.template.videoWrap.removeChild(e.prevVideo), e.video.classList.add(
                      "dplayer-video-current"), t || e.video.play(), e.prevVideo = null, e.notice(
                      "".concat(e.tran("switched-quality").replace("%q", e.quality.name)), void 0,
                      void 0, "switch-quality"), e.switchingQuality = !1, e.events.trigger(
                      "quality_end")
                  }
                })), this.on("error", (function() {
                  e.video.error && e.prevVideo && (e.template.videoWrap.removeChild(e.video), e
                    .video = e.prevVideo, t || e.video.play(), e.qualityIndex = e.prevIndex, e
                    .quality = e.options.video.quality[e.qualityIndex], e.noticeTime = setTimeout(
                      (function() {
                        e.template.notice.style.opacity = 0, e.events.trigger("notice_hide")
                      }), 3e3), e.prevVideo = null, e.switchingQuality = !1)
                }))
            }
          }
        }, {
          key: "notice",
          value: function(n) {
            var e, t, a, o = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 2e3,
              r = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : .8,
              i = arguments.length > 3 ? arguments[3] : void 0;
            if (i && ((e = document.getElementById("dplayer-notice-".concat(i))) && (e.innerHTML = n),
                this.noticeList[i] && (clearTimeout(this.noticeList[i]), this.noticeList[i] = null)), !
              e) {
              var l = Ct.NewNotice(n, r, i);
              this.template.noticeList.appendChild(l), e = l
            }
            this.events.trigger("notice_show", e), o > 0 && (this.noticeList[i] = setTimeout((t = e, a =
              this,
              function() {
                t.addEventListener("animationend", (function() {
                    a.template.noticeList.removeChild(t)
                  })), t.classList.add("remove-notice"), a.events.trigger("notice_hide"), a
                  .noticeList[i] = null
              }), o))
          }
        }, {
          key: "resize",
          value: function() {
            this.danmaku && this.danmaku.resize(), this.controller.thumbnails && this.controller
              .thumbnails.resize(160, this.video.videoHeight / this.video.videoWidth * 160, this
                .template.barWrap.offsetWidth), this.events.trigger("resize")
          }
        }, {
          key: "speed",
          value: function(n) {
            this.video.playbackRate = n
          }
        }, {
          key: "destroy",
          value: function() {
            Ja.splice(Ja.indexOf(this), 1), this.pause(), document.removeEventListener("click", this
                .docClickFun, !0), this.container.removeEventListener("click", this.containerClickFun, !
                0), this.fullScreen.destroy(), this.hotkey.destroy(), this.contextmenu.destroy(), this
              .controller.destroy(), this.timer.destroy(), this.video.src = "", this.container
              .innerHTML = "", this.events.trigger("destroy")
          }
        }], a = [{
          key: "version",
          get: function() {
            return "1.27.1"
          }
        }], t && Wa(e.prototype, t), a && Wa(e, a), Object.defineProperty(e, "prototype", {
          writable: !1
        }), n
      }();
    const Qa = Ha;
    const _a = Qa
  })(), a.default
})()));
//# sourceMappingURL=DPlayer.min.js.map