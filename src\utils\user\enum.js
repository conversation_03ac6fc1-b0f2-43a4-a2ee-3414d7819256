//学科排序
export const subSort = ['数学', '物理', '化学', '生物', '地理', '语文', '英语', '地理', '历史', '道法', '政治', '科学']

// 文科
export const wenke = [11, 12, 14, 24, 25, 26, 27, 28, 29, 35, 36, 37, 38, 39]

export const subjectImg = {
  数学: '/1.0/vip/mathematics.png',
  物理: '/1.0/vip/physics.png',
  化学: '/1.0/vip/chemistry.png',
  生物: '/1.0/vip/organism.png',
  语文: '/1.0/vip/chinese.png',
  英语: '/1.0/vip/english.png',
  地理: '/1.0/vip/geography.png',
  历史: '/1.0/vip/history.png',
  道法: '/1.0/vip/politics.png',
  政治: '/1.0/vip/politics.png',
  科学: '/1.0/vip/kexue.png'
}
//初中
export const subjectEnum = [
  {
    key: 'math',
    text: '数学',
    url: '/1.0/vip/mathematics.png'
  },
  {
    key: 'physics',
    text: '物理',
    url: '/1.0/vip/physics.png'
  },
  {
    key: 'chemistry',
    text: '化学',
    url: '/1.0/vip/chemistry.png'
  },
  {
    key: 'bio',
    text: '生物',
    url: '/1.0/vip/organism.png'
  },
  {
    key: 'chinese',
    text: '语文',
    url: '/1.0/vip/chinese.png'
  },
  {
    key: 'english',
    text: '英语',
    url: '/1.0/vip/english.png'
  },
  {
    key: 'geography',
    text: '地理',
    url: '/1.0/vip/geography.png'
  },
  {
    key: 'history',
    text: '历史',
    url: '/1.0/vip/history.png'
  },
  {
    key: 'politics',
    text: '道法', // 高中叫政治
    url: '/1.0/vip/politics.png'
  }
]
//小学
export const subjectEnum2 = [
  {
    key: 'math3',
    text: '数学',
    url: '/1.0/vip/mathematics.png'
  },
  {
    key: 'chinese3',
    text: '语文',
    url: '/1.0/vip/chinese.png'
  },
  {
    key: 'english3',
    text: '英语',
    url: '/1.0/vip/english.png'
  },
  {
    key: 'science3',
    text: '科学',
    url: '/1.0/vip/kexue.png'
  }
]
//初中
export const subjectEnum3 = [
  {
    key: 'math',
    text: '数学'
  },
  {
    key: 'physics',
    text: '物理'
  },
  {
    key: 'chemistry',
    text: '化学'
  },
  {
    key: 'chinese',
    text: '语文'
  },
  {
    key: 'english',
    text: '英语'
  },
  {
    key: 'bio',
    text: '生物'
  },
  {
    key: 'history',
    text: '历史'
  },
  {
    key: 'politics',
    text: '道法'
  },
  {
    key: 'geography',
    text: '地理'
  }
]

//1-5年级
export const subjectEnum4 = [
  {
    key: 10,
    text: '数学'
  },
  {
    key: 11,
    text: '语文'
  },
  {
    key: 12,
    text: '英语'
  },
  {
    key: 14,
    text: '科学'
  }
]

//6年级五四制
export const subjectEnum54 = [
  {
    key: 10,
    text: '数学'
  },
  {
    key: 11,
    text: '语文'
  },
  {
    key: 12,
    text: '英语'
  },
  {
    key: 14,
    text: '科学'
  },
  {
    key: 23,
    text: '生物'
  },
  {
    key: 28,
    text: '道法'
  },
  {
    key: 29,
    text: '历史'
  },
  {
    key: 25,
    text: '地理'
  }
]
//初中
export const subjectEnum5 = [
  {
    key: 20,
    text: '数学'
  },
  {
    key: 26,
    text: '语文'
  },
  {
    key: 27,
    text: '英语'
  },
  {
    key: 21,
    text: '物理'
  },
  {
    key: 22,
    text: '化学'
  },
  {
    key: 23,
    text: '生物'
  },
  {
    key: 29,
    text: '历史'
  },
  {
    key: 28,
    text: '道法'
  },
  {
    key: 25,
    text: '地理'
  }
]
//高中
export const subjectEnum6 = [
  {
    key: 30,
    text: '数学'
  },
  {
    key: 36,
    text: '语文'
  },
  {
    key: 37,
    text: '英语'
  },
  {
    key: 31,
    text: '物理'
  },
  {
    key: 32,
    text: '化学'
  },
  {
    key: 33,
    text: '生物'
  },
  {
    key: 39,
    text: '历史'
  },
  {
    key: 38,
    text: '政治'
  },
  {
    key: 35,
    text: '地理'
  }
]

export const subjectList = {
  math3: {
    key: 10,
    text: '数学'
  },
  chinese3: {
    key: 11,
    text: '语文'
  },
  english3: {
    key: 12,
    text: '英语'
  },
  science3: {
    key: 14,
    text: '科学'
  },
  math: {
    key: 20,
    text: '数学'
  },
  physics: {
    key: 21,
    text: '物理'
  },
  chemistry: {
    key: 22,
    text: '化学'
  },
  bio: {
    key: 23,
    text: '生物'
  },
  chinese: {
    key: 26,
    text: '语文'
  },
  english: {
    key: 27,
    text: '英语'
  },
  geography: {
    key: 25,
    text: '地理'
  },
  history: {
    key: 29,
    text: '历史'
  },
  politics: {
    key: 28,
    text: '道法'
  },
  science: {
    key: 24,
    text: '科学'
  },
  math2: {
    key: 30,
    text: '数学'
  },
  physics2: {
    key: 31,
    text: '物理'
  },
  chemistry2: {
    key: 32,
    text: '化学'
  },
  bio2: {
    key: 33,
    text: '生物'
  },
  chinese2: {
    key: 36,
    text: '语文'
  },
  english2: {
    key: 37,
    text: '英语'
  },
  geography2: {
    key: 35,
    text: '地理'
  },
  history2: {
    key: 39,
    text: '历史'
  },
  politics2: {
    key: 38,
    text: '政治'
  }
}

export const statusEnum = [
  {
    key: 0,
    text: '全部'
  },
  {
    key: 4,
    text: '已导出'
  },
  {
    key: 3,
    text: '已收藏'
  },
  {
    key: 5,
    text: '已做题'
  }
  // {
  //   key: 1,
  //   text: '已完成'
  // }
  // {
  //   key: 2,
  //   text: '已完成'
  // }
] //  状态枚举

export const midEnum = [
  {
    key: 3,
    text: '期中试卷'
  },
  {
    key: 4,
    text: '期末试卷'
  }
]

export const topicEnum = [
  {
    key: 5,
    text: '小升初真题'
  },
  {
    key: 6,
    text: '小升初模拟'
  }
]

export const topicEnum2 = [
  {
    key: 5,
    text: '中考真题'
  },
  {
    key: 6,
    text: '中考模拟'
  }
]

export const topicEnum3 = [
  {
    key: 5,
    text: '高考真题'
  },
  {
    key: 6,
    text: '高考模拟'
  }
]

export const gradeList = [
  {
    name: '一年级',
    value: 1
  },
  {
    name: '二年级',
    value: 2
  },
  {
    name: '三年级',
    value: 3
  },
  {
    name: '四年级',
    value: 4
  },
  {
    name: '五年级',
    value: 5
  },
  {
    name: '六年级',
    value: 6
  },
  {
    name: '七年级',
    value: 7
  },
  {
    name: '八年级',
    value: 8
  },
  {
    name: '九年级',
    value: 9
  },
  {
    name: '高一',
    value: 10
  },
  {
    name: '高二',
    value: 11
  },
  {
    name: '高三',
    value: 12
  }
]

export const gradeNameList = [
  '',
  '一年级',
  '二年级',
  '三年级',
  '四年级',
  '五年级',
  '六年级',
  '七年级',
  '八年级',
  '九年级',
  '高一',
  '高二',
  '高三'
]

export const gradeIdArr = {
  1: '一年级',
  2: '二年级',
  3: '三年级',
  4: '四年级',
  5: '五年级',
  6: '六年级',
  7: '初一',
  8: '初二',
  9: '初三',
  10: '高一',
  11: '高二',
  12: '高三'
}

export const deviceType = {
  100: '沃瞳学平板',
  101: '考试大师',
  102: '学数季',
  103: '爱得森',
  104: `小优小程序`,
  105: '趣印',
  106: '电话手表',
  107: '小霸王',
  108: '飞硕',
  801: `小优平板`
}

export const degreeArr = ['', '易', '较易', '中档', '较难', '难']

export const sourceType = {
  0: '',
  1: '随堂练习',
  2: '课后作业',
  3: '单元测试',
  4: '月考试题',
  5: '期中考试',
  6: '期末考试',
  7: '真题考试',
  8: '模拟考试',
  9: '精准练习',
  20: '错题专练',
  101: 'AI精准学',
  102: 'AI精准学',
  103: 'AI精准学',
  104: '测试卷',
  105: '在线作业',
  106: '真题试卷',
  107: '拍照上传',
  108: '相似题',
  109: '个性化训练',
  116: '入门测',
  1024: '其他'
}
