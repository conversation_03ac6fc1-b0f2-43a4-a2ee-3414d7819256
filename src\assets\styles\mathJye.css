/*数学公式*/
.edittable,
.edittable2,
.edittable3,
.edittable4 {
  border-collapse: collapse;
  margin: 2px;
  box-sizing: border-box;
}

.edittable .MathJye_th,
.edittable .MathJye_td,
.edittable2 .MathJye_th,
.edittable2 .MathJye_td,
.edittable3 .MathJye_th,
.edittable3 .MathJye_td,
.edittable4 .MathJye_th,
.edittable4 .MathJye_td {
  line-height: 30px;
  padding: 5px;
  white-space: normal;
  word-break: break-all;
  border: 1px solid #000;
  vertical-align: middle;
}

.edittable {
  text-align: center;
}

/*居中对齐*/
.edittable2 {
  text-align: center;
}

/*左对齐*/
.edittable3 {
  text-align: left;
}

/*第一行居中,其余左对齐*/
.edittable3 .MathJye_tr:first-child .MathJye_td {
  text-align: center;
}

.edittable4 {
  border: 1px solid #000;
}

/*只有外边框，无内边框*/
.edittable4 .<PERSON><PERSON>ye_td {
  border: none;
}

.composition {
  border-collapse: collapse;
  text-align: left;
  margin: 2px;
  width: 98%;
}

.composition .MathJye_th,
.composition .MathJye_td {
  line-height: 30px;
  white-space: normal;
  word-break: break-all;
  border-width: 0px;
  vertical-align: middle;
}

.composition2 {
  border-collapse: collapse;
  width: 100%;
}

.composition2 .MathJye_th,
.composition2 .MathJye_td {
  text-align: left;
  line-height: 30px;
  word-break: break-all;
  border: none;
  border-width: 0px;
  vertical-align: middle;
  white-space: nowrap;
}

/*第一行居中,其余左对齐*/
.composition2 .MathJye_tr:first-child .MathJye_td {
  width: auto;
}

.composition2 .MathJye_td {
  float: left;
  margin-right: 20px;
}

.MathJye {
  border: 0 none;
  direction: ltr;
  line-height: normal;
  display: inline-block;
  float: none;
  font-family: 'JyeMath', 'JyeMathLetters', 'Times New Roman', '宋体';
  font-size: 15px;
  font-style: normal;
  font-weight: normal;
  letter-spacing: 1px;
  margin: 0;
  padding: 0;
  text-align: left;
  text-indent: 0;
  text-transform: none;
  white-space: nowrap;
  word-spacing: normal;
  word-wrap: normal;
  -webkit-text-size-adjust: none;
}

.MathJye .MathJye_div,
.MathJye .MathJye_span {
  border: 0 none;
  margin: 0;
  padding: 0;
  line-height: normal;
  text-align: left;
  height: auto;
  /* _height: auto; */
  white-space: normal;
}

.MathJye .MathJye_table {
  border-collapse: collapse;
  margin: 0;
  padding: 0;
  text-align: center;
  vertical-align: middle;
  line-height: normal;
  font-size: inherit;
  font-style: normal;
  font-weight: normal;
  border: 0;
  float: none;
  display: inline-block;
  zoom: 0;
}

.MathJye .MathJye_table .MathJye_td {
  padding: 0;
  font-size: inherit;
  line-height: normal;
  white-space: nowrap;
  border: 0 none;
  width: auto;
  /* _height: auto; */
}

.MathJye_mi {
  font-style: italic;
}

.flipv {
  -ms-transform: scaleX(-1);
  -moz-transform: scaleX(-1);
  -webkit-transform: scaleX(-1);
  -o-transform: scaleX(-1);
  transform: scaleX(-1);
  filter: FlipH;
}

.fliph {
  -ms-transform: scaleY(-1);
  -moz-transform: scaleY(-1);
  -webkit-transform: scaleY(-1);
  -o-transform: scaleY(-1);
  transform: scaleY(-1);
  filter: FlipV;
}

.mathjye-bold {
  font-weight: 800;
}

.mathjye-del {
  text-decoration: line-through;
}

.mathjye-underline {
  padding-bottom: 2px;
  display: inline-block;
  padding: 3px 10px 3px 10px;
  margin: 0 3px;
  /* min-width: 1em; */
  min-height: 16px;
  line-height: 18px;
  height: auto;
  border-bottom: 1px solid #000;
  text-decoration: none;
}

.mathjye-underpline {
  border-bottom: 2px dotted #000;
  padding-bottom: 3px;
}

.mathjye-underpoint {
  background: url(http://img.jyeoo.net/images/formula/point.png) no-repeat center bottom;
  padding-bottom: 4px;
}

.mathjye-underpoint2 {
  border-bottom: 2px dotted #000;
  padding-bottom: 3px;
}

.mathjye-underwave {
  background: url(http://img.jyeoo.net/images/formula/wave.png) bottom repeat-x;
  padding-bottom: 4px;
}

.mathjye-alignleft {
  display: block;
  text-align: left;
}

.mathjye-aligncenter {
  display: block;
  text-align: center;
}

.mathjye-alignright {
  display: block;
  text-align: right;
}

.QUES_LI>.MathJye_h2 {
  font-size: 30px;
  margin-top: 20px;
}

/*后加*/
.quesborder {
  font-size: 14px;
}

.MathJye_sub,
.MathJye_sup {
  font-family: 'JyeMath', 'JyeMathLetters', 'Times New Roman', '宋体';
}

.MathJye .int {
  font-size: 20px;
  margin-right: -2px;
  font-style: normal;
  display: block;
  line-height: 2;
  transform: scale(1, 1.3);
  -ms-transform: scale(1, 1.3);
  -webkit-transform: scale(1, 1.3);
  -moz-transform: scale(1, 1.3);
  -o-transform: scale(1, 1.3);
}

.MathJye_mi {
  font-style: normal;
}

.MathJye .int-sup {
  margin-bottom: 10px;
}

/*积分号嵌入span加类名*/
/*回归方程*/
.MathJye .hat {
  height: 3px;
  text-align: center;
}

/*圆弧帽子只有两个字符*/
.MathJye .arc {
  height: 10px;
  text-align: center;
  display: inline-block;
  position: absolute;
  top: -10px;
  left: 50%;
  margin-left: -9px;
  width: 18px;
}

/*向量箭头*/
.MathJye .arrow {
  height: 8px;
  width: 100%;
  overflow: hidden;
  text-align: right;
  position: absolute;
  top: 3px;
}

.MathJye .arrow>.MathJye_span {
  position: absolute;
  font-size: 14px;
  text-align: right;
  height: 8px;
  line-height: 8px;
  right: 0;
  margin-right: -1px;
  list-style: none;
  top: -1px;
}

.MathJye .arrow>.MathJye_span>.MathJye_i {
  position: absolute;
  width: 13px;
  height: 3px;
  background-color: #fff;
  top: 3px;
  z-index: 99;
  left: 0;
  border-top: 1px solid #000;
  left: -1px;
}

.MathJye .arrow>.MathJye_i {
  height: 1.2px;
  width: 45px;
  position: absolute;
  right: 2px;
  top: 3px;
  font-size: 14px;
  line-height: 8px;
  font-style: normal;
  background-color: #000;
}

.selectoption .MathJye_label.s .MathJye_span>.MathJye_i,
.MathJye_div.s .MathJye_span>.MathJye_i {
  background-color: #deeeff;
}

.MathJye .matrixtop,
.MathJye .matrixbtm {
  height: 11px;
}

.MathJye .matrixmid {
  height: 7px;
}

/*高度height动态计算*/
/*大花括号*/
.MathJye .brace1 {
  height: 12px;
}

.MathJye .brace2 {
  height: 5px;
}

/*向量*/
/*根号数字*/
/*根号*/
.MathJye .sqrt {
  transform-origin: center top;
}

.MathJye .sqrt-num {
  position: absolute;
  top: -10px;
  left: 2px;
}

/*空白行*/
.pt6 .MathJye_span,
.ac .MathJye_td .fleft {
  white-space: normal;
}

/*IE hack*/
/*CSS-Hack for Internet Explorer 10+（IE10、IE11、Edge）*/
/*_:-ms-lang(x),
.ques-list li { list-style-type: circle; }*/
.MathJye_img {
  max-width: 100%;
  mix-blend-mode: multiply;
  /* zoom:1.1; */
}

/*填空题*/
.quizPutTag {
  display: inline-block;
  padding: 3px 10px 1px 10px;
  margin: 0 3px;
  font-size: 14px;
  min-width: 1em;
  min-height: 16px;
  line-height: 18px;
  height: auto;
  border-bottom: 1px solid #03f;
  text-decoration: none;
  zoom: 1;
  color: #127176;
  word-break: break-all;
}

.quizPutTag:hover {
  color: #f60;
}

.quizPutTag .MathJye_img {
  cursor: pointer;
  width: 200px;
  margin-left: 10px;
}

.sanwser {
  padding: 4px 10px;
  margin: 0px;
  border: 1px solid #adcd3c;
  background-color: #f2fddb;
  color: #000;
  display: none;
}

/*答案*/
.selectoption MathJye_label.s,
MathJye_div.s {
  border: 1px solid #91cbed;
  background-color: #deeeff;
  display: inline-block;
}

.selectoption MathJye_label.s.sh,
MathJye_div.s.sh {
  margin: 1px;
  border: none;
  background: none;
}

.mathJye_table_cont {
  display: inline-block;
  max-width: 100%;
  overflow-x: auto;
  box-sizing: border-box;
  vertical-align: middle;
}

/* 英文选项对齐 */
.opac {
  opacity: 0;
}

.xyopt2.composition2 .MathJye_td,
.xyopt1.composition2 .MathJye_td,
.xyopt5.composition2 .MathJye_td {
  margin-right: 0;
  white-space: nowrap;
  display: inline-block;
  box-sizing: border-box;
}

/* 根据字数用几行显示 */
/* 1行，A选项最多11个length */
.xyopt1.composition2 .MathJye_td {
  width: 25% !important;
}

/* 2行显示,第3行text-indent加序号即可 */
.xyopt2.composition2 .MathJye_td {
  width: 50% !important;
}

/* 5个选项2行 */
.xyopt5.composition2 .MathJye_td {
  width: 50% !important;
}

.xyopt5.composition2 .MathJye_td:nth-child(1),
.xyopt5.composition2 .MathJye_td:nth-child(2) {
  width: auto !important;
}

.xyopt5.composition2 .MathJye_td:nth-child(3) {
  float: right;
  width: 50% !important;
}

/* 图片单独一行显示 */
.MathJye_img[data-full='1'] {
  width: 100%;
  zoom:1;
}

.MathJye_img[data-full='2'] {
  width: 70%;
  margin: 0 30% 0 0;
  /* zoom:1.2; */
}

/* 小学数学兼容 */
.sub10 {
  width: 100%;
}

.sub10 .MathJye_td {
  float: left;
  width: 100% !important;
}

.mathJye_table_cont {
  width: 100%;
}

rich-text {
  font-size: 28rpx;
}
