<!-- 难题求助-拍搜结果 -->
<template>
  <div class="content">
    <div class="inner">
      <div class="wrap">
        <!-- 上传图片 -->
        <div class="st_box">
          <div class="st_pic">
            <!-- <el-image class="st_img" src="@/assets/img/note/help.png" :preview-src-list="['@/assets/img/note/help.png']"
              fit="cover" /> -->
            <!-- 上传后 -->
            <img class="st_img" :src="state.formPic" v-if="state.formPic" />
            <!-- 没上传 -->
            <div v-else>请选取单道试题图片，将图片拖拽或粘贴到这里</div>
          </div>
          <input type="file" id="file" accept="image/png, image/jpeg,image/jpg" class="file" @change="uploadFiles" title="">
          <div class="st_upload" onclick="document.querySelector('#file').click()">
            <div class="st_img">
              <img src="@/assets/img/note/upimg.svg" />
              点击上传图片 >
            </div>
            <div class="st_tip">（图片大小不超过5M）</div>
          </div>
          <div class="st_ok" style="opacity: .5;" v-if="state.formPic.indexOf('https://xiaoyin')==0">搜题</div>
          <div class="st_ok" @click="picSearch" v-loading.fullscreen.lock="state.loading" v-else>搜题</div>
        </div>
        <div class="result">
          <div class="res_tit">
            <img src="@/assets/img/note/result.svg" />搜题结果
          </div>
          <div class="res_ul" v-if="state.data.length">
            <div class="res_li" v-for="(item,i) in state.data" :key="i">
              <div class="res_circle"></div>
              <div class="res_stem" v-html="ReplaceMathString(item.content)" :data-i="i"></div>
              <div class="opt_ul" v-if="item.cate === 1 || item.cate===3" :data-i="i">
                <div class="opt_li" v-for="(item2,index2) in item.options">
                  <div class="opt_num">{{state.subArr[index2]}}.</div>
                  <div class="opt_txt" v-html="ReplaceMathString(item2)"></div>
                </div>
              </div>
              <!-- on显示答案 -->
              <div class="answer" :class="item.answer?'on':''">
                <div class="switch_box" @click="togAnswer(i)">
                  <div class="switch">
                    <div class="circle"></div>
                  </div>
                  <div class="ans_txt">显示答案与解析</div>
                </div>
                <div class="ans_box">
                  <div class="res_item res_item2">
                    <div class="res_h1">【知识点】</div>
                    <div class="res_p">
                      <div class="res_pt">{{item.pointName}}</div>
                    </div>
                  </div>
                  <div class="res_item">
                    <div class="res_h1">【答案】</div>
                    <div class="res_p" v-html="ReplaceMathString(item.displayAnswer)"></div>
                  </div>
                  <div class="res_item" v-if="item.analyse">
                    <div class="res_h1">【分析】</div>
                    <div class="res_p" v-html="ReplaceMathString(item.analyse)"></div>
                  </div>
                  <div class="res_item" v-if="item.method">
                    <div class="res_h1">【解答】</div>
                    <div class="res_p" v-html="ReplaceMathString(item.method)"></div>
                  </div>
                  <div class="res_item" v-if="item.discuss">
                    <div class="res_h1">【点评】</div>
                    <div class="res_p" v-html="ReplaceMathString(item.discuss)"></div>
                  </div>
                </div>
              </div>
              <div class="res_btn active" v-if="item.isAdd">+ 已加入错题本</div>
              <div class="res_btn" @click="addNote(i)" v-else>+ 加入错题本</div>
            </div>
          </div>
          <!-- 无数据 -->
          <div class="nodata" style="min-height: 30.625rem;" v-if="!state.data.length">
            <img src="@/assets/img/note/nodata.png" />你拍的信息太少啦，没有匹配到题目！
          </div>

        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { reactive, onMounted, watch } from 'vue';
  import router from '@/router/index'
  import { useUserStore } from "@/store/modules/user"
  import { ElMessage } from "element-plus"
  import { sourceType } from '@/utils/user/enum'
  import { getObjectURL } from '@/utils/download'
  import { getDay, getImageSize, formatDate2, ReplaceMathString } from '@/utils/user/util'
  import { NameOfSubject, KeyValueOfSubject } from '@/utils/user/commonData'
  import { addNoteApi, picSearchApi, updateNoteApi } from "@/api/note"
  import { uploadApi } from "@/api/user"
  import { useRoute } from "vue-router"

  defineOptions({
    name: "NoteHelp"
  })

  const route = useRoute()
  const state : any = reactive({
    loading: false,
    data: [],
    formdata: {},
    formPic: '',
    subArr: ['A', 'B', 'C', 'D', 'E', 'F', 'G'],
    // 掌握程度
    masteryArr: [
      {
        title: '未掌握',
        active: false,
        number: 1
      },
      {
        title: '已掌握',
        active: false,
        number: 2
      },
      {
        title: '熟练',
        active: false,
        number: 3
      }
    ],
    mastery: 0,
    // 重要程度
    importanceArr: [
      {
        title: '一般',
        active: false,
        number: 1
      },
      {
        title: '重要',
        active: false,
        number: 2
      },
      {
        title: '非常重要',
        active: false,
        number: 3
      }
    ],
    importance: 0,
    // 错误原因
    reasonArr: [
      {
        title: '概念模糊',
        active: false,
        number: 1
      },
      {
        title: '思路错误',
        active: false,
        number: 2
      },
      {
        title: '运算错误',
        active: false,
        number: 4
      },
      {
        title: '粗心大意',
        active: false,
        number: 16
      },
      {
        title: '其他',
        active: false,
        number: 1024
      }
    ],
    reason: 0,
    // 错误来源
    sourceArr: [
      {
        title: '随堂练习',
        active: false,
        number: 1
      },
      {
        title: '课后作业',
        active: false,
        number: 2
      },
      {
        title: '单元测试',
        active: false,
        number: 3
      },
      {
        title: '月考试题',
        active: false,
        number: 4
      },
      {
        title: '期中考试',
        active: false,
        number: 5
      },
      {
        title: '期末考试',
        active: false,
        number: 6
      },
      {
        title: '真题考试',
        active: false,
        number: 7
      },
      {
        title: '模拟考试',
        active: false,
        number: 8
      },
      {
        title: '精准练习',
        active: false,
        number: 9
      },
      {
        title: '错题专练',
        active: false,
        number: 20
      },
      {
        title: '在线作业',
        active: false,
        number: 105
      },
      {
        title: '其他',
        active: false,
        number: 1024
      }
    ],
    source: 0,

    //     无标签
    labelArr: [
      {
        title: '无',
        active: false,
        number: 0
      }
    ],
    label: 0,
    labelNum: 0,
    active: false,
    noteId: ''
  })

  onMounted(() => {
    init()
  })

  //监听路由参数
  watch(
    () => route.query,
    () => {
      if (localStorage.isLoad == '1' && route.name == "NoteHelp") {
        init()
      }
    }
  )

  // 属性赋值
  const setData = (obj : any) => {
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        state[key] = obj[key]
      }
    }
  }

  const init = () => {
    localStorage.removeItem('isLoad')
    let list : any = localStorage.searchPicData
    if (list) {
      setData({
        formPic: localStorage.searchPic || '',
        data: JSON.parse(localStorage.searchPicData)
      })
    }
  }
  // 加入错题本
  const addNote = (i : any) => {
    let item = state.data[i]
    const { mastery, importance, reason, source, quesId, noteId } = item
    if (!noteId) {
      const param = {
        mastery, //掌握程度。全部：0、未掌握：1、已掌握：2、熟练：3
        importance, //重要程度。全部：0、一般：1、重要：2、非常重要：3
        reason, //错题原因。全部：0、概念模糊：1、思路错误：2、运算错误：4、审题错误：8、粗心大意：16、其他：1024
        source: 107, //错题来源 拍照上传
        ques: [
          {
            quesId,
            mark: true
            // original: true
          }
        ], //原题：1.原题；0.同类题
        imgUrl: state.formPic
      }
      addNoteApi(param)
        .then(() => {
          state.data[i].isAdd = true
          ElMessage.success("已加入错题本")
        })
    } else {
      const param = {
        noteId,
        mastery,
        importance,
        reason,
        source
      }
      updateNoteApi(param)
        .then(() => {
          state.data[i].isAdd = true
          ElMessage.success("已加入错题本")
        })
    }
    localStorage.isLoad = 1
  }

  //显示答案
  const togAnswer = (i : any) => {
    const list = state.data
    let answer = list[i].answer ? false : true
    state.data[i].answer = answer
  }

  //关闭搜题弹窗
  const helpHide = () => {
    let fileInput : any = document.getElementById('file');
    fileInput.value = null;
    state.formdata = {}
  }
  // 搜题
  const picSearch = () => {
    let img = state.formPic
    if (img.indexOf('https://xiaoyin') == 0) {
      ElMessage.error('请上传新的图片')
      return
    }
    state.loading = true
    picSearchApi(state.formdata)
      .then((res : any) => {
        state.loading = false
        //缓存搜题结果和图片
        const data = res.data || []
        state.data = data
        if (data.length) {
          //上传图片
          uploadApi(state.formdata)
            .then((res2 : any) => {
              const data2 = res2.data
              const url = data2.url + data2.key
              localStorage.searchPicData = JSON.stringify(data)
              localStorage.searchPic = url
              helpHide()
            })
        }
      }).catch(() => {
        state.loading = false
      })
  }
  //上传图片
  async function uploadFiles(e : any) {
    let file = e.target.files[0]
    if (changeImg(file)) {
      //修改文件名
      const type = file.type.split('/')
      const file2 = new File([file], 'web_user_' + new Date().getTime() + Math.floor(Math.random() * 10001) + '.' + type[1], { type: file.type });
      const formdata : any = new FormData()
      formdata.append("file", file2)
      state.formdata = formdata
      state.formPic = getObjectURL(file)
    }
  }
  //限制图片大小和格式
  const changeImg = (file : any) => {
    console.log(file)
    const size = 1024 * 10
    const limit = (file?.size || 0) / 1024
    const isLtM = limit < size
    let msg = "上传图片大小不能超过" + size + "KB"
    const type = file.type
    const isType = type == "image/png" || type == "image/jpg" || type == "image/jpeg"
    if (!isType) {
      msg = "上传图片只支持png,jpg,jpeg格式"
    }
    if (!isLtM || !isType) {
      //解决重复图片不能上传
      let fileInput : any = document.getElementById('file');
      fileInput.value = null;
      ElMessage.error(msg)
      return false
    } else {
      return true
    }
  }
</script>

<style lang="scss" scoped>
  @import url('@/assets/styles/reset.css');

  .none {
    display: none !important
  }

  .nowrap {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .content {
    width: 100%;
    background: #F5F5F5;
    display: flex;
    flex: 1;
    flex-flow: column;
    overflow: auto;
    // box-sizing: border-box;
    // padding: 0 19.375rem;
  }

  .inner {
    margin: 0 auto;
    width: 81.25rem;
  }

  .wrap {
    flex: 1;
    float: left;
    width: 100%;
  }

  /* 搜题 */
  .st_box {
    width: 100%;
    height: 17.875rem;
    border: .0625rem solid #eaeaea;
    background: #ffffff;
    box-sizing: border-box;
    padding: 1.25rem 1.25rem .75rem;
  }

  .st_pic {
    width: 100%;
    height: 12.75rem;
    border-radius: .625rem;
    border: .0625rem dashed #00c9a3;
    background: #f3fffd;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1.25rem;
    color: #999999;
    font-size: .875rem;
  }

  /* .st_pic:hover {
   cursor: pointer;
 } */

  .st_img,
  .st_img img {
    width: auto !important;
    height: auto !important;
    max-width: 100%;
    max-height: 10.125rem;
  }

  .st_upload {
    float: left;
    width: 31.25rem;
    margin: .625rem 0 0rem;
  }

  .st_img {
    float: left;
    color: #009c7f;
    font-size: .875rem;
    line-height: 1rem;
    display: flex;
    align-items: center;
  }

  .st_img:hover {
    cursor: pointer;
  }

  .st_img img {
    width: 1rem;
    height: 1rem;
    margin: 0 .375rem 0 0;
  }

  .st_tip {
    float: left;
    line-height: 1rem;
    color: #999999;
    font-size: .875rem;
  }

  .file {
    float: left;
    width: 100%;
    height: 12.75rem;
    margin: -12.75rem 0 0;
    position: relative;
    z-index: 2;
    opacity: 0;
    cursor: pointer;
  }

  .st_ok {
    float: right;
    width: 7.625rem;
    height: 2.375rem;
    line-height: 2.375rem;
    font-size: 1rem;
    border-radius: 1.1875rem;
    text-align: center;
    color: #fff;
    margin: .625rem 0 0;
    background: linear-gradient(150.8deg, #36e2c2 0%, #00b7d0 100%);
  }

  .st_ok:hover {
    cursor: pointer;
  }

  /* 结果 */
  .result {
    float: left;
    width: 100%;
    display: flex;
    flex-flow: column;
    // min-height: calc(100vh - 20.9375rem - 4.375rem);
    // height: 18.75rem;
  }

  .res_tit {
    float: left;
    width: 100%;
    line-height: 1.3125rem;
    color: #2a2b2a;
    font-size: 1rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    margin: 1.25rem 0 .625rem;
  }

  .res_tit img {
    width: 1.125rem;
    height: 1.125rem;
    margin: 0 .5rem 0 0;
  }

  /* 列表 */
  .res_ul {
    float: left;
    width: 100%;
  }

  .res_li {
    float: left;
    width: 100%;
    background: #fff;
    box-sizing: border-box;
    padding: 1.25rem 0;
    margin: 0 0 .625rem;
  }

  .res_circle {
    float: left;
    width: .875rem;
    height: 1rem;
    border-radius: 0 .375rem .375rem 0;
    background: #5a85ec;
  }

  .res_stem {
    float: left;
    width: calc(100% - 1.875rem);
    margin: 0 0 0 1rem;
    color: #2a2b2a;
    font-size: .875rem;
    line-height: 1.5;
    box-sizing: border-box;
    padding: 0 1.875rem 0 0;
  }

  .opt_ul {
    float: left;
    width: 100%;
    box-sizing: border-box;
    padding: 0 1.875rem;
    display: flex;
    align-items: center;
    margin: 1.25rem 0 -0.625rem;
  }

  .opt_li {
    float: left;
    margin: 0 3.125rem 1.875rem 0;
    display: flex;
    align-items: center;
  }

  .opt_num {
    float: left;
    color: #2a2b2a;
    font-size: .875rem;
    font-weight: 700;
    line-height: 1.5;
    margin: 0 .3125rem 0 0;
  }

  .opt_txt {
    float: left;
    color: #2a2b2a;
    font-size: .875rem;
    line-height: 1.5;
  }

  .answer {
    float: left;
    width: 100%;
    background: #fef8e9;
    box-sizing: border-box;
    padding: 0 1.8125rem;
    border-top: .0625rem dashed #F5F5F5;
    margin: .625rem 0 0;
  }

  /* 开关 */
  .switch_box {
    float: left;
    margin: .625rem 0;
  }

  .switch_box:hover {
    cursor: pointer;
  }

  .switch {
    float: left;
    margin: 0 0 0 auto;
    width: 1.875rem;
    height: .9375rem;
    border-radius: .8438rem;
    background: #999;
    transition: background 0.1s, border 0.1s;
  }

  .circle {
    width: .6875rem;
    height: .6875rem;
    background: #fff;
    box-shadow: 0 .0625rem .1875rem rgba(0, 0, 0, 0.4);
    border-radius: 50%;
    transform: translateX(0);
    transition: transform 0.15s linear;
    position: relative;
    top: .125rem;
    left: .125rem;
  }

  .on .switch {
    border-color: #5a85ec;
    background: #5a85ec;
  }

  .on .switch .circle {
    transform: translateX(.9375rem);
  }

  .ans_txt {
    float: left;
    line-height: .9375rem;
    color: #666666;
    font-size: .75rem;
    margin: 0 0 0 .5rem;
  }

  /* 答案 */
  .ans_box {
    float: left;
    width: 100%;
    mix-blend-mode: multiply;
  }

  .res_item {
    display: none;
    float: left;
    width: 100%;
    box-sizing: border-box;
    margin: 0 0 .625rem;
  }

  .on .res_item {
    display: block;
  }

  .res_item:first-child {
    margin-top: .4375rem;
  }

  .res_item>div {
    float: left;
    color: #2a2b2a;
    font-size: .875rem;
    line-height: 1.625rem;
  }

  .res_h1 {
    width: 4.125rem;
    font-weight: 700;
  }

  .res_p {
    width: calc(100% - 4.125rem);
  }

  /* 知识点 */
  .res_item2 .res_h1 {
    width: 5rem;
  }

  .res_item2 .res_p {
    width: calc(100% - 5rem);
  }

  .res_item2 div {
    float: left;
  }

  .res_pt {
    margin: 0 .625rem 0 0;
  }

  /* 加入错题本 */
  .res_btn {
    float: right;
    width: 7.0625rem;
    line-height: 1.9375rem;
    border-radius: .25rem;
    background: #00c9a3;
    color: #ffffff;
    font-size: .875rem;
    text-align: center;
    margin: 1.25rem 1.25rem 0 0;
    border: .0625rem solid #00c9a3;
    box-sizing: border-box;
  }

  .res_btn.active {
    color: #999;
    border: .0625rem solid #eaeaea;
    background: #f5f5f5;
  }

  .res_btn:hover {
    cursor: pointer;
  }

  /* 暂无数据 */
  .nodata {
    flex: 1;
    color: #999999;
    font-size: .875rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-flow: column;
    background: #fff;
  }

  .nodata img {
    width: 7.4375rem;
    height: 8rem;
    margin: 0 0 .625rem;
  }

  /* 标签弹窗 */
  .tag_drawer .el-drawer {
    width: 35rem !important;
  }

  .tag_box {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-flow: column;
  }

  .tag_box div {
    float: left;
  }

  .tag_wrap {
    flex: 1;
    overflow-y: auto;
  }

  .tag_h1 {
    width: 100%;
    line-height: 1.3125rem;
    color: #2a2b2a;
    font-size: 1rem;
    font-weight: 700;
    margin: 3.125rem 0 1.25rem;
  }

  .tag_h1 div {
    width: .875rem;
    height: 1rem;
    border-radius: 0 .375rem .375rem 0;
    background: #5a85ec;
    margin: .25rem 2.25rem 0 0;
  }

  .tag_ul {
    width: 100%;
    box-sizing: border-box;
    padding: 0 0 0 3.125rem;
    margin: 0 0 -1.25rem;
  }

  .tag_li {
    width: 6.25rem;
    height: 3.125rem;
    line-height: 3.125rem;
    text-align: center;
    border-radius: .25rem;
    color: #2a2b2a;
    font-size: .875rem;
    border: .0625rem solid #dddddd;
    background: #ffffff;
    margin: 0 1.25rem 1.25rem 0;
    box-sizing: border-box;
  }

  .tag_li:hover {
    cursor: pointer;
  }

  .tag_li:nth-child(4n+4) {
    margin: 0 0 1.25rem 0;
  }

  .tag_li.active {
    color: #009c7f;
    border-color: #00c9a3;
    background: #e5f9f6;
  }

  .tag_btns {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 1.25rem 0 3.75rem;
  }

  .tag_btns div {
    cursor: pointer;
  }

  .tag_quit {
    width: 7.625rem;
    line-height: 2.375rem;
    border-radius: 1.1875rem;
    background: #f5f5f5;
    color: #666666;
    font-size: 1rem;
    text-align: center;
    margin: 0 2.125rem 0 0;
  }

  .tag_ok {
    width: 7.625rem;
    line-height: 2.375rem;
    border-radius: 1.1875rem;
    background: #00c9a3;
    color: #ffffff;
    font-size: 1rem;
    text-align: center;
  }
</style>
