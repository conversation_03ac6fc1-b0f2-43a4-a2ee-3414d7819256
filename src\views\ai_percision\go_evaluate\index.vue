<template>
  <div class="container">
    <div class="filter-box">
      <el-form class="form-sty" :model="form" label-form="7.5rem">
        <el-form-item label="试卷类型">
          <el-radio-group v-model="form.source" @change="handleTypeChange">
            <el-radio v-for="item in sourceArr" :label="item.sourceId" :key="item.sourceId">{{
              item.sourceName
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="筛选章节" v-show="form.source==1">
          <div class="cascader-box">
            <el-cascader v-model="form.chapterId" :popper-append-to-body="false" placeholder="请选择" ref="cascaderRef" :options="options" :props="props" @change="handleChange" />
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div class="test-box" v-loading="loading">
      <template v-if="testList.length > 0">
        <div class="test-wrap" v-for="item in testList">
          <div class="test-box-item">
            <div class="test-box-item-img">
              <span class="red-text" v-if="item.score&&item.score!='0'">{{item.score}}分</span>
            </div>
            <div class="test-box-item-info">
              <div class="test-box-item-info-title">
                {{item.title}}
              </div>
              <div class="test-box-item-info-data">
                <div>更新时间：{{item.reportDate}}</div>
                <div>浏览：{{ item.viewCount }}</div>
                <div v-show="form.source !== 1">题量：{{ item.quesCount }}</div>
              </div>
            </div>
            <div class="test-box-item-btn">
              <div class="test-box-item-btn-it btn" @click="handleDownload(item)">
                <img src="@/assets/img/percision/download.png" alt=""> 下载
              </div>
              <div class="test-box-item-btn-it blue-text" @click="testDetail(item)">
                查看详情>
              </div>
            </div>
          </div>
          <div class="hui-line"></div>
        </div>
        <div class="pagination-box">
          <Pagination
            :total="pageData.total"
            :current="pageData.current"
            @currentSizeChange="currentSizeChange"
            @pageClick="pageClick"/>
        </div>
      </template>
      <div class="empty" v-else>
        <img src="@/assets/img/percision/empty.png" alt="del" />
        <p>空空如也</p>
      </div>
    </div>
  </div>
  <!-- 下载试卷 -->
  <downloadTrestDialog v-if="dialogVisible" ref="downloadTrestDialogRef" :paper-detail="dowmloadData" />
</template>

<script lang="ts" setup>
import { computed, nextTick, onMounted, reactive, ref } from 'vue';
import { useRouter, useRoute } from 'vue-router'
import downloadTrestDialog from "@/components/TruePaperDownload/index.vue"
import { getChapterReportListApi } from "@/api/report"
import { getBookChapterListApi } from "@/api/book"
import { useUserStore } from "@/store/modules/user"
const userStore = useUserStore()
import { storeToRefs } from 'pinia'
const { subjectObj } = storeToRefs(userStore)
import { ElMessage } from 'element-plus';
import { dataEncrypt, dataDecrypt } from "@/utils/secret"
const downloadTrestDialogRef = ref()
const route = useRoute()
const router = useRouter()
const dowmloadData = reactive({
    id: '',
    title: ''
})
const queryData = route.query.data ? dataDecrypt(route.query.data) : {}
const dialogVisible = ref(false)
const sourceArr = ref([
  {
    sourceName: "单元试卷",
    sourceId: 1
  },
  // {
  //   sourceName: "月考试卷",
  //   sourceId: 2
  // },
  {
    sourceName: "期中试卷",
    sourceId: 3
  },
  {
    sourceName: "期末试卷",
    sourceId: 4
  },
  // {
  //   sourceName: "小升初真题",
  //   sourceId: 5
  // },
  // {
  //   sourceName: "小升初模拟",
  //   sourceId: 6
  // }
])
const options = ref(userStore.chapterList || [])
const cascaderRef = ref()
const props = {
  value: 'id',
  label: 'name',
  children: 'children'
}
const form = reactive({
  source: 1,
  chapterId: ""
})
const pageData = reactive({
  total: 0,
  current: 1,
  size: 10
})

const sort = ref(0)
const testList = ref([] as any[])
const loading = ref(false)
onMounted(async () => {
  if(form.chapterId == "") {
    await getChapterList()
  }
  getList()
})
//获取章节知识点树
const getChapterList = async() => {
  loading.value = true
  try {
    const res: any = await getBookChapterListApi({
      bookId: subjectObj.value.bookId,
      hierarchy: 3,
      type: 0
    })
    loading.value = false
    if(res.code == 200) {
      options.value = res.data || []
      userStore.setChapterList(options.value)
      form.chapterId = getLastId(res.data[0]).id
      nextTick(() => {
        userStore.setChapterId(form.chapterId, cascaderRef.value.getCheckedNodes()[0].text)
      })
    }
  } catch (error) {
    loading.value = false
    console.log(error)
  }
}
const getLastId = (data: any) => {
  if (data.children && data.children.length > 0) {
    return getLastId(data.children[0])
  } else {
    return data
  }
}
const currentSizeChange = (currentPage: number, pageSize: number) => {
  pageData.current = currentPage
  pageData.size = pageSize
  getList()
}
const pageClick = (val: number) => {
  pageData.current = val
  getList()
}
const getList = () => {
  const params = {
    // subject: subjectObj.value.id,
    current: pageData.current,
    size: pageData.size,
    bookId: subjectObj.value.bookId,
    chapterId: form.source == 1 ? form.chapterId : '',
    type: form.source
  }
  loading.value = true
  getChapterReportListApi(params)
    .then((res: any) => {
      testList.value = res.data ? res.data.records : []
      pageData.total = Number(res.data.total)
      loading.value = false
    })
    .catch(() => {
      loading.value = false
    })
}
const handleDownload = ({ id, title }: any) => {
  Object.assign(dowmloadData, {
    id: id,
    title: title
  })
  dialogVisible.value = true
  nextTick(() => {
    downloadTrestDialogRef.value.dialogShow()
  })
}
const handleTypeChange = (value: any) => {
  pageData.current = 1
  getList()
}
const handleChange = (value: any) => {
  pageData.current = 1
  form.chapterId = value[value.length - 1]
  nextTick(() => {
    userStore.setChapterId(form.chapterId, cascaderRef.value.getCheckedNodes()[0].text)
  })
  getList()
}
const testDetail = (data: any) => {
  router.push({
    path: '/ai_percision/go_evaluate/true_paper_detailE',
    query: {
      data: dataEncrypt({
        reportId: data.id,
        pageSource: '2'
      }),
    }
  })
}
</script>
<style lang="scss" scoped>
.container {
  height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height));
  background: #ffffff;
  position: relative;
  .title-coustom {
    width: 11.1875rem;
    height: 2.875rem;
    line-height: 2.875rem;
    text-align: center;
    border-bottom-right-radius: .625rem;
    background: #00c9a3;
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 700;
    position: absolute;
    left: -0.875rem;
    top: .625rem;
    span{
      display: inline-block;
      border-top: .4375rem solid #00886E;
      border-left: .4375rem solid #F5F5F5;
      border-bottom: .4375rem solid #F5F5F5;
      border-right: .4375rem solid #00886E;
      position: absolute;
      bottom: -0.875rem;
      left: 0;

    }
  }
  .filter-box {
    border-bottom: .0625rem solid #EAEAEA;
    padding: 1.875rem 1.25rem 1rem .875rem;
    .form-sty {
      padding: 1.25rem;
    }
    .justify-box {
      width: 3.75rem;
      text-align-last: justify;
      text-align: justify;
    }
    :deep(.el-radio) {
      margin-right: 1.5rem;
    }
    :deep(.el-radio__input) {
      display: none;
    }
    :deep(.el-radio__label) {
      text-align: center;
      padding: .1875rem .625rem;
      min-width: 3.75rem;
      color: #4d4d4d;
      line-height: 1.5rem;
      font-weight: bold;
    }
    .gery {
      :deep(.el-radio__label) {
        color: #cccccc;
      }
    }
    :deep(.el-radio.is-checked .el-radio__label) {
      border-radius: .25rem;
      width: 100%;
      background: #e3fff7;
      color: var(--percision-tecah-select-font-color) !important;
      font-weight: bold;
    }
    :deep(.el-form-item__label) {
      color: #7f7f7f;
      font-size: .875rem;
    }
    .cascader-box {
      :deep(.el-cascader) {
        width: 40rem;
        height: 1.9375rem;
        .el-input__wrapper {
          border-radius: 1.375rem;
          border: .0625rem solid #00c9a3;
          box-shadow: none;
        }
      }
    }
  }
  .test-box {
    height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height) - 12.5rem);
    width: 81.25rem;
    overflow-y: auto;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: .625rem 0;
    position: relative;
    box-sizing: border-box;
    .test-wrap{
      width: 100%;
      .hui-line{
        width: calc(100% - 1.75rem);
        border-bottom: .0625rem dashed #EAEAEA;
        margin: 0 0 0 .875rem;
        float: left;
      }
    }
    .pagination-box{
      padding-top: 1.25rem;
    }
    &-item {
      width: 100%;
      height: 6.875rem;
      display: flex;
      padding: 1.25rem 1.25rem 1.25rem 1.25rem;
      box-sizing: border-box;
      &:hover {
        background: #F4F9FF;
        .test-box-item-info-title{
          color: #4D8BFF;
        }
      }
      &-img {
        width: 100px;
        height: 100px;
        margin-top: -8px;
        font-size: .75rem;
        background-image: url(@/assets/img/percision/test-img.png);
        background-size: 100%;
        background-repeat: no-repeat;
        text-align: center;
        position: relative;
        span {
          position: absolute;
          top: 40%;
          left: 50%;
          transform: translate(-50%, -50%);
          display: inline-block;
          font-weight: bold;
        }
      }
      &-info {
        margin-left: 1rem;
        width: 67.5rem;
        margin-right: 1rem;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        &-title {
          color: #383838;
          font-size: 1rem;
          font-weight: bold;
        }
        &-data {
          div {
            height: 1.75rem;
            border-radius: .875rem;
            background: #F5F6FA;
            color: #A3A5B3;
            display: inline-block;
            box-sizing: border-box;
            padding: .375rem .75rem;
            font-size: .75rem;
            margin-right: .625rem;
          }
        }
      }
      &-btn {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        &-it {
          width: 86px;
          height: 32px;
          line-height: 32px;
          border-radius: .25rem;
          font-size: .875rem;
          text-align: center;
          cursor: pointer;
          img {
            width: .875rem;
            height: .875rem;
          }
        }
      }
    }
    .learn-img {
      position: fixed;
      bottom: 1.875rem;
      left: 55%;
      width: 14.0625rem;
      height: 3.125rem;
    }
  }
}
.btn {
  color: #ffffff;
  background: linear-gradient(to right, #66A3FF, #4D8BFF);
  border-radius: 4px;
}
.red-btn {
  background: #fee9e9;
  color: #dd2a2a;
}
.grey-btn {
  background: #F5F5F5;
  color: #666666;
  margin-right: 2.125rem;
}
.red-text {
  color: #dd2a2a;
}
.blue-text {
  color: #4D8BFF;
  font-weight: bold;
}
.btn-box {
  width: 7.625rem;
  height: 2.375rem;
  line-height: 2.375rem;
  border-radius: 1.1875rem;
}
.dialog-content {
  padding-top: 1.875rem;
  padding-bottom: 2.625rem;
  text-align: center;
  p {
    text-align: center;
    margin-bottom: 1rem;
  }
  img {
    width: 5.5rem;
    height: 6.5rem;
  }
}
.dialog-footer {
  display: flex;
  justify-content: center;
  padding-bottom: 1.875rem;
}
.empty {
  text-align: center;
  padding-top: 13.9375rem;
  img {
    width: 7.4375rem;
    height: 8rem;
  }
  p {
    text-align: center;
    color: #999999;
    font-size: .875rem;
    font-weight: 400;
  }
}
</style>
<style lang="scss">
.filter-select {
  .el-select-dropdown__list {
    padding: 0;
    .el-select-dropdown__item {
      width: 5.25rem;
      height: 3.0625rem;
      line-height: 3.0625rem;
      text-align: center;
      padding: 0;
    }
    .el-select-dropdown__item.is-selected {
      font-weight: 700;
      color: #009c7f;
      background-color: #E5F9F6;
    }
  }
}
</style>
