<template>
    <div class="container" v-loading="loading">
      <div class="left">
        <div class="top-box">
            <div class="title-handle">
                <div class="title-box">
                    <img class="test-icon" src="@/assets/img/percision/title.png" />
                    <div class="test-title">{{ paperData.title }}</div>
                    <div class="icon-btn size84" @click="handleDownload(paperData)">
                        <img src="@/assets/img/percision/download.png" alt="">
                        下载
                    </div>
                </div>
                <div class="btn" :class="paperData.collectStatus?'grey-btn': ''" @click="addMine" v-loading="loadingBtn">{{paperData.collectStatus?' + 已加入我的试卷': ' + 加入我的试卷'}}</div>
            </div>
            <div class="title-data">
                <div class="title-data-item">更新时间：{{ paperData.reportDate }}</div>
                <div class="title-data-item">浏览：{{ paperData.viewCount }}</div>
                <div class="title-data-item">题量：{{ paperData.quesCount }}</div>
                <div class="title-data-item" v-if="paperData.regionName">地区：{{ paperData.regionName }}</div>
            </div>
        </div>
        <div class="test-box">
            <div v-for="(item, index) in allTest"
            :key="item.quesId">
                <div class="test-tittle">
                    <div v-html="resetSty(item, index + 1)" />
                </div>
                <div class="test-body" v-html="resetOptions(item)" />
            </div>
        </div>
      </div>
      <div class="right">
        <div class="line-text">
            <el-divider style="width: 2.5625rem" border-style="dashed"></el-divider>密<el-divider style="width: 6.25rem" border-style="dashed"></el-divider>封<el-divider style="width: 6.25rem" border-style="dashed"></el-divider>线<el-divider style="width: 2.5625rem" border-style="dashed"></el-divider>
        </div>
        <div class="test-type-box">
            <div class="scroll-box">
                <div class="test-type-item" v-for="(item, index) in paperData.cates" :key="index">
                    <p>{{ testArr[index]?.value.length }}</p>
                    <div>{{ item.seq }} 、{{ item.name }}</div>
                </div>
                <div class="test-type-item green">
                    <p>{{ paperData.quesCount }}</p>
                    <div>总计</div>
                </div>
            </div>
            <div class="icon-btn size285" @click="toWrite">
                <img src="@/assets/img/percision/write.png" alt="">
                开始作答
            </div>
            <p class="test-type-box-tips">点击开始作答后，该套试卷自动加入到“我的试卷”</p>
        </div>
        <div class="line-text">
            <el-divider style="width: 2.5625rem" border-style="dashed"></el-divider>密<el-divider style="width: 6.25rem" border-style="dashed"></el-divider>封<el-divider style="width: 6.25rem" border-style="dashed"></el-divider>线<el-divider style="width: 2.5625rem" border-style="dashed"></el-divider>
        </div>
      </div>
    </div>
    <!-- 下载试卷 -->
    <downloadTrestDialog v-if="dialogVisible" ref="downloadTrestDialogRef" :paper-detail="dowmloadData" />
</template>

<script lang="ts" setup>
// import { useAuthStore } from '../store'
import { useRouter,useRoute } from 'vue-router'
import { getReportGetApi, collectReportApi } from "@/api/report"
import { nextTick, onMounted, reactive, ref } from 'vue'
import { dataEncrypt, dataDecrypt, mergeObject } from "@/utils/secret"
import downloadTrestDialog from "@/components/TruePaperDownload/index.vue"
import { ElMessage } from 'element-plus'
const route = useRoute()

const downloadTrestDialogRef = ref()
const dialogVisible = ref(false)
const queryData = route.query.data ? dataDecrypt(route.query.data) : {}
const loading = ref(false)
const loadingBtn = ref(false)
let paperData = reactive({
    title: '',
    id: '',
    source: '',
    reportDate:'',
    viewCount: '',
    quesCount: '',
    regionName: '',
    collectStatus: false,
    cates: [] as any[],
    userReportId: ''
})
const testArr = ref([] as any[])
const allTest = ref([] as any[])
const router = useRouter()
const dowmloadData = reactive({
    id: '',
    title: ''
})
onMounted(() => {
    getDetail()
})
const getDetail = () => {
    loading.value = true
    getReportGetApi({reportId: queryData.reportId}).then((res: any) => {
        if (res.code == 200) {
            paperData = mergeObject(paperData, res.data)
            testArr.value = res.data.groups
            let arr = []
            res.data.groups.map((item: any) => {
                arr = arr.concat(item.value)
            })
            allTest.value = arr
            //判断有没有做过题
            paperData.collectStatus = paperData.userReportId ? true : false
        }
        loading.value = false
    }).catch((error) => {
        loading.value = false
    })
}
const addMine = () => {
    if (!paperData.collectStatus) {
        loadingBtn.value = true
        collectReportApi({reportId: queryData.reportId}).then((res: any) => {
            if (res.code == 200) {
                paperData.collectStatus = true
                ElMessage.success("加入我的试卷成功")
                loadingBtn.value = false
            }
        }).catch((error) => {
            loadingBtn.value = false
        })
    }
}
const handleDownload = ({ id, title }: any) => {
  Object.assign(dowmloadData, {
    id: id,
    title: title
  })
  dialogVisible.value = true
  nextTick(() => {
    downloadTrestDialogRef.value.dialogShow()
  })
}
//过滤修改题干标签内容
const resetSty = function (testItem: any, sort?: number) {
  const tittle = sort + "." + filterContent(testItem.content)
  return tittle
}
//过滤掉填空题等div标签的contenteditable属性
const filterContent = (contect: string) => {
  let highlightedResult = ""
  highlightedResult = contect.replaceAll('contenteditable="true"', " ")
  highlightedResult = highlightedResult.replaceAll("contenteditable='true'", " ")
  return highlightedResult
}
//过滤修改选项内容
const resetOptions = function (testItem: any) {
    let optionHtml = ""
    if (!testItem.options) return
    testItem.options.map((item: any, index: number) => {
        optionHtml += `<div class="answer-item"> ${String.fromCharCode(65 + index)}. <div style="display:inline-table;max-width:39.8125rem">${item}</div></div>`
    })
    return optionHtml
}
// pageSource 1是真题试卷，2是文科AI精准学去测评, 3是理科知识图谱单元测试
const toWrite = () => {
    let url = ''
    if (queryData.pageSource == '1') {
        url = '/true_paper/true_paper_page/paper_write_switchT'
    } else if (queryData.pageSource == '2') {
        url = '/ai_percision/go_evaluate/true_paper_writeE'
    } else if (queryData.pageSource == '3') {
        url = '/ai_percision/knowledge_graph_detail_unit/paper_writeU'
    }
    router.push({
        path: url,
        query: {
            data: dataEncrypt({
                id: paperData.id,
                source: paperData.source,
                pageSource: queryData.pageSource
            })
        }
    })
}

</script>
<style lang="scss" scoped>
.container {
    display: flex;
    .left {
        padding: 1.25rem;
        width: 60.3125rem;
        background: #ffffff;
        .top-box {
            border-bottom: .0625rem solid #EAEAEA;
            .title-handle {
                display: flex;
                justify-content: space-between;
                .title-box {
                    display: flex;
                    align-items: center;
                    .test-icon {
                        width: 1.125rem;
                        height: 1.125rem;
                    }
                    .test-title {
                        color: #2a2b2a;
                        font-size: 1rem;
                        font-weight: 700;
                        margin-left: .5rem;
                        margin-right: .625rem;
                    }
                    .size84 {
                        width: 5.25rem;
                        height: 1.875rem;
                        font-size: .875rem;
                        img {
                            width: .75rem;
                            height: .75rem;
                        }
                    }
                }
                .btn {
                    width: 7.375rem;
                    height: 1.875rem;
                    line-height: 1.875rem;
                    text-align: center;
                    font-size: .875rem;
                    border-radius: .25rem;
                    border: .0625rem solid #009c7f;
                    background: #ffffff;
                    cursor: pointer;
                    color: #009c7f;
                }
            }
            .title-data {
                margin-top: .625rem;
                margin-bottom: 1rem;
                &-item {
                    display: inline-block;
                    border-radius: .875rem;
                    padding: .375rem .75rem;
                    background: #fef8e9;
                    color: #ef9d19;
                    font-size: .75rem;
                    font-weight: 400;
                    margin-right: .625rem;
                }
            }
        }
    }
    .right {
        padding: 1.125rem 0;
        width: 20.3125rem;
        margin-left: .625rem;
        background: #ffffff;
        box-sizing: border-box;
        height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height));
        .line-text {
            display: flex;
            align-items: center;
        }
        .test-type-box {
            height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height) - 8.125rem);
            box-sizing: border-box;
            padding: .625rem;
            .scroll-box {
                max-height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height) - 16.25rem);
                overflow-y: auto;
                display: flex;
                flex-wrap: wrap;
            }
            .test-type-item {
                display: inline-block;
                margin-left: .625rem;
                margin-bottom: .625rem;
                width: 8.5625rem;
                height: 7.3125rem;
                border: .0625rem solid #eaeaea;
                background: #ffffff;
                // padding: 1rem 0;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;
                box-sizing: border-box;
                p{
                    color: #2a2b2a;
                    font-size: 2.5rem;
                    font-weight: 700;
                    text-align: center;
                    margin: 0;
                }
                div{
                    color: #666666;
                    font-size: 1rem;
                    font-weight: 400;
                    text-align: center;
                }
            }
            .green {
                border: .0625rem solid #00c9a3;
                background: #e5f9f6;
                p {
                    color: #009c7f;
                }

            }
            .size285 {
                width: 17.8125rem;
                height: 2.75rem;
                font-size: 1rem;
                font-weight: 700;
                margin-left: .625rem;
                margin-top: 1.25rem;
                img {
                    width: 1rem;
                    height: 1rem;
                }
            }
            &-tips {
                text-align: center;
                color: #999999;
                font-size: .75rem;
                font-weight: 400;

            }
        }
    }
}
.icon-btn {
    border-radius: .25rem;
    background: #00c9a3;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    img {
        margin-right: .3125rem;
    }
}
.test-tittle {
    display: flex;
    font-size: .875rem;
    line-height: 1.4375rem;
    letter-spacing: .125rem;
    padding: 1.25rem;
    :deep(td) {
      min-width: 3.0625rem;
    }
  }
  .test-body {
    padding: 0 1.875rem;
    display: flex;
    align-items: center;
    flex-flow: row wrap;
    :deep(.answer-item) {
      margin-right: 5rem;
      margin-bottom: .625rem;
      font-size: 1rem;
    }
}
.test-box {
    color: #2A2B2A;
    height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height) - 8.125rem);
    overflow-y: auto;
}
.grey-btn {
    background: #f5f5f5!important;
    color: #999999!important;
    border: .0625rem solid #eaeaea!important;
    cursor: not-allowed!important;
}
</style>
