<template>
    <header class="page-header">
      <div class="breadcrumbs">
        <a href="#" class="back-link" @click.prevent="goBack">&lt; 返回</a>
        <span class="breadcrumb-separator">&nbsp;/&nbsp;</span>
        <span class="breadcrumb-item">作答</span>
      </div>
    </header>
    <div class="container" v-loading="writeState.loading">

      <div class="left">
        <div class="top-box">
            <div class="title-handle">
                <div class="title-box">
                    <img class="test-icon" src="@/assets/img/percision/title.png" />
                    <!-- <div class="test-title">{{ paperData.title }}123</div> -->
                    <div class="test-title">{{ queryData.title }} </div>
                </div>
            </div>
        </div>
        <div class="test-content">
            <div v-for="(item, index) in allTest" :key="item.index" class="test-content-ques" :class="setClass(item, index)" >
                <div class="squre"></div>
                <div class="test-tittle">
                    <div v-html="resetSty(item, index + 1)" />
                </div>
                <div class="test-body" v-html="resetOptions(item)" />
                <div>
                    <div v-if="item.cate == 1 || item.cate == 3" class="paper-content-ques">
                        <div v-if="item.cate == 1">
                            <el-checkbox-group v-model="item.userJson" class="checkbox-style-checkbox">
                                <el-checkbox
                                    v-for="(it,ind) in item.options"
                                    :key="ind"
                                    @change="checkChange(ind,index)"
                                    :label="ind"
                                    size="large" border
                                >
                                {{ String.fromCharCode(65 + ind) }}
                                </el-checkbox>
                            </el-checkbox-group>
                        </div>
                        <div v-else>
                            <el-checkbox-group v-model="item.userJson" class="checkbox-style-checkbox">
                                <el-checkbox
                                    v-for="(it,ind) in item.options"
                                    :key="ind"
                                    :label="ind"
                                    size="large" border
                                >
                                {{ String.fromCharCode(65 + ind) }}
                                </el-checkbox>
                            </el-checkbox-group>
                        </div>
                    </div>
                    <div v-else>
                        <div class="paper-content-ques">
                            <uploadAnswerImg :imgList="item.userJson" :index="index" @getImgList="handleImgList" />
                            
                            <!-- 非选择题上传图片后显示批改选项 -->
                            <div v-if="item.userJson && item.userJson.length > 0" class="answers-container">
                                <div class="answers">
                                    <div class="answer-box" @click="correcthandle(index, 1)" :class="item.userMark == 1?'green-box':''">
                                        <div></div>正确
                                    </div>
                                    <div class="answer-box" @click="correcthandle(index, 2)" :class="item.userMark == 2?'yellow-box':''">
                                        <div></div>半对
                                    </div>
                                    <div class="answer-box" @click="correcthandle(index, 0)" :class="item.userMark == 0?'red-box':''">
                                        <div></div>错误
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
      </div>
      <div class="right">
        <div class="time-box">
            <div class="time-text">用时:</div>
            <div class="time-number"> {{ timeState.hours < 10 ? "0" + timeState.hours : timeState.hours }} </div> :
            <div class="time-number"> {{ timeState.minutes < 10 ? "0" + timeState.minutes : timeState.minutes }} </div> :
            <div class="time-number"> {{ timeState.seconds < 10 ? "0" + timeState.seconds : timeState.seconds }} </div>
        </div>
        <div class="test-number-box">
            <div class="test-number-item" v-for="(item, index) in allTest" 
                 :class="setClass1(item, index)" 
                 @click="switchQuestion(index)"> {{ index + 1 }} </div>
            <div class="icon-btn size285" :class="isAllAnswered ? '' : 'disabled'" @click="submitAllAnswers" v-loading="writeState.btnloading">
                <img src="@/assets/img/percision/submit.png" alt="">
                提交批改
            </div>
        </div>
      </div>
      <div class="five-step-box" v-if="writeState.showStep">
        <fiveStep :sourceId="queryData.sourceId" :type="1" :update="false" @sendStep="sendStep"></fiveStep>
      </div>
    </div>
</template>
  
<script lang="ts" setup>
import { watch, onMounted, reactive, ref, onUnmounted, computed } from 'vue'
import uploadAnswerImg from '@/views/components/uploadAnswerImg/index.vue'
import coinAlert from "@/views/components/coinAlert/index.vue"
import { dataDecrypt, dataEncrypt, mergeObject } from "@/utils/secret"
import { useRouter, useRoute } from 'vue-router'
import fiveStep from "@/views/components/fiveStep/index.vue"
import { quesGetApi} from "@/api/video"
import { Action, ElMessage, ElMessageBox } from 'element-plus'
import { paperQuesApi,submitAssignment } from '@/api/online'

const route = useRoute()
const router = useRouter()
const timeState = reactive({
    hours: 0,
    minutes: 0,
    seconds: 0
})
const writeState = reactive({
    current: 0 as number | null,
    step: 1,
    btnloading: false,
    loading: false,
    showStep: false,
    disabled: true,
    trainingId: "",
    //积分
    jfShow: false,
    jfHide: true,
    jfNum: '0',
    jfSource: '0',
    itemTimer: 0, // 单题计时器
    lastTimestamp: 0 // 上次计时时间戳
})
interface Ques {
    cate: number;
    cateName: string;
    content: string;
    displayAnswer: string;
    analyse: string;
    method: string;
    discuss: string;
    options: any[];
    pointVos: any[];
    userJson: any[];
    answers: any[];
}

class AData {
    quesId: string = "";
    cate: number = 0;
    cateName: string = "";
    trainTime: string = "";
    userAnswer: string[] = [];
    userMark: number | null = null;
    userMarks: number | null = null;
    showAnalyse: boolean = false;
    content: string = "";
    ques: Ques = { // 添加 ques 属性
        cate: 0,
        cateName: "",
        content: "",
        analyse: "",
        discuss: "",
        method: "",
        displayAnswer: "",
        options: [],
        pointVos: [],
        userJson: [],
        answers: []
    };
}
let detailData
let timer :  NodeJS.Timeout | null = null
const queryData = route.query.data ? dataDecrypt(route.query.data) : {}
const allTest = ref([] as any[])

// 检查所有题目是否都已作答和批改
const checkAllAnswersComplete = () => {
    const unansweredItems = allTest.value.filter(item => {
        // 选择题只需要有选择
        if (item.cate == 1 || item.cate == 3) {
            return !item.userJson || item.userJson.length === 0;
        } 
        // 非选择题需要上传图片并完成批改
        else {
            return !item.userJson || item.userJson.length === 0 || item.userMark === undefined || item.userMark === null;
        }
    });
    
    return unansweredItems.length === 0;
};

// 计算属性检查所有题目是否已作答
const isAllAnswered = computed(() => {
    return checkAllAnswersComplete();
});

watch(() => timeState.seconds, () => {
    if(timeState.seconds == 60) {
        timeState.minutes ++
        timeState.seconds = 0
    }
    if(timeState.minutes == 60) {
        timeState.hours ++
        timeState.minutes = 0
    }
})

// 自定义返回方法
const customGoBack = () => {
    router.go(-1)
}

onMounted(() => {
    // 初始化时间戳
    writeState.lastTimestamp = Date.now()
    
    getDetails()
    
    // 启动计时器
    timer = setInterval(() => {
        timeState.seconds++
        writeState.itemTimer++ // 更新单题计时
    }, 1000)
    
    window.customGoBack = customGoBack
})

onUnmounted(() => {
    if (timer !== null) { // 添加类型安全检查
        clearInterval(timer)
        timer = null // 确保timer被清空
    }
    // 重置计时器状态
    writeState.itemTimer = 0
    
    // 清除自定义返回方法
    if (window.customGoBack) {
        delete window.customGoBack
    }
})
// 隐藏积分
const jfHide = () => {
    writeState.jfShow = false
}
// 获取学习步骤
const sendStep = ( data: number) => {
    writeState.step = data
}

// 将时分秒转换为毫秒时间戳
const convertTimeToMilliseconds = () => {
    const totalSeconds = timeState.hours * 3600 + timeState.minutes * 60 + timeState.seconds
    return totalSeconds * 1000 // 转换为毫秒
}

const getDetails = () => {
    writeState.loading = true
    paperQuesApi({ studentPaperId: queryData.studentPaperId}).then((res1: any) => {
        if (res1.code == 200) {
            detailData = res1.data
            timeState.seconds = Number(0)  / 1000
            res1.data.forEach((item) => {
                item.showAnalyse = false
                item.userJson = []
            })
            allTest.value = res1.data
            
            // 设置第一题为当前题目
            writeState.current = 0
            writeState.itemTimer = 0
        }
        writeState.loading = false

    }).catch((error) => {
        writeState.loading = false
    })
}

const setClass = (item: any, index: number) => {
    let classState = ""
    if (writeState.current == index) {
        classState = "black-text"
    }
    return classState
}

// 修改 setClass1 函数，根据题目完成状态来设置题号样式
const setClass1 = (item: any, index: number) => {
    let classState = ""
    
    // 当前选中的题目显示为蓝色
    // if (writeState.current == index) {
    //     classState = "blue"
    // } 
    // // 已完成的题目显示为绿色
    // else
    if (isQuestionCompleted(item)) {
        classState = "green"
    }
    
    return classState
}

// 判断题目是否已完成（选择题已选择或非选择题已上传图片并批改）
const isQuestionCompleted = (item: any) => {
    if (item.cate == 1 || item.cate == 3) {
        // 选择题：有选择即为完成
        return item.userJson && item.userJson.length > 0;
    } else {
        // 非选择题：需要上传图片并进行批改
        return item.userJson && 
               item.userJson.length > 0 && 
               item.userMark !== undefined && 
               item.userMark !== null;
    }
}

// 添加点击题号切换题目的方法
const switchQuestion = (index: number) => {
    // 允许切换到任何题目
    writeState.itemTimer = 0
    writeState.current = index
}

// 监听当前题目变化
watch(() => writeState.current, (newVal, oldVal) => {
    if (newVal !== oldVal && newVal !== null) {
        // 切换题目时重置单题计时器
        writeState.itemTimer = 0
    }
})

// 更新选择题选择处理函数
const checkChange = (val: any, index: number) => {
    // 验证 index 是否在有效范围内
    if (index < 0 || index >= allTest.value.length) {
        console.error(`Invalid index: ${index}`);
        return;
    }

    // 确保 allTest.value[index] 存在且 userJson 已初始化
    const currentItem = allTest.value[index];
    if (!currentItem) {
        console.error(`Item at index ${index} is undefined`);
        return;
    }

    // 更新 userJson
    currentItem.userJson = [val];
    
    // 更新提交按钮状态
    updateAllAnsweredState();
    
    // 选择题选择完后自动切换到下一题
    if (index < allTest.value.length - 1) {
        setTimeout(() => {
            writeState.current = index + 1;
        }, 300); // 短暂延迟，让用户感知到选择已完成
    }
}

// 添加处理批改选项点击的函数
const correcthandle = (index: number, userMark: number) => {
    // 验证 index 是否在有效范围内
    if (index < 0 || index >= allTest.value.length) {
        console.error(`Invalid index: ${index}`);
        return;
    }

    // 确保 allTest.value[index] 存在
    const currentItem = allTest.value[index];
    if (!currentItem) {
        console.error(`Item at index ${index} is undefined`);
        return;
    }

    // 更新题目的批改状态
    currentItem.userMark = userMark;
    
    // 检查是否能够提交（所有题目都已作答并批改）
    updateAllAnsweredState();
}

// 更新提交按钮状态
const updateAllAnsweredState = () => {
    // isAllAnswered 是计算属性，会自动更新
};

// 更新图片上传处理函数
const handleImgList = (index: number, imgList: any) => {
    // 验证 index 是否在有效范围内
    if (index < 0 || index >= allTest.value.length) {
        console.error(`Invalid index: ${index}`);
        return;
    }

    // 确保 allTest.value[index] 存在
    const currentItem = allTest.value[index];
    if (!currentItem) {
        console.error(`Item at index ${index} is undefined`);
        return;
    }

    // 更新 userJson 和重置批改状态（如果有更新图片）
    if (currentItem.userJson && currentItem.userJson.length !== imgList.length) {
        // 图片发生变化，重置批改状态
        currentItem.userMark = null;
    }
    
    currentItem.userJson = imgList;
    
    // 更新提交按钮状态
    updateAllAnsweredState();
    
    // 自动检查是否可以切换到下一题
    if (index < allTest.value.length - 1 && imgList.length > 0) {
        // 图片上传后可以手动点击批改选项，所以不自动切换题目
        // 这里仅更新题号状态，不自动切换
    }
}

const goBack = () => {
  router.go(-1);
};

//过滤掉填空题等div标签的contenteditable属性
const filterContent = (contect: string) => {
  let highlightedResult = ""
  highlightedResult = contect.replaceAll('contenteditable="true"', " ")
  highlightedResult = highlightedResult.replaceAll("contenteditable='true'", " ")
  return highlightedResult
}

//过滤修改题干标签内容
const resetSty = function (testItem: any, sort?: number) {
  const tittle = "（" + testItem.cateName + "）" +sort + "." + '&nbsp;&nbsp;' + filterContent(testItem.content)
  return tittle
}

//过滤修改选项内容
const resetOptions = function (testItem: any) {
    let optionHtml = ""
    if (!testItem.options) return
    testItem.options.map((item: any, index: number) => {
        optionHtml += `<div class="answer-item"> ${String.fromCharCode(65 + index)}. <div style="display:inline-table;max-width:39.8125rem">${item}</div></div>`
    })
    return optionHtml
}

// 一次性提交所有答案
const submitAllAnswers = () => {
    if (!isAllAnswered.value) {
        ElMessage({
            message: '请确保所有题目都已完成作答和批改',
            type: 'warning'
        })
        return
    }

    ElMessageBox.confirm('确认提交所有答案进行批改?', '提交确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        writeState.btnloading = true
        
        // 准备所有题目的答案数据，包括批改结果
        const items = allTest.value.map((item, index) => {
            // 处理选择题
            if (item.cate == 1 || item.cate == 3) {
                return {
                    cate: item.cate,
                    trainingItemId: item.trainingItemId,
                    userJson: item.userJson,
                    studentPaperId: queryData.studentPaperId,
                    quesId: item.quesId
                }
            } 
            // 处理非选择题，包含批改结果
            else {
                const imgUrls = item.userJson.map((imgItem: any) => imgItem.url.replace('https://xiaoyin-test.obs.cn-south-1.myhuaweicloud.com', '').replace('https://xiaoyin.obs.cn-south-1.myhuaweicloud.com', ''))
                return {
                    userJson: imgUrls,
                    quesId: item.quesId,
                    userMark: item.userMark, // 添加批改结果
                    isBase64:false
                }
            }
        })
        // 确保使用最新的计时值
        const finalTrainTime = timer !== null ? timeState.seconds * 1000 : convertTimeToMilliseconds();
        console.log(items,finalTrainTime)
        const params = {
            studentPaperId: queryData.studentPaperId,
            times: finalTrainTime,
            status:2,   //状态：（1：未完成、2：已完成）
            items: items
        }
        
        // 使用submitAssignment API提交所有答案
        submitAssignment(params)
        .then((res: any) => {
            if (timer !== null) {
                clearInterval(timer)
                timer = null
            }
            
            writeState.btnloading = false
            
            if (res.code === 200) {
                ElMessage({
                    message: '提交成功',
                    type: 'success'
                })
                
                // 跳转到报告页面
                // router.push({
                //     name: 'Analysis',
                //     query: {
                //         data: dataEncrypt({
                //             studentPaperId: queryData.studentPaperId,
                //             pageSource: '1'
                //         }),
                //     }
                // })
            } else {
                ElMessage({
                    message: res.msg || '提交失败',
                    type: 'error'
                })
            }
        })
        .catch((error) => {
            console.error('提交失败:', error)
            writeState.btnloading = false
            ElMessage({
                message: '提交失败，请稍后重试',
                type: 'error'
            })
        })
    }).catch(() => {
        // 用户取消提交
    })
}

const handleAnalysis = () => {
    router.push({ 
        path: '/ai_percision/knowledge_graph_detail/paper_analysis'
    })
}
</script>
<style lang="scss" scoped>
.container {
    display: flex;
    position: relative;
    .five-step-box {
        position: absolute;
        right: -8.75rem;
        top: 11.25rem;
    }
    .left {
        width: 60.3125rem;
        .top-box {
            padding: 1.25rem;
            background: #ffffff;
            margin-bottom: 10px;
            .title-handle {
                display: flex;
                justify-content: space-between;
                .title-box {
                    display: flex;
                    align-items: center;
                    .test-icon {
                        width: 1.125rem;
                        height: 1.125rem;
                    }
                    .test-title {
                        color: #2a2b2a;
                        font-size: 1rem;
                        font-weight: 700;
                        margin-left: .5rem;
                        margin-right: .625rem;
                    }
                    .size84 {
                        width: 5.25rem;
                        height: 1.875rem;
                        font-size: .875rem;
                        img {
                            width: .75rem;
                            height: .75rem;
                        }
                    }
                }
                .btn {
                    width: 7.375rem;
                    height: 1.875rem;
                    line-height: 1.875rem;
                    text-align: center;
                    font-size: .875rem;
                    border-radius: .25rem;
                    border: .0625rem solid #009c7f;
                    background: #ffffff;
                    cursor: pointer;
                    color: #009c7f;
                }
            }
            .title-data {
                margin-top: .625rem;
                margin-bottom: 1rem;
                &-item {
                    display: inline-block;
                    border-radius: .875rem;
                    padding: .375rem .75rem;
                    background: #fef8e9;
                    color: #ef9d19;
                    font-size: .75rem;
                    font-weight: 400;
                    margin-right: .625rem;
                }
            }
        }
        .test-content {
            height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height));
            box-sizing: border-box;
            overflow-y: auto;
            .test-content-ques {
                background: #ffffff;
                width: 100%;
                box-sizing: border-box;
                padding: 1.25rem 0;
                margin-bottom: .625rem;
                position: relative;
                color: #999999;
                .squre {
                    width: .875rem;
                    height: 1rem;
                    border-radius: 0 .375rem .375rem 0;
                    background: #5a85ec;
                    position: absolute;
                    top: 1.625rem;
                    left: 0;
                }
                .test-tittle,.test-body {
                    padding: 0 1.875rem;
                }
            }
        }
    }
    .right {
        padding: 1.125rem 0;
        width: 20.3125rem;
        margin-left: .625rem;
        background: #ffffff;
        box-sizing: border-box;
        height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height));
        .time-box {
            display: flex;
            align-items: center;
            color: #2a2b2a;
            font-size: 1.875rem;
            font-weight: 700;
            padding-left: .625rem;
            padding-bottom: 1.25rem;
            border-bottom: .0625rem dashed #eaeaea;
            .time-text {
                font-size: 1rem;
                font-weight: 700;
            }
            .time-number {
                width: 3.75rem;
                height: 3.75rem;
                line-height: 3.75rem;
                text-align: center;
                border-radius: .25rem;
                border: .0625rem solid #eaeaea;
                background: #f5f5f5;
                margin: 0 .625rem;
            }
        }
        .test-number-box {
            height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height) - 8.125rem);
            box-sizing: border-box;
            padding: 1.25rem .625rem 1.25rem 0;
            .test-number-item {
                display: inline-block;
                margin-left: .625rem;
                margin-bottom: .625rem;
                border-radius: .25rem;
                width: 2.5rem;
                height: 2.5rem;
                line-height: 2.5rem;
                text-align: center;
                color: #2a2b2a;
                font-size: 1rem;
                font-weight: 400;
                border: .0625rem solid #eaeaea;
                background: #f5f5f5;
                box-sizing: border-box;
            }
            .disabled {
                background: #bebebe;
                cursor: not-allowed;
            }
            .blue {
                background: #5a85ec;
                color: #ffffff;
            }
            .red {
                background: #dd2a2a;
                color: #ffffff;
            }
            .green {
                background: #00c9a3;
                color: #ffffff;
            }
            .yellow {
                background: #f1be21;
                color: #ffffff;
            }
            .size285 {
                width: 17.8125rem;
                height: 2.75rem;
                font-size: 1rem;
                font-weight: 700;
                margin-left: .625rem;
                margin-top: 1.25rem;
                img {
                    width: 1rem;
                    height: 1rem;
                }
            }
        }
    }
}
.icon-btn {
    border-radius: .25rem;
    background: #00c9a3;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    img {
        margin-right: .3125rem;
    }
}
.answer-img-box {
    padding-left: 1.875rem;
    padding-top: 1.25rem;
    .answer-img {
        width: 10.8125rem;
        height: 10.8125rem;
        border-radius: .25rem;
        margin-right: .625rem;
    }
}
:deep(.el-checkbox-group) {
    .el-checkbox {
        width: 6.25rem;
        height: 3.125rem;
        margin-right: 1.25rem;
        display: inline-flex;
        justify-content: center;
    }
    .is-checked {
        .el-checkbox__inner {
            background: #5a85ec;
            border: .0625rem solid #5a85ec;
        }
        .el-checkbox__label {
            color: #5a85ec;
        }
    }
    .el-checkbox__inner {
        &:hover {
            border: .0625rem solid #5a85ec;
        }
    }
}
.show-analyse {
    width: 100%;
    background: #fef8e9;
    padding-left: 1.875rem;
    height: 2.1875rem;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    color: #666666;
    font-size: .75rem;
    font-weight: 400;
    margin-top: 1.25rem;
    span {
        margin-left: .375rem;
    }
}
.analyse {
    padding: .625rem 1.875rem;
    letter-spacing: .125rem;
    background: #fef8e9;
    div {
      margin-bottom: .625rem;
    }
}
.flex-sty {
  display: flex;
  font-size: .875rem;
  align-items: baseline;
  div {
    max-width: 52.375rem;
    line-height: 1.0625rem;
  }
  span {
    text-wrap: nowrap;
    font-weight: 700;
    letter-spacing: normal;
  }
}
.paper-content-ques {
    margin-top: 1.25rem;
    border-top: .0625rem dashed #EAEAEA;
    padding: 1.25rem 1.875rem;
}
.blue-btn {
    width: 7.625rem;
    height: 2.375rem;
    line-height: 2.375rem;
    text-align: center;
    border-radius: 1.1875rem;
    background: #00c9a3;
    color: #ffffff;
    font-size: 1rem;
    cursor: pointer;
    margin-right: .625rem;
    font-weight: 400;
}
.black-text {
    color: black!important;
}
.red-border {
    border: .0625rem solid #dd2a2a;
    color: black!important;
    .squre {
        background: #dd2a2a!important;
    }
    .wrong-selected {
        border: .0625rem solid #dd2a2a!important;
        background: #fce9e9!important;
        div {
            background-image: url(@/assets/img/percision/wrong-check.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
}
.green-border {
    color: black!important;
    border: .0625rem solid #00C9A3;
    .squre {
        background: #00C9A3!important;
    }
    .selected {
        border: .0625rem solid #00c9a3!important;
        background: #e5f9f6!important;
        div {
            background-image: url(@/assets/img/percision/right-check.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
}
.yellow-border {
    color: black!important;
    border: .0625rem solid #f1be21;
    .squre {
        background: #f1be21!important;
    }
    .selected {
        border: .0625rem solid #00c9a3!important;
        background: #e5f9f6!important;
        div {
            background-image: url(@/assets/img/percision/right-check.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
    .wrong-selected {
        border: .0625rem solid #dd2a2a!important;
        background: #fce9e9!important;
        div {
            background-image: url(@/assets/img/percision/wrong-check.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
}
.green-box {
    border: .0625rem solid #00c9a3!important;
    background: #e5f9f6!important;
    div {
        background-image: url(@/assets/img/percision/right-check.png);
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
}
.red-box {
    border: .0625rem solid #dd2a2a!important;
    background: #fce9e9!important;
    div {
        background-image: url(@/assets/img/percision/wrong-check.png);
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
}
.yellow-box {
    border: .0625rem solid #f1be21!important;
    background: #fef8e8!important;
    div {
        background-image: url(@/assets/img/percision/harf-right.png);
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
}
.answers {
    display: flex;
    margin-top: 1.25rem;
    .answer-box {
        width: 6.25rem;
        height: 3.125rem;
        border-radius: .25rem;
        cursor: pointer;
        border: .0625rem solid #dddddd;
        background: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1.25rem;
        &:first-child {
            margin-left: 1.875rem;
        }
        div {
            border: .0625rem solid #999999;
            width: 1rem;
            height: 1rem;
            border-radius: .125rem;
            margin-right: .625rem;
        }
    }
}
.answers-container {
    margin-top: 1rem;
}
  .page-header {
  margin-bottom: 18px;margin-top: 20px;
}

.breadcrumbs {
  font-size: 14px;
  color: #606266;
  display: flex;
  align-items: center;
}

.breadcrumbs .back-link {
  color: #606266;
  text-decoration: none;
  font-size: 18px;
}
.breadcrumbs .back-link:hover {
  color: #00bfa5;
}

.breadcrumb-separator {
  color: #c0c4cc;
  margin: 0 5px;
}

.breadcrumb-item {
  color: #606266;
}
.breadcrumb-item.active {
  color: #303133;
  font-weight: 500;
}
</style>
<style lang="scss">
.answer-item:not(:last-child) {
    margin-bottom: 1.875rem;
}
</style>