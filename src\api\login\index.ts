import { request } from "@/utils/axios"
const api = "/api"
const sdkapi = "/api/xiaoyeoo/sdkapi"

// 账号密码登录
export const pwdLoginApi = (data: object) => {
  return request({
    // url: `${api}/user/loginSDK/login`,//小优
    url: `${api}/user/loginSDK/studyRoom/login`,//自习室
    method: "POST",
    data
  })
}

// 获取手机验证码
export const sendCodeApi = (data: object) => {
  return request({
    url: `${api}/user/loginSDK/sendCode`,
    method: "POST",
    data
  })
}

// app 注册
export const registerApi = (data: object) => {
  return request({
    url: `${api}/user/loginSDK/register`,
    method: "POST",
    data
  })
}

// app 设置密码、重置密码（安卓没用）
export const savePwdNewApi = (data: object) => {
  return request({
    url: `${sdkapi}/learn/user/savePwdNew`,
    method: "POST",
    data
  })
}

// 忘记密码
export const forgetPwdApi = (data: object) => {
  return request({
    url: `${api}/user/loginSDK/forgetPassword`,
    method: "POST",
    data
  })
}
