/* 倍速弹窗 */
.dplayer-setting-box {
  width: 70px !important;
  right: 45px !important;
  bottom: 0px !important;
  padding-bottom: 50px !important;
  /* transform: none !important; */
  background: none !important;
}

.dplayer-setting-speed-panel {
  background: rgba(28, 28, 28, 0.9);
}

.dplayer-setting-box>div {
  display: inline-block !important;
  transform: none !important;
}

.dplayer-setting-speed-panel {
  width: 100%;
  text-align: center;
}

.dplayer-setting-icon {
  color: #fff;
  width: 70px !important;
  text-align: center;
  line-height: 1.3;
}

.dplayer-setting:hover {
  cursor: pointer;
}

.dplayer-setting-speed-item.green .dplayer-label {
  color: #1DDFAC !important;
}

.dplayer-setting-origin-panel {
  display: none !important;
}

/* 隐藏动画 */
.dplayer-setting-box {
  transition: none !important;
}

/* 屏蔽右键 */
.dplayer-menu {
  display: none !important;
}

/* 中间播放图标 */
.dplayer-bezel span {
  zoom: 1.25;
  transform: none !important;
  animation: none !important;
  opacity: 0 !important;
}

.diplayer-loading-icon {
  display: none !important;
}

.dplayer-bezel svg {
  display: none !important;
}

.dplayer-bezel-icon.play span {
  opacity: 1 !important;
}

.dplayer-bezel-icon.play {
  opacity: 1 !important;
  background-color: rgba(0, 0, 0, 0.5);
  background-image: url('@/assets/img/teachroom/play2.svg') !important;
  background-repeat: no-repeat !important;
  background-position: center center !important;
  background-size: 30px 30px !important;
  cursor: pointer !important;
  pointer-events: all !important;
}

.dplayer-bezel-icon.play:hover {
  pointer-events: all !important;
  cursor: pointer !important;
}

/* 全屏按钮title */
[data-title]:hover::after {
  content: attr(data-title);
  position: absolute;
  background-color: #333;
  color: #fff;
  padding: 5px;
  border-radius: 3px;
  white-space: nowrap;
  z-index: 10;
  right: 0px;
  top: -35px;
  display: inline-block;
}

.dplayer-hide-controller [data-title]:hover::after {
  display: none;
}

/* 全屏标题 */
.full-tit {
  position: absolute;
  width: 90%;
  top: 50px;
  left: 10px;
  align-self: flex-start;
  font-size: 28px;
  color: #fff;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: inline-block;
  z-index: 99;
}

.dplayer-hide-controller .full-tit {
  display: none;
}

/* 测试样式 */
/* .dplayer-controller-mask,.dplayer-controller{
         opacity: 1 !important;
         transform:none !important;
       } */
/* .dplayer div{
         display: block !important;
         transform:none !important;
       } */