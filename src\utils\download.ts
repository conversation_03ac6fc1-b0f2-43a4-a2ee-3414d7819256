/**
 * 获取 blob
 * url 目标文件地址
 */
function getBlob(url : string) {
  return new Promise((resolve) => {
    const xhr = new XMLHttpRequest()
    xhr.open("GET", url, true)
    xhr.responseType = "blob"
    xhr.onload = () => {
      if (xhr.status === 200) {
        resolve(xhr.response)
      }
    }
    xhr.send()
  })
}
/**
 * 保存 blob
 * filename 想要保存的文件名称
 */
function saveAs(blob : any, filename : string) {
  const link = document.createElement("a")
  const body : any = document.querySelector("body")
  link.href = window.URL.createObjectURL(blob)
  link.download = filename
  link.style.display = "none"
  body.appendChild(link)
  link.click()
  body.removeChild(link)
  window.URL.revokeObjectURL(link.href)
}

// 下载文件，url:http开头的链接，filename：文件名
export function download(url : string, filename : string) {
  getBlob(url).then((blob) => {
    saveAs(blob, filename)
  })
}
// 下载blob文件，url:blob，filename：文件名，type:文件类型
export function downloadBlob(url : string, filename : string, type : string) {
  const blob = new Blob([url], { type: type })
  const link = document.createElement("a")
  link.style.display = "none"
  link.href = URL.createObjectURL(blob)
  document.body.appendChild(link)
  link.setAttribute("download", filename)
  link.click()
  URL.revokeObjectURL(link.href)
  document.body.removeChild(link)
}

//建立一个可存取到file的url
export function getObjectURL(file:any) {
  var url:any = null;
  if (window.URL != undefined) { // mozilla(firefox)
    url = window.URL.createObjectURL(file);
  } else if (window.webkitURL != undefined) { // webkit or chrome
    url = window.webkitURL.createObjectURL(file);
  }
  return url;
}