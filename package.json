{"name": "my-vue-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build && tsc --build --clean", "preview": "vite preview"}, "dependencies": {"@types/three": "^0.174.0", "axios": "^1.8.4", "crypto-js": "^4.2.0", "echarts": "^5.6.0", "element-plus": "^2.9.6", "js-cookie": "^3.0.4", "pinia": "^3.0.1", "pinia-plugin-persistedstate": "^4.2.0", "qrcode": "^1.5.3", "qrcodejs": "^1.0.0", "swiper": "^3.4.2", "three": "^0.174.0", "vue": "^3.5.13", "vue-cropperjs": "^5.0.0", "vue-draggable-plus": "^0.3.2", "vue-router": "^4.5.0"}, "devDependencies": {"@babel/plugin-proposal-optional-chaining": "^7.21.0", "@types/crypto-js": "^4.2.2", "@types/js-cookie": "^3.0.6", "@types/node": "^22.13.10", "@vitejs/plugin-legacy": "^4.1.1", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "fast-glob": "^3.3.3", "html2canvas": "^1.4.1", "sass": "^1.62.0", "terser": "^5.43.1", "typescript": "~5.7.2", "unplugin-auto-import": "^19.1.1", "unplugin-vue-components": "^28.4.1", "unplugin-vue-define-options": "^3.0.0-beta.6", "vite": "^4.0.0", "vite-plugin-babel": "^1.3.2", "vite-plugin-restart": "^0.4.0", "vite-plugin-svg-icons": "^2.0.1", "vite-svg-loader": "^4.0.0", "vitest": "^3.0.9", "vue-tsc": "^2.2.4"}}