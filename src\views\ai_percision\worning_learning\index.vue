<template>
    <div class="container" v-loading="reportState.loading">
        <div class="test-header">
            <div class="blue-btn" @click="toCorrect" v-if="allTest.length>0">
                去订正
            </div>
            <div>
                共{{reportState.pointCount}}个知识点，{{ reportState.noteCount }}道错题未订正
            </div>
            <div class="underline-bg-text" @click="exportNote">
                <img src="@/assets/img/percision/download-blue.png" alt="download1" >下载Word
            </div>
        </div>
        <div class="test-content">
            <div class="ques_box" id="quesBox" ref="quesBoxRef" @scroll="handleScroll" v-if="allTest.length > 0">
                <div v-for="(item, index) in allTest" :key="item.quesId">
                    <div class="title-data">
                        <div class="title-data-item">{{reportState.type==1?'未订正':'已订正'}}</div>
                        <div class="title-data-item">错题来源：{{sourceType[item.source]}}</div>
                        <div class="title-data-item">做错次数：{{item.count}}</div>
                    </div>
                    <div class="test-content-ques" >
                        <div class="squre"></div>
                        <div class="test-tittle">
                            <div v-html="resetSty(item, index + 1)" />
                        </div>
                        <div class="test-body" v-html="resetOptions(item)" />
                        <div class="show-analyse">
                            <el-switch size="small"  @change="togAnswer(item,item.showAnalyse)"  v-model="item.showAnalyse" /> <span>显示答案与解析</span>
                        </div>
                        <div v-show="item.showAnalyse" class="analyse">
                            <div class="flex-sty">
                                <span>【知识点】</span>&nbsp;&nbsp;
                                <div v-html="item.ques.pointVos[0].name" />
                            </div>
                            <div class="flex-sty">
                                <span>【答案】</span>&nbsp;&nbsp;
                                <div v-html="item.ques.displayAnswer" />
                            </div>
                            <div class="flex-sty">
                                <span>【分析】</span>&nbsp;&nbsp;
                                <div v-html="item.ques.analyse" />
                            </div>
                            <div class="flex-sty">
                                <span>【解答】</span>&nbsp;&nbsp;
                                <div v-html="item.ques.method" />
                            </div>
                            <div class="flex-sty">
                                <span>【点评】</span>&nbsp;&nbsp;
                                <div v-html="item.ques.discuss" />
                            </div>
                        </div>
                        <div class="point-box">
                            <div class="point-box-vidio">
                                <div class="gery-bg-text" v-for="(item2,index2) in item.ques.pointVos" :data-id="item2.pointId"
                                :data-name="item2.name" :data-subject="item.ques.subject" :data-noteid="item.noteId"
                                :data-i="index2" @click="goWklist">
                                    <img src="@/assets/img/percision/play-blue.png" alt="playblue" ><span>{{item2.name}}</span>
                                </div>
                            </div>
                            <div class="point-box-btn" @click="goJuyi(item.noteId)">
                                <div class="blue-btn-small">举一反三</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="pg_loading">
                  <div class="pg_load" v-if="reportState.pgIndex < reportState.pgPage">
                    <img src="@/assets/img/note/loading.svg" class="pg_gif" />
                    <div class="pg_tip">正在加载...</div>
                  </div>
                  <div class="pg_load" v-else>
                    <div class="pg_tip">暂无更多</div>
                  </div>
                </div>
            </div>
            <!-- 无数据 -->
          <div class="nodata" v-if="!allTest.length" :class="!allTest.length ? '' : 'none'">
            <img src="@/assets/img/note/nodata.png" />错题本空空如也
          </div>
        </div>
        <div class="five-step-box" v-if="subjectObj.subject == '数学'">
            <fiveStep :sourceId="chapterObj.chapterId" :type="1" :update="true" @sendStep="sendStep"></fiveStep>
        </div>
    </div>
    <!-- 购买会员弹窗 -->
  <buyVip :show="reportState.showVip" @close="reportState.showVip = false"></buyVip>
</template>

<script lang="ts" setup>
import { watch, onMounted, reactive, ref, onUnmounted } from 'vue'
import { dataDecrypt, dataEncrypt, getDegreeName } from "@/utils/secret"
import { useUserStore } from "@/store/modules/user"
import buyVip from "@/views/components/buyVip/index.vue"
import { useRouter, useRoute } from 'vue-router'
import fiveStep from "@/views/components/fiveStep/index.vue"
import { listToDigestionApi, exportNoteApi } from '@/api/note'
import { storeToRefs } from 'pinia'
import { ElMessage } from 'element-plus'
import { useCommonStore } from "@/store/modules/common"
import { sourceType, wenke } from '@/utils/user/enum'
import { quesGetApi} from "@/api/video"
const commonStore = useCommonStore()
const route = useRoute()
const router = useRouter()

const reportState = reactive({
    type: 1,
    step: 1,
    switch: false,
    loading: false,
    correctRate: false,
    showVip: false,
    noteCount: 0,
    isload: 0,
    pgIndex: 1,
    pgSize: 20,
    pgTotal: 0,
    pgPage: 1,
    isFirst: 0,
    pointCount: 0,
    trainingId: ""
})
interface Ques {
    cate: number;
    cateName: string;
    content: string;
    displayAnswer: string;
    analyse: string;
    method: string;
    discuss: string;
    options: any[];
    pointVos: any[];
    userJson: any[];
    answers: any[];
    subject: number;
}

class AData {
    quesId: string = "";
    cate: number = 0;
    cateName: string = "";
    trainTime: string = "";
    noteId: string = "";
    viewCount: string = "";
    quesCount: string = "";
    userAnswer: string[] = [];
    userMark: number | null = null;
    showAnalyse: boolean = false;
    content: string = "";
    ques: Ques = { // 添加 ques 属性
        cate: 0,
        cateName: "",
        content: "",
        analyse: "",
        discuss: "",
        method: "",
        displayAnswer: "",
        options: [],
        pointVos: [],
        userJson: [],
        answers: [],
        subject: 0
    };
    count: number | null = null;
    source: number | null = null;
}
const queryData = route.query.data ? dataDecrypt(route.query.data) : {}
const allTest:any = ref<AData[]>([])
const userStore = useUserStore()
const quesBoxRef = ref(null);

const { subjectObj, chapterObj } = storeToRefs(userStore)
let chapterId2 = chapterObj.value.chapterId
chapterObj.value.chapterId = chapterId2?.replace('章节','')?.replace('单元测试','')

// 自定义返回方法
const customGoBack = () => {
    router.go(-1)
}

onMounted(() => {
    getDetails()
    window.customGoBack = customGoBack
})

onUnmounted(() => {
// 清除自定义返回方法
if (window.customGoBack) {
    delete window.customGoBack
}
})
// 获取学习步骤
const sendStep = ( data: number) => {
    reportState.step = data
}

  //显示答案
const togAnswer = async (item:any,isShow:any) => { 
    if(isShow){
        // 如果已经有完整的题目信息，直接显示，无需重复请求
        if (item.ques.analyse && item.ques.method && item.ques.discuss) {
            return
        }   
        try {
            // 添加加载状态，防止重复点击
            if (item.loading) return
            item.loading = true
            
            const response = await quesGetApi({id: item.ques.quesId}) as any
            
            if (response.code === 200 && response.data) {
                // 使用 Object.assign 来安全地合并数据，保留原有属性
                Object.assign(item.ques, response.data)
                
                // 确保必要的属性存在
                if (!item.ques.pointVos) {
                    item.ques.pointVos = []
                }
                if (!item.ques.options) {
                    item.ques.options = []
                }
                if (!item.ques.answers) {
                    item.ques.answers = []
                }
                console.log('题目详细信息已更新:', item.ques)
            } else {
                console.error('获取题目详细信息失败:', response)
                // 如果获取失败，关闭显示开关
                item.showAnalyse = false
                // 可以添加用户提示
            }
        } catch (error) {
            console.error('获取题目详细信息时发生错误:', error)
            // 发生错误时关闭显示开关
            item.showAnalyse = false
            // 可以添加用户提示
        } finally {
            // 清除加载状态
            item.loading = false
        }
    }

}

const getDetails = () => {
    reportState.loading = true
    const { subject } = subjectObj.value
    const { pgIndex, pgSize } = reportState
    const param = {
      subject,
      chapterId: chapterObj.value.chapterId,
      source: 0,
      importance: 0,
      mastery: 0,
      reason: 0,
      type: 1,
      wrongCount: 0,
    }
    listToDigestionApi(param, pgIndex, pgSize ).then((res: any) => {
        if (res.code == 200) {
            res.data.noteList.records.forEach((item) => {
                item.showAnalyse = false
            })
            if (reportState.isFirst) {
                reportState.pgIndex = pgIndex + 1
                reportState.isFirst = 1
                reportState.isload = 1
                allTest.value.concat(res.data.noteList.records)
            } else {
                reportState.pgIndex = pgIndex + 1
                reportState.isFirst = 1
                reportState.isload = 1
                reportState.pgTotal = res.data.noteList.tatal
                reportState.pgPage = res.data.noteList.pages
                allTest.value = res.data.noteList.records
            }
            reportState.pointCount = res.data.pointCount
            reportState.noteCount = res.data.noteCount
        }
        reportState.loading = false

    }).catch((error) => {
        reportState.loading = false
    })
}
  // 导出错题
  const exportNote = () => {
    if (getIsMember()) {
      let noteIds = [] as any[]
      allTest.value.map((item: any) => {
        noteIds.push(item.noteId)
      })
      let param = {
        type: 2,//0不带答案，1：解答
        noteIds: noteIds.toString()
      }
      exportNoteApi(param)
        .then((res : any) => {
          if (res.data) {
            ElMessage.success("导出成功")
            const link : any = document.createElement("a");
            link.href = res.data;
            link.setAttribute("download", '错题消化');
            document.body.appendChild(link);
            link.click();
            link.remove();
          } else {
            ElMessage.error("导出失败")
          }
        })
    }
  }
// 滚动到底部
const handleScroll = () => {
    const element : any = quesBoxRef.value;
    if (element) {
      if (element.scrollHeight - element.scrollTop === element.clientHeight) {
        pageLoad()
      }
    }
  }
  //分页加载
  const pageLoad = () => {
    const { pgIndex, pgPage, isload } = reportState
    if (pgIndex < pgPage && isload) {
        reportState.pgIndex = pgIndex + 1
        reportState.isFirst = 1
        reportState.isload = 0
        getDetails()
    }
  }

  // 判断会员状态
  const getIsMember = () => {
    const isVip = useUserStore().memberInfo
    if (isVip) {
      return true
    } else {
      reportState.showVip = true
      return false
    }
  }
// 点击知识点微课
const goWklist = (e : any) => {
    if (getIsMember()) {
      const { id, subject, noteid } = e.currentTarget.dataset
      if (wenke.includes(Number(subject)) || Number(subject) >= 30) {
        //文科、高中原知识点
        router.push({ name: "NoteWkvideo3", query: { pointId: id, type: 'jiucuo', subject, noteId: noteid } })
      } else {
        //初中小学优学派
        router.push({ name: "NoteWkvideo4", query: { pointId: id, type: 'jiucuo', subject, noteId: noteid } })
      }
    }
  }
//过滤修改题干标签内容
const resetSty = function (testItem: any, sort?: number) {
  const tittle = "（" + testItem.ques.cateName + "）" +sort + "." + filterContent(testItem.ques.content)
  return tittle
}
//过滤掉填空题等div标签的contenteditable属性
const filterContent = (contect: string) => {
  let highlightedResult = ""
  highlightedResult = contect.replaceAll('contenteditable="true"', " ")
  highlightedResult = highlightedResult.replaceAll("contenteditable='true'", " ")
  return highlightedResult
}
//过滤修改选项内容
const resetOptions = function (testItem: any) {
    let optionHtml = ""
    if (!testItem.ques.options) return
    testItem.ques.options.map((item: any, index: number) => {
        optionHtml += `<div class="answer-item"> ${String.fromCharCode(65 + index)}. <div style="display:inline-table;max-width:39.8125rem">${item}</div></div>`
    })
    return optionHtml
}
const toCorrect = () => {
    router.push({
        path: '/ai_percision/knowledge_graph_detail/paper_write_switchC',
        query: {
            data: dataEncrypt({
                chapterId: chapterObj.value.chapterId,
                pageSource: '6'
            })
        }
    })
    // router.push({
    //   name: 'worningLearningC',
    //   query: {
    //     data: dataEncrypt({
    //       chapterId: '',// 不用传章节
    //       noteId: [],
    //       pageSource: '6'
    //     })
    //   }
    // })
}
const goJuyi = (noteId) => {
    router.push({
        path: '/ai_percision/knowledge_graph_detail/paper_write_switchJ',
        query: {
            data: dataEncrypt({
                noteId: noteId,
                pageSource: '8'
            })
        }
    })
}
</script>
<style lang="scss" scoped>
.container {
    position: relative;
    .five-step-box {
        position: absolute;
        right: -8.75rem;
        top: 11.25rem;
    }
    .test-header {
        width: 81.25rem;
        height: 4.5rem;
        margin-bottom: .625rem;
        border: .0625rem solid #eaeaea;
        background: #ffffff;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 1.875rem;
        box-sizing: border-box;
        .underline-bg-text {
            color: #009c7f;
            font-size: .875rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            img {
                margin-right: .375rem;
                width: .875rem;
                height: .875rem;
            }
        }
        .switch-box {
            color: #666666;
            font-size: .75rem;
        }
    }
    .test-content {
        width: 81.25rem;
        height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height) - 5.125rem);
        box-sizing: border-box;
        overflow-y: auto;
        .test-content-ques {
            background: #ffffff;
            width: 100%;
            box-sizing: border-box;
            padding: 1.25rem 0;
            margin-bottom: .625rem;
            position: relative;
            .show-analyse {
                width: 100%;
                background: #fef8e9;
                padding-left: 1.875rem;
                height: 2.1875rem;
                box-sizing: border-box;
                display: flex;
                align-items: center;
                color: #666666;
                font-size: .75rem;
                font-weight: 400;
                margin-top: 1.25rem;
                span {
                    margin-left: .375rem;
                }
            }
            .squre {
                width: .875rem;
                height: 1rem;
                border-radius: 0 .375rem .375rem 0;
                background: #5a85ec;
                position: absolute;
                top: 1.625rem;
                left: 0;
            }
            .test-tittle,.test-body {
                padding: 0 1.875rem;
            }
        }
    }
}
.img-text {
    display: flex;
    align-items: center;
    justify-content: center;
    img {
        height: 1.4375rem;
        width: 1.125rem;
        margin-right: .625rem;
    }
}
.gery-bg-text {
    margin: 0 .625rem 0 0;
    padding: .375rem .75rem;
    border-radius: 1rem;
    background: #f5f5f5;
    color: #2a2b2a;
    font-size: .875rem;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    img {
        margin-right: .375rem;
        width: 1rem;
        height: 1rem;
    }
}
.pg_loading {
    width: 100%;
    text-align: center;
    margin: 1.25rem 0;
}
.icon-btn {
    border-radius: .25rem;
    background: #00c9a3;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    img {
        margin-right: .3125rem;
    }
}
.analyse {
    padding: .625rem 1.875rem;
    letter-spacing: .125rem;
    background: #fef8e9;
    div {
      margin-bottom: .625rem;
    }
}
.flex-sty {
  display: flex;
  font-size: .875rem;
  align-items: baseline;
  div {
    max-width: 52.375rem;
    line-height: 1.0625rem;
  }
  span {
    text-wrap: nowrap;
    font-weight: 700;
    letter-spacing: normal;
  }
}
.paper-content-ques {
    margin-top: 1.25rem;
    border-top: .0625rem dashed #EAEAEA;
    padding: 1.25rem 1.875rem;
}
.blue-btn {
    width: 7.625rem;
    height: 2.375rem;
    line-height: 2.375rem;
    text-align: center;
    border-radius: 1.1875rem;
    background: #00c9a3;
    color: #ffffff;
    font-size: 1rem;
    cursor: pointer;
    margin-right: .625rem;
    font-weight: 400;
}
.blue-btn-small {
    width: 6.125rem;
    height: 1.9375rem;
    line-height: 1.9375rem;
    border-radius: .25rem;
    background: #00c9a3;
    text-align: center;
    color: #ffffff;
    font-size: .875rem;
    margin-left: 1.875rem;
}
.black-text {
    color: black!important;
}
.center-box {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.answers {
    display: flex;
    margin-top: 1.25rem;
    .answer-box {
        width: 6.25rem;
        height: 3.125rem;
        border-radius: .25rem;
        border: .0625rem solid #dddddd;
        background: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1.25rem;
        &:first-child {
            margin-left: 1.875rem;
        }
        div {
            border: .0625rem solid #999999;
            width: 1rem;
            height: 1rem;
            border-radius: .125rem;
            margin-right: .625rem;
        }
    }
}
.title-data {
    margin-top: .625rem;
    background: #ffffff;
    padding-left: 1.875rem;
    padding-top: 1.25rem;
    &-item {
        display: inline-block;
        border-radius: .875rem;
        padding: .375rem .75rem;
        background: #fef8e9;
        color: #ef9d19;
        font-size: .75rem;
        font-weight: 400;
        margin-right: .625rem;
    }
}
.point-box {
    display: flex;
    justify-content: space-between;
    padding: 1.25rem 1.875rem 0 1.875rem;
    align-items: center;
    cursor: pointer;
    &-btn {
        border-left: .0625rem solid #ccc;
    }
}

/* 暂无数据 */
.nodata {
    flex: 1;
    height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height) - 5.125rem);
    color: #999999;
    font-size: .875rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-flow: column;
    background: #fff;
  }

  .nodata img {
    width: 7.4375rem;
    height: 8rem;
    margin: 0 0 .625rem;
  }
  .pg_tip{
    color: #999;
  }
</style>
<style lang="scss">
.answer-item:not(:last-child) {
    margin-bottom: 1.875rem;
}
</style>
