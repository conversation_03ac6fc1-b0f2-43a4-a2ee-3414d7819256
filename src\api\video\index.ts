import { request } from "@/utils/axios"
const api = "/api"
const sdkapi = "/api/xiaoyeoo/sdkapi"

// 名师课堂获取章节列表
export const getBookChapterApi = (data : any) => {
  return request({
    url: `${sdkapi}/book/getBookChapter`,
    method: "GET",
    params: data,
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

// 获取知识点视频(理科，用于知识点学习模块)-错题本-优学派
export const getKnowledgeVideosApi = (data : any) => {
  return request({
    url: `${sdkapi}/video/getKnowledgeVideos`,
    method: "GET",
    params: data,
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

// 知识点详情(知识点视频)-错题本-学大
export const pointDetailsApi = (data : any) => {
  return request({
    url: `${sdkapi}/point/details`,
    method: "GET",
    params: data,
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

// 知识点讲解-记录已学习
export const savePointDescApi = (data : any) => {
  return request({
    url: `${sdkapi}/video/savePointDesc`,
    method: "POST",
    data
  })
}

// 查询知识点微课-章节id 章节下知识点列表
export const queryPointCourseApi = (data : any) => {
  return request({
    url: `${sdkapi}/book/pointList`,
    method: "GET",
    params: data,
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

// 获取提高模式的知识点列表
export const getImprovementPointList = (data : any) => {
  return request({
    url: `${sdkapi}/point/getImprovementPointList`,
    method: "GET",
    params: data,
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

// 获取子级知识的
export const getSonPointList = (data : any) => {
  return request({
    url: `${sdkapi}/point/sonPointList`,
    method: "GET",
    params: data,
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

// 选中节点
export const getStudyPointApi  = (data : any) => {
  return request({
    url: `${sdkapi}/point/getStudyPoint`,
    method: "GET",
    params: data
  })
}

// 学习步骤
export const getStudvSten  = (data : any) => {
  return request({
    url: `${sdkapi}/point/getStudvSten`,
    method: "GET",
    params: data,
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}




// 根据章节获取视频（理科文科）
export const getVideoListApi = (data : any) => {
  return request({
    url: `${sdkapi}/video/getVideoList`,
    method: "GET",
    params: data,
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

// 知识点视频Url
export const getVideoUrlApi = (data : any) => {
  return request({
    url: `${sdkapi}/video/getVideoUrl`,
    method: "GET",
    params: data,
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

// 收藏、点赞、设置学习状态-视频
export const videoModifyApi = (data : any) => {
  return request({
    url: `${sdkapi}/video/modify`,
    method: "POST",
    data
  })
}

// 批量删除收藏
export const setVideoCollectApi = (data : any) => {
  return request({
    url: `${sdkapi}/video/deleteCollect`,
    method: "POST",
    data,
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

// 查询今日昨日收藏
export const videoDayCollectApi = (data : any) => {
  return request({
    url: `${sdkapi}/video/queryDayCollect`,
    method: "GET",
    params: data,
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

// 查询今日昨日收藏
export const videoMoreCollectApi = (data : any, current : any, size : any) => {
  return request({
    url: `${sdkapi}/video/queryMoreCollect?current=${current}&size=${size}`,
    method: "GET",
    params: data,
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

// 获取学习用户视频查看次数
export const getVideoReviewNumApi = () => {
  return request({
    url: `${sdkapi}/video/getVideoReviewNum`,
    method: "GET",
    // params: data,
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

// 收藏、点赞、设置学习状态-视频
export const setUserVideoViewNumberApi = (data : any) => {
  return request({
    // url: `${sdkapi}/video/setUserVideoViewNumber`,
    url: `${sdkapi}/userVideo/setUserVideoViewNumber`,
    method: "POST",
    data
  })
}

// 保存评分--打开知识点微课的时候调用一次
export const videoScoreSaveApi = (data : any) => {
  return request({
    url: `${sdkapi}/video/save`,
    method: "POST",
    data
  })
}


// 知识点列表(三级)(1.1.4)
export const pointListList = (data : any) => {
  return request({
    url: `${sdkapi}/point/pointList`,
    method: "GET",
    params: data,
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

// 获取试题解析
export const quesGetApi = (data : any) => {
  return request({
    url: `${sdkapi}/quesGet`,
    method: "GET",
    params: data,
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

// 视频列表_yxp【优学派同步视频】
export const getVideoListYxpApi = (data : any) => {
  return request({
    url: `${sdkapi}/video/getVideoList_yxp`,
    method: "POST",
    data
  })
}

// 任务首页
export const taskHomepageApi = () => {
  return request({
    url: `api/xiaoyeoo/task/taskHomepage`,
    method: "GET"
  })
}

// 当前任务-历史任务-督学
export const taskListApi = (data: any) => {
  return request({
    url: `api/xiaoyeoo/task/taskList`,
    method: "GET",
    params: data,
  })
}


