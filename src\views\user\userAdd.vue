<!-- 学习用户-新增/编辑 -->
<template>
  <div class="content" v-loading="pageLoading" element-loading-text="学生信息加载中..." element-loading-spinner="el-icon-loading" element-loading-background="rgba(255, 255, 255, 0.9)">
    <div class="header">
      <div class="inner">
        <img src="@/assets/img/user/owl.png" class="owl" />
        <div class="hd_back" @click="router.go(-1)">
          < 返回</div>
            <div class="tip" v-if="param.pageType=='add'">
              您还没有学生用户，请先添加学生用户，为您推荐专属学习内容。
            </div>
        </div>
      </div>
      <div class="inner">
        <div class="wrap">
          <!-- 学生信息 -->
          <div class="info">
            <div class="pos_mid">每个学生每{{param.dayCount}}天最多可修改{{param.modifyCount}}次年级教材！</div>
  
            <img src="@/assets/img/user/info.png" class="tag" />
            <div>
              <div class="photo">
                <template v-if="param.pageType == 'add'">
                  <!-- 选头像 -->
                  <div class="imgs" v-if="!param.headUrl">
                    <div :class="item.url == param.avatar?'active':''" v-for="item in param.imgList" :key="item.url"
                      @click="setHeadImg(item.url)">
                      <img :src="item.url" class="pic" @error="handleImageError" />
                      <img src="@/assets/img/user/circle.svg" class="picsel" />
                    </div>
                  </div>
                  <div class="imgs" v-else>
                    <div class="active">
                      <img :src="param.headUrl" class="pic" />
                      <img src="@/assets/img/user/circle.svg" class="picsel" />
                    </div>
                  </div>
                  <!-- 未上传 -->
                  <div class="upload">
                    <span>自定义</span>
                    <input type="file" id="file" accept="image/png, image/jpeg,image/jpg" class="file"
                      @change="uploadFiles">
                  </div>
                </template>
                <template v-if="param.pageType == 'edit'">
                  <!-- 已上传 -->
                  <div class="imgs">
                    <div class="active">
                      <img :src="param.headUrl" class="pic" />
                      <img src="@/assets/img/user/circle.svg" class="picsel" />
                    </div>
                  </div>
                  <!-- 未上传 -->
                  <div class="upload">
                    <span>自定义</span>
                    <input type="file" id="file" accept="image/png, image/jpeg,image/jpg" class="file"
                      @change="uploadFiles">
                  </div>
                </template>
              </div>
              <label class="lable">
                <div class="labtxt">昵称</div>
                <el-input placeholder="请输入昵称（最多不超过8个字符）" v-model="param.nickName" maxlength="8" class="nickname" />
              </label>
              <div class="lable">
                <div class="labtxt">年级</div>
                <el-select v-model="param.gradeId" placeholder="请选择" class="select" @change="getBookVersion(true)"
                  :disabled="!param.isModify">
                  <el-option v-for="item in param.gradeList" :key="item.value" :label="item.name" :value="item.value" />
                </el-select>
              </div>
              <div class="lable" :class="!param.isModify?'disabled':''">
                <div class="labtxt">学制</div>
                <div class="item">
                  <div :class="param.academic==1?'active':''" @click="setAcademic(1)">六三制</div>
                  <div :class="param.academic==2?'active':''" @click="setAcademic(2)">五四制</div>
                </div>
              </div>
              <div class="lable" :class="!param.isModify?'disabled':''">
                <div class="labtxt">学期</div>
                <div class="item">
                  <div :class="param.termId==1?'active':''" @click="setTermId(1)" :disabled="!param.isModify">上学期</div>
                  <div :class="param.termId==2?'active':''" @click="setTermId(2)" :disabled="!param.isModify">下学期</div>
                </div>
              </div>
            </div>
          </div>
          <!-- 设置教材 -->
          <div class="books" :class="!param.isModify?'disabled':''">
            <img src="@/assets/img/user/book.png" class="tag" />
            <div>
              <div class="map" v-if="param.isModify">
                <img src="@/assets/img/user/map.svg" />
                <span class="city" v-if="param.province||param.city">{{param.province+' '+param.city}}</span>
                <div class="area">选择地区</div>
              </div>
              <div style="width: 100%;height: 5rem;" v-else></div>
              <div class="mapbox" v-if="param.isModify">
                <el-cascader :options="param.cityList" :show-all-levels="false" class="elmap" style="width: 16.25rem;"
                  @change="getRegion" v-model="param.region"></el-cascader>
              </div>
              <!-- 没选时 -->
              <div class="perfect none">
                <img src="@/assets/img/user/nodata.png" />请先完善您的学生信息
              </div>
              <!-- 学科 -->
              <div class="subul" v-if="param.gradeId && !dataLoading">
                <template v-if="param.gradeId<10">
                  <!-- 初中小学 -->
                  <template v-for="item in param.versions" :key="item.subject">
                    <div class="subli" v-if="item.subjectName=='数学'">
                      <div class="sublab">
                        <img src="@/assets/img/user/shuxue.svg" />数学
                      </div>
                      <el-select v-model="item.bookId" placeholder="请选择" class="subtxt" :disabled="!param.isModify">
                        <el-option v-for="item2 in item.versions" :key="item2.bookId"
                          :label="item2.editionName + (item2.typeName || '')" :value="item2.bookId">
                        </el-option>
                      </el-select>
                    </div>
                  </template>
                  <template v-for="item in param.versions" :key="item.subject">
                    <div class="subli" v-if="item.subjectName=='物理'">
                      <div class="sublab">
                        <img src="@/assets/img/user/wuli.svg" />物理
                      </div>
                      <el-select v-model="item.bookId" placeholder="请选择" class="subtxt" :disabled="!param.isModify">
                        <el-option v-for="item2 in item.versions" :key="item2.bookId"
                          :label="item2.editionName + (item2.typeName || '')" :value="item2.bookId">
                        </el-option>
                      </el-select>
                    </div>
                  </template>
                  <template v-for="item in param.versions" :key="item.subject">
                    <div class="subli" v-if="item.subjectName=='化学'">
                      <div class="sublab">
                        <img src="@/assets/img/user/huaxue.svg" />化学
                      </div>
                      <el-select v-model="item.bookId" placeholder="请选择" class="subtxt" :disabled="!param.isModify">
                        <el-option v-for="item2 in item.versions" :key="item2.bookId"
                          :label="item2.editionName + (item2.typeName || '')" :value="item2.bookId">
                        </el-option>
                      </el-select>
                    </div>
                  </template>
                  <template v-for="item in param.versions" :key="item.subject">
                    <div class="subli" v-if="item.subjectName=='生物'">
                      <div class="sublab">
                        <img src="@/assets/img/user/shengwu.svg" />生物
                      </div>
                      <el-select v-model="item.bookId" placeholder="请选择" class="subtxt" :disabled="!param.isModify">
                        <el-option v-for="item2 in item.versions" :key="item2.bookId"
                          :label="item2.editionName + (item2.typeName || '')" :value="item2.bookId">
                        </el-option>
                      </el-select>
                    </div>
                  </template>
                  <template v-for="item in param.versions" :key="item.subject">
                    <div class="subli" v-if="item.subjectName=='地理'">
                      <div class="sublab">
                        <img src="@/assets/img/user/dili.svg" />地理
                      </div>
                      <el-select v-model="item.bookId" placeholder="请选择" class="subtxt" :disabled="!param.isModify">
                        <el-option v-for="item2 in item.versions" :key="item2.bookId"
                          :label="item2.editionName + (item2.typeName || '')" :value="item2.bookId">
                        </el-option>
                      </el-select>
                    </div>
                  </template>
                  <template v-for="item in param.versions" :key="item.subject">
                    <div class="subli" v-if="item.subjectName=='语文'">
                      <div class="sublab">
                        <img src="@/assets/img/user/yuwen.svg" />语文
                      </div>
                      <el-select v-model="item.bookId" placeholder="请选择" class="subtxt" :disabled="!param.isModify">
                        <el-option v-for="item2 in item.versions" :key="item2.bookId"
                          :label="item2.editionName + (item2.typeName || '')" :value="item2.bookId">
                        </el-option>
                      </el-select>
                    </div>
                  </template>
                  <template v-for="item in param.versions" :key="item.subject">
                    <div class="subli" v-if="item.subjectName=='英语'">
                      <div class="sublab">
                        <img src="@/assets/img/user/yingyu.svg" />英语
                      </div>
                      <el-select v-model="item.bookId" placeholder="请选择" class="subtxt" :disabled="!param.isModify">
                        <el-option v-for="item2 in item.versions" :key="item2.bookId"
                          :label="item2.editionName + (item2.typeName || '')" :value="item2.bookId">
                        </el-option>
                      </el-select>
                    </div>
                  </template>
                  <template v-for="item in param.versions" :key="item.subject">
                    <div class="subli" v-if="item.subjectName=='历史'">
                      <div class="sublab">
                        <img src="@/assets/img/user/lishi.svg" />历史
                      </div>
                      <el-select v-model="item.bookId" placeholder="请选择" class="subtxt" :disabled="!param.isModify">
                        <el-option v-for="item2 in item.versions" :key="item2.bookId"
                          :label="item2.editionName + (item2.typeName || '')" :value="item2.bookId">
                        </el-option>
                      </el-select>
                    </div>
                  </template>
                  <template v-for="item in param.versions" :key="item.subject">
                    <div class="subli" v-if="item.subjectName=='政治'">
                      <div class="sublab">
                        <img src="@/assets/img/user/zhengzhi.svg" />政治
                      </div>
                      <el-select v-model="item.bookId" placeholder="请选择" class="subtxt" :disabled="!param.isModify">
                        <el-option v-for="item2 in item.versions" :key="item2.bookId"
                          :label="item2.editionName + (item2.typeName || '')" :value="item2.bookId">
                        </el-option>
                      </el-select>
                    </div>
                  </template>
                  <template v-for="item in param.versions" :key="item.subject">
                    <div class="subli" v-if="item.subjectName=='道法'">
                      <div class="sublab">
                        <img src="@/assets/img/user/zhengzhi.svg" />道法
                      </div>
                      <el-select v-model="item.bookId" placeholder="请选择" class="subtxt" :disabled="!param.isModify">
                        <el-option v-for="item2 in item.versions" :key="item2.bookId"
                          :label="item2.editionName + (item2.typeName || '')" :value="item2.bookId">
                        </el-option>
                      </el-select>
                    </div>
                  </template>
                  <template v-for="item in param.versions" :key="item.subject">
                    <div class="subli" v-if="item.subjectName=='科学'">
                      <div class="sublab">
                        <img src="@/assets/img/user/kexue.svg" />科学
                      </div>
                      <el-select v-model="item.bookId" placeholder="请选择" class="subtxt" :disabled="!param.isModify">
                        <el-option v-for="item2 in item.versions" :key="item2.bookId"
                          :label="item2.editionName + (item2.typeName || '')" :value="item2.bookId">
                        </el-option>
                      </el-select>
                    </div>
                  </template>
                </template>
                <template v-else>
                  <!-- 高中 -->
                  <template v-for="item in param.versions" :key="item.subject">
                    <div class="subli" v-if="item.subjectName=='数学'">
                      <div class="sublab">
                        <img src="@/assets/img/user/shuxue.svg" />数学
                      </div>
                      <el-cascader :options="item.versions" class="subtxt2" v-model="item.bookId"></el-cascader>
                    </div>
                  </template>
                  <template v-for="item in param.versions" :key="item.subject">
                    <div class="subli" v-if="item.subjectName=='物理'">
                      <div class="sublab">
                        <img src="@/assets/img/user/wuli.svg" />物理
                      </div>
                      <el-cascader :options="item.versions" class="subtxt2" v-model="item.bookId"></el-cascader>
                    </div>
                  </template>
                  <template v-for="item in param.versions" :key="item.subject">
                    <div class="subli" v-if="item.subjectName=='化学'">
                      <div class="sublab">
                        <img src="@/assets/img/user/huaxue.svg" />化学
                      </div>
                      <el-cascader :options="item.versions" class="subtxt2" v-model="item.bookId"></el-cascader>
                    </div>
                  </template>
                  <template v-for="item in param.versions" :key="item.subject">
                    <div class="subli" v-if="item.subjectName=='生物'">
                      <div class="sublab">
                        <img src="@/assets/img/user/shengwu.svg" />生物
                      </div>
                      <el-cascader :options="item.versions" class="subtxt2" v-model="item.bookId"></el-cascader>
                    </div>
                  </template>
                  <template v-for="item in param.versions" :key="item.subject">
                    <div class="subli" v-if="item.subjectName=='地理'">
                      <div class="sublab">
                        <img src="@/assets/img/user/dili.svg" />地理
                      </div>
                      <el-cascader :options="item.versions" class="subtxt2" v-model="item.bookId"></el-cascader>
                    </div>
                  </template>
                  <template v-for="item in param.versions" :key="item.subject">
                    <div class="subli" v-if="item.subjectName=='语文'">
                      <div class="sublab">
                        <img src="@/assets/img/user/yuwen.svg" />语文
                      </div>
                      <el-cascader :options="item.versions" class="subtxt2" v-model="item.bookId"></el-cascader>
                    </div>
                  </template>
                  <template v-for="item in param.versions" :key="item.subject">
                    <div class="subli" v-if="item.subjectName=='英语'">
                      <div class="sublab">
                        <img src="@/assets/img/user/yingyu.svg" />英语
                      </div>
                      <el-cascader :options="item.versions" class="subtxt2" v-model="item.bookId"></el-cascader>
                    </div>
                  </template>
                  <template v-for="item in param.versions" :key="item.subject">
                    <div class="subli" v-if="item.subjectName=='历史'">
                      <div class="sublab">
                        <img src="@/assets/img/user/lishi.svg" />历史
                      </div>
                      <el-cascader :options="item.versions" class="subtxt2" v-model="item.bookId"></el-cascader>
                    </div>
                  </template>
                  <template v-for="item in param.versions" :key="item.subject">
                    <div class="subli" v-if="item.subjectName=='政治'">
                      <div class="sublab">
                        <img src="@/assets/img/user/zhengzhi.svg" />政治
                      </div>
                      <el-cascader :options="item.versions" class="subtxt2" v-model="item.bookId"></el-cascader>
                    </div>
                  </template>
                  <template v-for="item in param.versions" :key="item.subject">
                    <div class="subli" v-if="item.subjectName=='科学'">
                      <div class="sublab">
                        <img src="@/assets/img/user/kexue.svg" />科学
                      </div>
                      <el-cascader :options="item.versions" class="subtxt2" v-model="item.bookId"></el-cascader>
                    </div>
                  </template>
                </template>
              </div>
            </div>
          </div>
          <template v-if="param.pageType === 'add'">
            <div class="submit" :class="{ 'disabled': btnLoading }" @click="check" v-if="learnUsers.length==0">
              <span v-if="btnLoading" class="loading-icon"></span>{{ btnLoading ? '加载中...' : '确定并进入首页' }}
            </div>
            <div class="submit" :class="{ 'disabled': btnLoading }" @click="warning" v-else-if="learnUsers.length>=3">
              <span v-if="btnLoading" class="loading-icon"></span>{{ btnLoading ? '加载中...' : '确定' }}
            </div>
            <div class="submit" :class="{ 'disabled': btnLoading }" v-else @click="check">
              <span v-if="btnLoading" class="loading-icon"></span>{{ btnLoading ? '加载中...' : '确定' }}
            </div>
          </template>
          <div class="submit" :class="{ 'disabled': btnLoading }" v-else @click="checkTimes">
            <span v-if="btnLoading" class="loading-icon"></span>{{ btnLoading ? '加载中...' : '确定' }}
          </div>
        </div>
      </div>

    <!-- 确认弹窗 -->
    <el-dialog
      v-model="showModal"
      width="900px"
      height="300px"
      :show-close="false"
      :modal="true"
      :close-on-click-modal="false"
      class="teacher-detail-dialog"
    >
      <template #header>
          <div class="dialog-header" style="height: 200px;">
          <div class="dialog-title" style="height: 200px;">
            <span class="title-text"  style="margin-top: 20px;font-size: 18px;">提示</span>
          </div>
          <el-icon class="close-icon" @click="closeModal">
            <Close />
          </el-icon>
        </div>
      </template>

      <div class="teacher-detail-content" style="width: 500px;text-align: left;margin: 0 auto;">
          <div class="mt-2" style="text-align: center">
            <div style="color: #000;padding-top: 50px;padding-bottom: 150px;font-size: 16px;text-align: center;">本次修改为本周期最后一次修改，确认要修改吗?</div>
          <el-button @click="closeModal" style="width: 5.5rem">取消</el-button>
          <el-button type="primary" @click="check" >确认</el-button>
        </div>
      </div>
    </el-dialog>

    </div>
</template>

<script lang="ts" setup>
  import { KeyValueOfSubject } from '@/utils/user/commonData'
  import { gradeList, gradeNameList, subSort } from '@/utils/user/enum'
  import { reactive, ref, onMounted } from "vue"
  import { useUserStore } from "@/store/modules/user"
  import { type FormInstance, FormRules, ElMessage } from "element-plus"
  import { userGetAllApi, uploadApi, userAddApi, userUpdateApi, getRegionsTreeApi, bookVersionApi, userDefaultApi, getEditionByIpApi, getEditionListApi, getSectionEditionBookListApi } from "@/api/user"
  import router from "@/router"
  import { useRoute } from "vue-router"
import { fa } from 'element-plus/es/locale'

  const loading = ref(false)
  const isLoading = ref(false)
  const btnLoading = ref(false) // 新增按钮loading状态
  const pageLoading = ref(true) // 页面整体加载状态
  const dataLoading = ref(true) // 数据加载状态，用于控制表单数据显示
  const route = useRoute()
  const query = reactive<any>(route.query)
  const showModal = ref(false)
  //初始化
  onMounted(() => {
    // 设置页面加载状态
    pageLoading.value = true
    
    // 先获取地区数据
    getRegionsTree().then(() => {
      if (param.pageType === 'add') {
        // 新增时默认选择男孩头像
        param.avatar = boyImg
        getGPSCity()
      } else {
        // 编辑
        editLernInfo(query)
      }
    }).catch(() => {
      // 即使获取地区数据失败，也需要关闭页面加载状态
      pageLoading.value = false
      dataLoading.value = false
      ElMessage.warning('获取地区数据失败，请稍后重试')
    })
  })

  const boyImg = 'https://xiaoyin.obs.cn-south-1.myhuaweicloud.com/education/assets/web/user/boy.png'
const girlImg = 'https://xiaoyin.obs.cn-south-1.myhuaweicloud.com/education/assets/web/user/girl.png'
const defaultImg = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNDAiIGN5PSI0MCIgcj0iNDAiIGZpbGw9IiNGMkY4RkYiIHN0cm9rZT0iIzAwQkZBNSIgc3Ryb2tlLXdpZHRoPSIyIi8+CjxjaXJjbGUgY3g9IjQwIiBjeT0iMzIiIHI9IjEwIiBmaWxsPSIjMDBCRkE1Ii8+CjxwYXRoIGQ9Ik0yMiA1OEMyMiA1Mi40NzcxIDI2LjQ3NzEgNDggMzIgNDhINDhDNTMuNTIyOSA0OCA1OCA1Mi40NzcxIDU4IDU4VjYwSDIyVjU4WiIgZmlsbD0iIzAwQkZBNSIvPgo8L3N2Zz4K'
  const month = new Date().getMonth() + 1
  const learnUsers = JSON.parse(localStorage.learnUsers || "[]") || []
  const param : any = reactive({
    isFirst: 1,
    pageType: query.pageType || 'add',
    learnUsers,
    gradeList: gradeList,
    nickName: '',
    academic: 1, //六三制 1 五四制2
    termId: [8, 9, 10, 11, 12, 1].includes(month) ? 1 : 2,//'上学期': '下学期'
    avatar: query.pageType ? boyImg : '',
    headUrl: '',
    learnId: '',
    gradeId: '',
    modifyCount:'',     //modifyCount年级教材版本可修改总次数
    modifyCounted:'',   //年级教材版本可修改总次数
    dayCount:'',    //间隔天数
    gradeNum: '',
    gradeName: '',
    sex: '',
    sexVal: '',
    region: '',
    cityList: [],
    deviceType: 801,
    allEnum: [], // 教材类型、枚举
    subSel: '', // 教材类型
    selBooks: [], // 教材版本
    province: '',//省
    city: '',//区
    isModify: true,//是否可编辑
    versId: '', // 选中版本id
    versName: '', // 选中版本名称
    versions: [
      {
        region: '',
        subject: '',
        subjectEN: '',
        subjectName: '',
        bookId: "",
        editionName: "",
        editionId: "",
        name: "",
        termId: 0,
        termName: '',
        typeId: 0,
        versions: [
          { subject: '', bookId: "", name: "", editionId: 0, editionName: "", termId: 0, termName: "", typeId: 0, typeName: '' }
        ]
      }
    ],
    imgList: [
      {
        url: boyImg
      },
      {
        url: girlImg
      },
      {
        url: defaultImg
      }
    ],
  })
  // 编辑用户
  const editLernInfo = (e : any) => {
    let userNow : any = ''
    for (const i of learnUsers) {
      if (i.learnId == e.learnId) {
        userNow = i
        break
      }
    }
    const { learnId, nickName, avatar, versions, gradeId, gradeName, learnNo, sex, province, region, academic, termId, isModify,modifyCount,modifyCounted ,dayCount} = userNow
    //匹配性别
    // for (const i of sex) {
    //   if (i.val == sex) {
    //     i.active = 'active'
    //   }
    // }
    // param.sex=sexArr
    // param.sexVal= sex
    param.isModify = isModify
    param.termId = termId
    param.academic = academic
    param.learnId = learnId
    param.nickName = nickName
    param.headUrl = avatar
    param.versions = versions
    param.gradeId = gradeId
    param.gradeName = gradeName
    param.gradeNum = learnNo
    param.region = region || ''
    param.city = region || ''
    param.province = province || ''
    param.modifyCount = modifyCount
    param.modifyCounted = modifyCounted
    param.dayCount = dayCount
    
    // if (modifyCounted == 1) { 
    //   showModal.value = true
    // }
    getBookVersion(false)
  }

  // 选择头像
  const setHeadImg = (url : any) => {
    if (param.pageType === 'edit') {
      return
    }
    param.avatar = url
  }
  

  // 图片加载错误处理
  const handleImageError = (event: Event) => {
    const img = event.target as HTMLImageElement;
    // 使用本地默认头像或Base64编码的SVG作为备用
    img.src = '@/assets/img/user/default-avatar.svg';
    // 如果本地图片也不存在，使用SVG backup
    img.onerror = () => {
      img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNDAiIGN5PSI0MCIgcj0iNDAiIGZpbGw9IiNGNUY1RjUiLz4KPGNpcmNsZSBjeD0iNDAiIGN5PSIzMiIgcj0iMTIiIGZpbGw9IiNEREREREQiLz4KPHBhdGggZD0iTTIwIDYwQzIwIDUyLjI2ODQgMjYuMjY4NCA0NiAzNCA0NkM0MS43MzE2IDQ2IDQ4IDUyLjI2ODQgNDggNjBINjBDNjAgNDQuNjU0MSA0OS41OSAzMCAzNCAzMEMxOC40MSAzMCA4IDQ0LjY1NDEgOCA2MEgyMFoiIGZpbGw9IiNEREREREQiLz4KPC9zdmc+';
    };
  }

  //上传头像
  async function uploadFiles(e : any) {
    if (changeImg(e.target.files[0])) {
      let file = e.target.files[0]
      //修改文件名
      const type = file.type.split('/')
      const file2 = new File([file], 'web_user_' + new Date().getTime() + Math.floor(Math.random() * 10001) + '.' + type[1], { type: file.type });
      const formdata : any = new FormData()
      formdata.append("file", file2)
      uploadApi(formdata)
        .then((res : any) => {
          const data = res.data
          const url = data.url + data.key
          param.headUrl = url
          useUserStore().setData({ avatar: url })
          //解决重复图片不能上传
          let fileInput : any = document.getElementById('file');
          fileInput.value = null;
        })
    }
  }

  //限制图片大小和格式
  const changeImg = (file : any) => {
    const size = 1024 * 10
    const limit = file.size / 1024
    const isLtM = limit < size
    let msg = "上传图片大小不能超过" + size + "KB"
    const type = file.type
    const isType = type == "image/png" || type == "image/jpg" || type == "image/jpeg"
    if (!isType) {
      msg = "上传图片只支持png,jpg,jpeg格式"
    }
    if (!isLtM || !isType) {
      //解决重复图片不能上传
      let fileInput : any = document.getElementById('file');
      fileInput.value = null;
      ElMessage.error(msg)
      return false
    } else {
      return true
    }
  }

  // 根据学习用户年级查询教材版本
  const getBookVersion = (isBol = true) => {
    console.log("点击选择 年级")
    btnLoading.value = true // 设置按钮loading状态

    let gradeId : any = param.gradeId
    
    // 如果是编辑状态，更新learnNow中的gradeId
    if (param.pageType === 'edit' && param.learnId) {
      const currentLearnNow = JSON.parse(localStorage.learnNow || '{}')
      if (currentLearnNow && currentLearnNow.learnId === param.learnId) {
        currentLearnNow.gradeId = gradeId
        localStorage.setItem('learnNow', JSON.stringify(currentLearnNow))
        useUserStore().setlearnNow(currentLearnNow)
      }
    }
 
    if (gradeId > 9) {
      //高中时
      //获取学科editionId
      let data2 = {
        section: 30
      }
      getEditionListApi(data2).then((res2 : any) => {
        let ediList : any = res2.data || []
        //获取年级所有教材版本
        getSectionEditionBookListApi(data2).then((res : any) => {
          let allEnum = res.data || []
          let ediList2 : any = []
          // 匹配版本已有的editionId
          if (ediList.length && allEnum.length) {
            for (let x of ediList) {
              for (let y of allEnum) {
                //匹配相同学科version
                if (x.subject == y.subject) {
                  for (let n of x.versions) {
                    n.children = []
                    n.label = n.editionName
                    n.value = n.editionName
                    for (let z of y.versions) {
                      if (n.editionId == z.editionId) {
                        //同editionId并一类
                        z.label = z.typeName || z.name
                        z.value = z.bookId
                        n.children.push(z)
                      }
                    }
                  }
                }
              }
            }
            //删除没2级children的项
            for (let i of ediList) {
              let arr : any = {
                bookId: i.bookId,
                editionId: i.editionId,
                editionName: i.editionName,
                province: i.province,
                region: i.region,
                subject: i.subject,
                subjectEN: i.subjectEN,
                subjectName: i.subjectName,
                versions: []
              }
              for (let n of i.versions) {
                if (n.children.length) {
                  arr.versions.push(n)
                }
              }
              ediList2.push(arr)
            }
          }
          console.log(ediList2)
          allEnum = ediList2
          param.allEnum = allEnum
          if (param.pageType == 'add') {
            if (isBol) {
              param.versions = allEnum
              if (allEnum.length) {
                for (let i of allEnum) {
                  for (let n of i.versions) {
                    //默认第一个
                    if (n.children.length) {
                      n.bookId = n.children[0].bookId
                    }
                  }
                }
              }
            } else {
              param.versions = allEnum
            }
            //自动定位
            getGPSCity()
          } else {
            // 编辑时
            if (isBol || !param.isFirst) {
              if (allEnum.length) {
                for (let i of allEnum) {
                  for (let n of i.versions) {
                    //默认第一个
                    if (n.children.length) {
                      n.bookId = n.children[0].bookId
                    }
                  }
                }
              }
              param.versions = allEnum
              //自动定位
              getGPSCity()
            } else {
              //匹配学科对应的教材
              for (const x of param.versions) {
                let hasBook = 0
                x.versions = []
                for (const y of allEnum) {
                  if (x.subjectName == y.subjectName) {
                    x.versions = y.versions
                  }
                  for (let z of y.versions) {
                    if (x.bookId == z.bookId) {
                      hasBook = 1
                    }
                  }
                }
              }
              dataLoading.value = false
              pageLoading.value = false
            }
            param.isFirst = 0
          }
        }).finally(() => {
          btnLoading.value = false // 接口完成后取消loading状态
        })
      }).catch(() => {
        // 处理API调用失败的情况
        btnLoading.value = false
        dataLoading.value = false
        pageLoading.value = false
        ElMessage.warning('获取教材版本失败，请稍后重试')
      })
    } else {
      //初中小学
      let data = {
        gradeId,
        academic: gradeId > 9 ? '' : param.academic,
        termId: param.termId//gradeId > 9 ? '' :
      }
      bookVersionApi(data).then((res : any) => {
        const allEnum = res.data || []
        param.allEnum = allEnum
        if (param.pageType == 'add') {
          if (isBol) {
            param.versions = allEnum
            //默认选第一个
            if (allEnum.length) {
              for (let i of allEnum) {
                i.bookId = i.versions[0].bookId
              }
            }
          } else {
            param.versions = allEnum
          }
          //自动定位
          getGPSCity()
        } else {
          // 编辑时
          if (isBol || !param.isFirst) {
            if (allEnum.length) {
              for (let i of allEnum) {
                i.bookId = i.versions[0].bookId
              }
            }
            param.versions = allEnum
            //自动定位
            getGPSCity()
          } else {
            //匹配学科对应的教材
            for (const x of param.versions) {
              let hasBook = 0
              x.versions = []
              for (const y of allEnum) {
                if (x.subjectName == y.subjectName) {
                  x.versions = y.versions
                }
                for (let z of y.versions) {
                  if (x.bookId == z.bookId) {
                    hasBook = 1
                  }
                }
              }
              //第一次编辑时才匹配
              if (!hasBook && param.isFirst) {
                x.versions.unshift({
                  bookId: x.bookId,
                  editionId: Number(x.editionId),
                  editionName: x.editionName,
                  name: x.name,
                  subject: x.subject,
                  termId: x.termId,
                  termName: x.termName,
                  typeId: x.typeId,
                  typeName: ''
                })
              }
            }
            dataLoading.value = false
            pageLoading.value = false
          }
          param.isFirst = 0
        }
      }).finally(() => {
        btnLoading.value = false // 接口完成后取消loading状态
      }).catch(() => {
        // 处理API调用失败的情况
        btnLoading.value = false
        dataLoading.value = false
        pageLoading.value = false
        ElMessage.warning('获取教材版本失败，请稍后重试')
      })
    }
    isLoading.value = false
  }
  //设置学制
  const setAcademic = (num : any) => {
    param.isFirst = 0
    param.academic = num
    if (param.gradeId) {
      getBookVersion(false)
    }
  }
  //设置学期
  const setTermId = (num : any) => {
    param.isFirst = 0
    param.termId = num
    if (param.gradeId) {
      getBookVersion(false)
    }
  }

  //教材定位s
  //定位-城市
  const getGPSCity = () => {
    getGpsBook(param.region)
  }

  // 查询教材版本-定位
  const getGpsBook = (city : any) => {
    const { gradeId, versions, province } = param
    const data = {
      grade: gradeId || 7,
      academic: Number(gradeId) > 9 ? '' : param.academic, //63制1 54制2
      city: city || '',
      province: province || ''
    }
    isLoading.value = true
    
    // 设置超时，确保加载状态至少显示1秒，避免闪烁
    const minLoadingTime = 800
    const startTime = Date.now()
    
    getEditionByIpApi(data).then((res : any) => {
      if(res.code == 200){
        const elapsedTime = Date.now() - startTime
        const remainingTime = Math.max(0, minLoadingTime - elapsedTime)
        
        // 使用setTimeout确保加载动画至少显示minLoadingTime毫秒
        setTimeout(() => {
          // 数据加载成功后关闭页面加载状态
          pageLoading.value = false
          isLoading.value = false
          btnLoading.value = false // 确保在getGpsBook完成后也取消loading状态
          
          console.log(res,"打印一下 RES",res.data.length)
          const data = res.data || [],
            list : any = []
          for (const i of data) {
            const param2 : any = i.versions[0]
            param2.subjectName = i.subjectName
            list.push(param2)
          }
          if (Number(gradeId) > 9) {
            //高中
            //匹配同学科修改的教材
            for (const x of versions) {
              let hasBook = 0
              for (const n of x.versions) {
                for (const y of list) {
                  if (x.subjectEN == y.subject || x.subject == y.subject) {
                    //判断教材一致
                    let versions2 = n.children
                    if (versions2.length) {
                      for (let z of versions2) {
                        if (z.editionId == y.editionId && z.bookId == y.bookId) {
                          hasBook = 1
                          x.bookId = z.bookId
                          x.editionName = y.editionName
                          x.editionId = y.editionId
                        }
                      }
                    }
                  }
                }
              }
              //没匹配到选第一个
              if (!hasBook) {
                const vers : any = x.versions[0]
                x.bookId = vers.bookId + ''
                x.editionName = vers.editionName
                x.editionId = vers.editionId
              }
            }
            param.versions = versions
            // 后端会默认取深圳
            let region = city || data[0].region
            param.region = region.replace('-', '')
            //编辑时
            if (param.pageType == 'edit') {
              saveGpsBook(param.region)
            }
          } else {
            //初中小学
            //匹配同学科修改的教材
            for (const x of versions) {
              let hasBook = 0
              for (const y of list) {
                if (x.subjectEN == y.subject || x.subject == y.subject) {
                  //判断教材一致
                  let versions2 = x.versions
                  if (versions2.length) {
                    for (let z of versions2) {
                      if (z.editionId == y.editionId) {
                        hasBook = 1
                        x.bookId = z.bookId
                        x.editionName = y.editionName
                        x.editionId = y.editionId
                      }
                    }
                  }
                }
              }
              //没匹配到选第一个
              if (!hasBook) {
                const vers : any = x.versions[0]
                x.bookId = vers.bookId + ''
                x.editionName = vers.editionName
                x.editionId = vers.editionId
              }
            }
            param.versions = versions
            // 后端会默认取深圳
            let region = city || data[0].region
            param.region = region.replace('-', '')
            //编辑时
            if (param.pageType == 'edit') {
              saveGpsBook(param.region)
            }
          }
          
          // 数据加载完成，设置状态
          dataLoading.value = false
        }, remainingTime)
      }
    }).catch(() => {
      // 处理API调用失败的情况
      setTimeout(() => {
        btnLoading.value = false
        dataLoading.value = false
        pageLoading.value = false
        ElMessage.warning('获取教材版本失败，请稍后重试')
      }, Math.max(0, minLoadingTime - (Date.now() - startTime)))
    }).finally(() => {
      // 确保在任何情况下都会清除loading状态
      setTimeout(() => {
        btnLoading.value = false
      }, minLoadingTime)
    })
  }
  // 自动保存定位的教材
  const saveGpsBook = (city : any) => {
    const { versions, avatar, nickName, gradeNum, gradeId, learnId, sexVal } = param
    const versions2 : any = []
    //处理教材
    for (const i of versions) {
      versions2.push({
        subject: typeof i.subject === 'number' ? KeyValueOfSubject[i.subject] : i.subject,//转英文
        bookId: i.bookId
      })
    }
    const param2 = {
      versions: versions2,
      avatar: 'education' + avatar.split('/education')[1],
      nickName,
      gradeId,
      gradeNum,
      sex: sexVal,
      region: city,
      learnId
    }
    //没自动保存需求
    // userDefaultApi(param2).then(() => {
    //   refreshUsers(0)
    // })
  }
  //教材定位e

  //获取地区
  const getRegionsTree = () => {
    return getRegionsTreeApi()
      .then((res : any) => {
        if (res.data.length) {
          let arr = res.data
          for (let i of arr) {
            i.value = `${i.name}-${i.id}`
            i.label = i.name
            i.id = i.id
            if (i.children.length) {
              i.leaf = false
              for (let n of i.children) {
                n.value = `${n.name}-${n.id}`
                n.label = n.name
                n.id = n.id
                n.leaf = true
              }
            }
          }
          param.cityList = arr
        }
      })
  }

  //处理地区
  const getRegion = () => {
    let region : any = param.region, province = '', city = ''
    const lastIndex = region.length - 1
    //获取省市
    param.province = region[0].split('-')[0]
    param.city = region[1].split('-')[0]
    console.log(12, province, city)
    if (lastIndex >= 0) {
      const strIndex = region[lastIndex].indexOf("-")
      if (region[0].indexOf("市") > -1) {
        //直辖市
        region = region[0].slice(0, strIndex)
      } else {
        //省
        if (strIndex > 0) {
          region = region[lastIndex].slice(0, strIndex)
        }
      }

    } else {
      region = ""
    }
    param.region = region
    getGpsBook(param.city)
  }

  const warning = () => {
    ElMessage.error('最多只能添加3个学习用户！')
}
const checkTimes = () => { 

  if (param.modifyCounted == 1) { 
    showModal.value = true
  } else { 
    check()
  }
}
  //提交
  const check = () => {

    if (btnLoading.value) return; // 如果按钮处于loading状态，不执行提交
    
    btnLoading.value = true // 设置按钮loading状态
    loading.value = true
    const { nickName, gradeId, gradeNum, sexVal, versions, headUrl, avatar, learnId, region, academic, termId, province, city } = param
    if (!nickName) {
      ElMessage.error('请输入一个用户昵称！')
      btnLoading.value = false
      loading.value = false
      return
    }

    // if (!sexVal) {
    //   ElMessage.error('请选择性别！')
    //   return
    // }

    if (!gradeId) {
      ElMessage.error('请选择用户年级！')
      btnLoading.value = false
      loading.value = false
      return
    }
    const versions2 : any = []
    //处理教材
    for (const i of versions) {
      let bookId = i.bookId, bookId2 = ''
      if (bookId.length == 2) {
        bookId2 = bookId[1]
      } else {
        bookId2 = bookId
      }
      versions2.push({
        subject: i.subjectEN || i.subject,
        bookId: bookId2
      })
    }
    let avatar2 = headUrl || avatar
    const data = {
      versions: versions2,
      avatar: 'education' + avatar2.split('/education')[1],
      nickName,
      gradeId,
      gradeNum: gradeNum,
      sex: 0,
      province: province || '',
      region: city || '',
      deviceType: '801',
      learnId: '',
      academic,
      termId,
      isDefault: false
    }
    if (param.pageType === 'add') {
      data.deviceType = '801'
      userAddApi(data)
        .then(() => {
          loading.value = false
          ElMessage.success('用户添加成功！')
          refreshUsers(0)
        })
        .catch(() => {
          btnLoading.value = false
          loading.value = false
        })
    } else {
      data.learnId = learnId
      data.isDefault = true
      isLoading.value = true
      userUpdateApi(data)
        .then(() => {
          refreshUsers(0)
          //切换教材缓存
          localStorage.isBookChange = 1
          setTimeout(() => {
            ElMessage.success('修改成功！')
            isLoading.value = false
            btnLoading.value = false
            router.go(-1)
          }, 2000)
        })
        .catch(() => {
          btnLoading.value = false
          loading.value = false
          isLoading.value = false
        })
    }
  }

    //关闭老师详情弹窗
  const closeModal = () => {
    showModal.value = false

  }

  // 更新当前用户信息
  const refreshUsers = (check : any) => {
    //去除精准学缓存章节
    localStorage.removeItem('chapterList')
    localStorage.removeItem('chapterObj')
    const { pageType } = param
    const usersNow = localStorage.learnNow ? JSON.parse(localStorage.learnNow) : ''
    userGetAllApi()
      .then((res : any) => {
        const arr : any = [res.data]
        if (arr.length) {
          arr.forEach((item : any) => {
            item.gradeName = gradeNameList[item.gradeId]
          })
          localStorage.learnUsers = JSON.stringify(arr)
          //绑定也切换为当前用户
          if (usersNow) {
            // 当前有勾选过学生用户, 而且是编辑状态，更新当前用户信息
            if (pageType === 'edit' || check == 1) {
              let obj = []
              obj = arr.find((item : any) => {
                return item.learnId === usersNow.learnId
              })
              //列表页会更新
              localStorage.learnUsers = JSON.stringify(arr)
              if (check == 1) {
                // 替换当前用户 - 就是最后一个人变当前
                const arr2 = arr[arr.length - 1]
                localStorage.learnNow = JSON.stringify(arr2)
                // 更新store中的learnNow
                useUserStore().setlearnNow(arr2)
                //修改为默认用户s
                const { versions, avatar, gradeId, learnId, region, nickName, sex, learnNo } = arr2
                const versions2 : any = []
                //处理教材
                for (const n of versions) {
                  versions2.push({
                    subject: n.subject,
                    bookId: n.bookId
                  })
                }
                const param = {
                  versions: versions2,
                  avatar: 'education' + avatar.split('/education')[1],
                  nickName,
                  gradeId,
                  gradeNum: learnNo,
                  sex,
                  region,
                  learnId,
                  isDefault: true
                }
                userDefaultApi(param)
                  .then(() => {
                    //静态缓存默认用户
                    const list = JSON.parse(localStorage.learnUsers)
                    for (const x of list) {
                      x.isDefault = false
                      if (x.learnId == learnId) {
                        x.isDefault = true
                      }
                    }
                    localStorage.learnUsers = JSON.stringify(list)
                    btnLoading.value = false
                  })
                  .catch(() => {
                    btnLoading.value = false
                  })
                //修改为默认用户e
              } else {
                // 编辑状态下，更新当前用户信息
                const updatedUser = arr.find((item : any) => {
                  return item.learnId === usersNow.learnId
                })
                if (updatedUser) {
                  localStorage.learnNow = JSON.stringify(updatedUser)
                  // 更新store中的learnNow
                  useUserStore().setlearnNow(updatedUser)
                }
                btnLoading.value = false
              }
            }
          } else {
            // 没有勾选学生用户，列表页会更新
            arr[0].isDefault = true
            localStorage.learnNow = JSON.stringify(arr[0])
            // 更新store中的learnNow
            useUserStore().setlearnNow(arr[0])
            if (JSON.parse(localStorage.learnUsers)?.length == 1) {
              setTimeout(() => {
                //跳首页
                router.replace({ path: "/ai_percision/knowledge_graph" })
              }, 2000)
            } else {
              router.go(-1)
            }
            btnLoading.value = false
          }
        }
      })
      .catch(() => {
        btnLoading.value = false
      })
  }
</script>

<style lang="scss" scoped>
    .dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
      .el-dialog {
    border-radius: 20px;
    overflow: hidden;
    // box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    border: none;
    // background: linear-gradient(135deg, #ff6b6b 0%, #ff4757 100%);
    position: relative;
  }
  
  .el-dialog__header {
    padding: 0;
    margin: 0;
    background: transparent;
    border-bottom: none;
    height: 100px!important;
  }
  
  .el-dialog__body {
    padding: 0;
    height: 300px!important;
    // background: transparent;
  }
    .dialog-title {
      display: flex;
      align-items: center;
      
      .title-text {
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
      
      .teacher-name {
        font-size: 14px;
        font-weight: 400;
        color: #666;
        margin-left: 8px;
      }
    }
    
    .header-actions {
      display: flex;
      align-items: center;
    }
    
    .close-icon {
      position: absolute;
      right: 16px;
      top: 16px;
      z-index: 1000;
      font-size: 24px;
      color: #999;
      cursor: pointer;
      transition: color 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(4px);
      
      &:hover {
        color: #333;
        background: rgba(255, 255, 255, 1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }
    }
  }
      /* 老师详情弹窗移动端适配 */
    .teacher-detail-dialog {
      .el-dialog {
        width: 95% !important;
        margin: 20px;
      }
    }

    .teacher-iframe {
      height: 500px !important;
    }

    .teacher-loading {
      min-height: 500px;
    }

    .iframe-error {
      min-height: 500px;
    }

  .none {
    display: none !important
  }

  .content {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    background: #F5F5F5;
  }

  .inner {
    margin: 0 auto;
    width: 81.25rem;
  }

  .inner>div {
    float: left
  }

  .header {
    width: 100%;
    background: url(@/assets/img/user/bg.png) #f5f5f5 no-repeat;
    background-size: 120rem 6.5rem;
    height: 6.5rem;
    box-sizing: border-box;
  }

  /* .logo_img {
    float: left;
    width: 5.625rem;
    height: 5.625rem
  }

  .logo_h1 {
    float: left;
    line-height: 3.5rem;
    color: #009c7f;
    font-size: 2.5rem;
    font-weight: 700;
    margin: 2.1875rem .5rem 0 1.25rem
  }

  .logo_h2 {
    float: left;
    line-height: 1.625rem;
    color: #f3943c;
    font-size: 1.25rem;
    font-weight: 700;
    margin: 3.625rem 0 0
  } */

  .hd_back {
    color: #2a2b2a;
    font-size: .875rem;
    line-height: 1.1875rem;
    margin: 4rem 0 .375rem;
    cursor: pointer;
  }

  div.tip {
    line-height: 1.625rem;
    color: #2a2b2a;
    font-size: 1.25rem;
    margin: 4rem 0 0;
    float: right;
  }

  .owl {
    float: right;
    width: 4.5625rem;
    height: 4rem;
    margin: 2.5rem 0 0;
  }

  /* 学生信息 */
  .info {
    float: left;
    width: 81.25rem;
    height: 20.875rem;
    background: url(@/assets/img/user/head.png) no-repeat;
    background-size: 100% 100%;
    position: relative;
  }
  .pos_mid{
    color: #fff;
    position: absolute;
    left: 50%;
    top: 20px;
    font-size: 18px;
    transform: translateX(-50%);
  } 
  .tag {
    float: right;
    width: 11.1875rem;
    height: 4.125rem;
    margin: 0 -0.875rem -4.125rem 0;
  }

  .photo,
  .photo div {
    float: left;
  }

  .photo {
    width: 100%;
    margin: 1.625rem 0 1.625rem;
    box-sizing: border-box;
    padding: 0 0 0 1.625rem;
  }

  .pic {
    float: left;
    width: 3.75rem;
    height: 3.75rem;
    border-radius: 50%;
    margin: 0 1.5625rem 0 0;
    box-sizing: border-box;
  }

  .picsel {
    float: left;
    width: 3.75rem;
    height: 3.75rem;
    margin: 0 0 0 -5.3125rem;
    position: relative;
    z-index: 2;
    transform: scale(1.15);
    display: none;
  }

  .imgs .active .picsel {
    display: inline-block;
  }

  .pic.active {
    border: .125rem solid #00C9A3;
    padding: .125rem;
  }

  .pic:hover {
    cursor: pointer;
  }

  .upload {
    width: 3.75rem;
    height: 3.75rem;
    line-height: 3.75rem;
    background: #fff;
    color: #2a2b2a;
    font-size: .75rem;
    border-radius: 50%;
    text-align: center;
    overflow: hidden;
    margin: 0 1.5625rem 0 0;
    cursor: pointer;
  }

  .upload:hover {
    background: #ccc;
  }

  .file {
    width: 3.75rem;
    height: 3.75rem;
    margin: -3.75rem 0 0;
    position: relative;
    z-index: 2;
    float: left;
    background: red;
    opacity: 0;
    cursor: pointer;
  }

  .lable {
    float: left;
    width: 34.5rem;
    height: 5.0625rem;
    box-sizing: border-box;
    padding: 0 0 0 1.875rem;
    margin: 0 0 1.25rem;
  }

  .labtxt {
    float: left;
    width: 100%;
    line-height: 1.3125rem;
    color: #2a2b2a;
    font-size: 1rem;
    margin: 0 0 .5rem;
  }

  .nickname {
    width: 27.5rem;
    height: 3.3125rem;
    line-height: 3.3125rem;
    box-sizing: border-box;
  }

  .nickname .el-input__inner,
  .nickname :deep(.el-input__inner) {
    width: 27.5rem;
    height: 3.3125rem !important;
    line-height: 3.3125rem !important;
    border-radius: .375rem;
    background: none;
    color: #000;
    font-size: 1rem;
  }

  .item div {
    float: left;
    width: 10rem;
    height: 3.3125rem;
    line-height: 3.3125rem;
    text-align: center;
    border-radius: .375rem;
    background: #f5f5f6;
    color: #2a2b2a;
    font-size: 1rem;
    margin: 0 1.25rem 0 0;
  }

  .item div.active {
    color: #fff;
    background: #00c9a3;
  }

  .item div:hover {
    cursor: pointer;
  }

  /* 设置教材 */
  .books {
    float: left;
    width: 100%;
    min-height: 31.25rem;
    background: #ffffff;
    margin: .625rem 0 0;
  }

  .map {
    float: left;
    margin: 1.875rem 0 1.875rem 1.875rem;
    height: 2.625rem;
    line-height: 2.625rem;
    border-radius: 1.3125rem;
    background: #f5f5f6;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding: 0 1.25rem;
  }

  .map img {
    width: 1rem;
    height: 1.1875rem;
  }

  .mapbox {
    width: 100%;
    float: left;
    box-sizing: border-box;
    padding: 0 0 0 1.875rem;
    margin: -4.4375rem 0 0 0;
  }

  .mapbox :deep(.el-input__wrapper) {
    opacity: 0;
  }

  .elmap {
    float: left;
    height: 2.625rem;
    opacity: 0;
    width: 17.5rem;
  }

  .city {
    color: #2a2b2a;
    font-size: 1rem;
    padding: 0 1.25rem 0 .625rem;
  }

  .area {
    color: #009c7f;
    font-size: 1rem;
    padding: 0 0 0 .625rem;
  }

  .perfect {
    width: 100%;
    float: left;
    margin: 7.375rem 0 7rem;
    color: #999999;
    font-size: .875rem;
    display: flex;
    align-items: center;
    flex-flow: column;
  }

  .perfect img {
    width: 7.4375rem;
    height: 8rem;
  }

  .submit {
    float: left;
    width: 15.25rem;
    height: 3.5625rem;
    line-height: 3.5625rem;
    text-align: center;
    border-radius: 2.1563rem;
    background: #00c9a3;
    box-shadow: 0 .25rem .9375rem 0 #00000040;
    margin: 0 33rem 1.875rem;
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 700;
    position: fixed;
    bottom: 0;
  }

  .submit.disabled {
    background: #cccccc;
    cursor: not-allowed;
    opacity: 0.7;
  }

  .loading-icon {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    margin-right: 0.5rem;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
    vertical-align: middle;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .submit:hover {
    cursor: pointer;
  }

  .subul {
    width: 100%;
    float: left;
    box-sizing: border-box;
    padding: 0 0 6.25rem 1.875rem;
  }

  .subli {
    float: left;
    width: 24.5625rem;
    margin: 0 1.875rem 1.25rem 0;
  }

  .sublab {
    display: flex;
    align-items: center;
    margin: 0 0 .4375rem;
    color: #2a2b2a;
    font-size: 1rem;
  }

  .sublab img {
    width: 1.375rem;
    height: 1.375rem;
    margin: 0 .5rem 0 0;
  }

  .subtxt {
    width: 24.5625rem;
    height: 3.3125rem;
    border-radius: .375rem;
  }

  .select {
    width: 28.875rem;
    height: 3.3125rem;
    border-radius: .375rem;
    background: #f5f5f6;
  }

  :deep(.el-select__wrapper) {
    height: 3.3125rem;
  }

  .subtxt .el-input__inner,
  .select .el-input__inner,
  .subtxt :deep(.el-input__inner),
  .select :deep(.el-input__inner)
  {
  height: 3.3125rem;
  line-height: 3.3125rem;
  background: #f5f5f6;
  font-size: 1rem;
  color: #2A2B2A;
  }

  .subtxt .el-input__inner::-webkit-input-placeholder,
  .select .el-input__inner::-webkit-input-placeholder,
  .subtxt :deep(.el-input__inner::-webkit-input-placeholder),
  .select :deep(.el-input__inner::-webkit-input-placeholder)
  {
  color: #999;
  }

  .el-select-dropdown__item,
  .el-cascader-node__label {
    font-size: .9375rem !important;
    height: 3.0625rem !important;
    line-height: 3.0625rem !important;
  }

  .el-select-dropdown {
    box-shadow: 0 .125rem 1.25rem 0 #00000040 !important;
  }

  .el-icon-arrow-up {
    background: url(@/assets/img/user/up.svg) no-repeat;
    background-position: 40% 50%;
  }

  .el-icon-arrow-up:before {
    content: '' !important;
  }

  /* emlement改样式 */
  .el-select .el-input.is-focus .el-input__inner,
  .el-input .el-input__inner:focus,
  .el-input .el-input__inner:hover {
    border-color: #00C9A3 !important;
  }

  /* select */
  .el-select-dropdown__item.selected {
    color: #00C9A3 !important;
  }

  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover,
  .el-select-dropdown__item.is-hovering {
    color: #00C9A3 !important;
    background: #e5f9f6 !important;
  }

  /* 多级选择 */
  .el-cascader-node.in-active-path,
  .el-cascader-node.is-active,
  .el-cascader-node.is-selectable.in-checked-path {
    color: #00C9A3 !important;
  }

  :deep(.el-input__wrapper) {
    box-shadow: 0 0 0 .0625rem rgba(0, 0, 0, 0) !important;
  }

  :deep(.el-select__wrapper.is-focused),
  :deep(.el-input__wrapper.is-focus) {
    box-shadow: 0 0 0 .0625rem #00C9A3 !important;
  }

  /* 禁用 */
  .disabled .item div,
  .disabled .map .area {
    pointer-events: none;
  }

  // 二级选择
  :deep(.subtxt2) {
    width: 393px;
    height: 53px;
    line-height: 53px;

    .el-input__wrapper {
      height: 53px;
      line-height: 53px;
      box-shadow: 0 0 0 1px #dcdfe6 inset !important;
    }
  }

  /* 自定义loading样式 */
  :deep(.el-loading-mask) {
    background-color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(3px);
    
    .el-loading-spinner {
      animation: fadeInUp 0.5s ease-out;
      
      .circular {
        width: 60px;
        height: 60px;
        animation: pulse 2s infinite;
      }
      
      .path {
        stroke: #00C9A3;
        stroke-width: 3px;
      }
      
      .el-loading-text {
        color: #00C9A3;
        font-size: 16px;
        font-weight: bold;
        margin-top: 12px;
        animation: fadeIn 1s ease-in;
      }
    }
  }
  
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes pulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
    100% {
      transform: scale(1);
    }
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
</style>
