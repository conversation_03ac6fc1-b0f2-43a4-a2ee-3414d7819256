@charset "utf-8";
a,button,div,input,label,li,select,span,textarea,ul{border-radius:0;-webkit-appearance:none;-webkit-tap-highlight-color:transparent;-webkit-tap-highlight-color:transparent;border:0}
body,html{color:#000;background:#fff;-webkit-text-size-adjust:100%!important}
article,aside,blockquote,body,button,code,dd,details,div,dl,dt,fieldset,figcaption,figure,footer,form,h1,h2,h3,h4,h5,h6,header,hgroup,hr,input,legend,li,menu,nav,ol,p,pre,section,td,textarea,th,ul{margin:0;padding:0}
article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section{display:block}
audio,canvas,video{display:inline-block}
body,button,input,select,textarea{font:12px/1.5 tahoma,arial,\5b8b\4f53}
input,select,textarea{font-size:100%}
table{border-collapse:collapse;border-spacing:0}
th{text-align:inherit}
fieldset,img{border:0}
iframe{display:block}
abbr,acronym{border:0;font-variant:normal}
del{text-decoration:line-through}
address,caption,cite,code,dfn,em,th,var{font-style:normal;font-weight:500}
ol,ul{list-style:none}
caption,th{text-align:left}
h1,h2,h3,h4,h5,h6{font-size:100%;font-weight:400}
q:after,q:before{content:''}
sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}
sup{top:-.5em}
sub{bottom:-.25em}
a:hover{text-decoration:underline}
a,ins{text-decoration:none}
input[type=button],input[type=reset],input[type=submit]{-webkit-appearance:none}
a:hover{text-decoration:none}
a:active{color:#000}
a{color:#000}
textarea{resize:none}
button,input,label,option,select,textarea:focus{outline:0}
i{font-style:normal}
body,html{line-height:1;-webkit-overflow-scrolling:touch}
iframe{border:0}
input[disabled]{opacity:1}
body,html {font-family: "微软雅黑", miui, system-ui, -apple-system, BlinkMacSystemFont, Helvetica Neue, Helvetica, sans-serif;}

/* emlement改样式 */
.el-select .el-input.is-focus .el-input__inner,
.el-input .el-input__inner:focus,
.el-input .el-input__inner:hover{
  border-color: #00C9A3 !important;
}
/* select */
.el-select-dropdown__item.selected{
  color: #00C9A3 !important;
}

.el-select-dropdown__item.hover, .el-select-dropdown__item:hover,.el-select-dropdown__item.is-hovering{
  color: #00C9A3 !important;
  background: #e5f9f6 !important;
}

/* 多级选择 */
.el-cascader-node.in-active-path, .el-cascader-node.is-active, .el-cascader-node.is-selectable.in-checked-path{
  color: #00C9A3 !important;
}

/* 返回 */
.back_h1 {
  float: left;
  width: 100%;
  height: 49px;
  box-sizing: border-box;
  padding: 20px 0 0;
}

.back_h1 div {
  float: left;
  line-height: 19px;
  color: #666666;
  font-size: 14px;
}

.back_img {
  margin: 0 30px 0 0;
}

.back_img:hover {
  cursor: pointer;
}

.back_arrow{
  margin: 0 5px;
}

/* 参考菜单 */
.header_box{
  width: 100%;
  float: left;
  height: 70px;
  background: #fff;
}

.header_menu{
  width: 100%;
  height: 70px;
  position: fixed;top: 0;left: 0;
  z-index: 90;
  background: #fff;
}

/* 底部加载动画 */
.pg_load {
  width: 100%;
  float: left;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pg_tip {
  font-size: 16px;
  color: #999;
  line-height: 30px;
}

.pg_gif {
  width: 23px;
  height: 23px;
  -webkit-animation: pgroll .6s linear infinite;
  margin: 0 8px 0 0;
}

@-webkit-keyframes pgroll {
  0% {
    transform: rotate(0deg)
  }

  100% {
    transform: rotate(360deg)
  }
}