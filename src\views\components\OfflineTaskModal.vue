<template>
  <div v-if="visible" class="modal-overlay" @click.self="closeModal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>线下作业</h3>
        <button class="close-button" @click="closeModal">&times;</button>
      </div>
      <div class="modal-body">
        <div class="task-meta">
          <span class="teacher-name">王老师</span>
          <span class="publish-time">布置时间: {{ taskData?.time }}</span>
        </div>
        <div class="teacher-message">
          <p><strong>老师说了：</strong></p>
          <p>{{ taskData?.message || '今日作业没有，都回去好好玩，放开了玩，打游戏，谁敢学习明天到班里老师弄死谁！' }}</p>
        </div>
      </div>
      <div class="modal-footer">
        <span v-if="taskData?.deadline" class="deadline-text">截止时间: {{ taskData.deadline }}</span>
        <button class="confirm-button" @click="closeModal">我知道了</button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineProps, defineEmits } from 'vue';

interface TaskData {
  time: string;
  message?: string;
  deadline?: string;
  // Add other necessary properties from the task object
}

const props = defineProps<{
  visible: boolean;
  taskData: TaskData | null;
}>();

const emit = defineEmits(['close']);

const closeModal = () => {
  emit('close');
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: #fff;
  padding: 25px 30px;
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  width: 600px; /* Adjust width as needed */
  max-width: 90%;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
  margin-bottom: 20px;
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.close-button {
  background: none;
  border: none;
  font-size: 28px;
  cursor: pointer;
  color: #888;
  padding: 0;
  line-height: 1;
}
.close-button:hover {
  color: #555;
}

.modal-body {
  margin-bottom: 25px;
  font-size: 14px;
  color: #555;
}

.task-meta {
  background-color: #fffbe6; /* Light yellow background */
  border: 1px solid #ffe58f; /* Yellow border */
  border-radius: 4px;
  padding: 10px 15px;
  margin-bottom: 20px;
  display: flex;
  gap: 20px;
  font-size: 13px;
}
.task-meta .teacher-name {
  color: #e07509; /* Orange for teacher name, adjust as per image */
  font-weight: 500;
}

.task-meta .publish-time {
  color: #888;
}

.teacher-message {
  line-height: 1.7;
}
.teacher-message p {
  margin: 0 0 10px 0;
}
.teacher-message strong {
  color: #333;
  font-weight: 600;
}


.modal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.deadline-text {
  font-size: 13px;
  color: #e53935; /* Red color for deadline */
}

.confirm-button {
  background-color: #13c2c2; /* Teal button */
  color: white;
  border: none;
  padding: 10px 25px;
  border-radius: 20px; /* Pill shape */
  cursor: pointer;
  font-size: 15px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.confirm-button:hover {
  background-color: #0fa3a3; /* Darker teal on hover */
}
</style> 