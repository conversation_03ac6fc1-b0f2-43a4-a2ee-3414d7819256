<!-- 名师课堂-知识点视频（全屏）-收藏进入 -->
<template>
  <div class="content">
    <div class="inner">
      <div class="wrap">
        <div class="menu_lt" style="width: 100%;">
          <div class="wk_box">
            <div class="wk_tit">
              <div class="wk_p" v-html="ReplaceMathString(state.vidInfo.videoTag0||state.vidInfo.videoTag)"></div>
              <!-- <div class="wk_status status2" v-if="state.vidInfo.studyStatus==2">已学完</div>
              <div class="wk_status status1" v-else-if="state.vidInfo.studyStatus==1">未学完</div>
              <div class="wk_status status0" v-else>未学习</div> -->
            </div>
            <!-- 播放器 -->
            <div id="dplayer1" class="wk_video" v-show="state.videoUrl|| state.videoList.length"></div>
            <div class="wk_nodata" v-show="!(state.videoUrl|| state.videoList.length)&&state.isShow">
              <img src="@/assets/img/teachroom/nopoint.png" />暂无知识点视频
            </div>
            <div class="wk_opt" style="border:0">
              <div class="wk_collect" :class="state.vidInfo.userCollect?'active':''" @click="setCollect">
                <img src="@/assets/img/teachroom/collect.svg" />
                <img src="@/assets/img/teachroom/collectsel.svg" />
              </div>
              <div class="wk_thumbs" :class="state.vidInfo.userLike?'active':''" @click="setThumbs">
                <img src="@/assets/img/teachroom/thumbs.svg" />
                <img src="@/assets/img/teachroom/thumbs2sel.svg" />
                <!-- {{state.vidInfo.likeNum||0}} -->
              </div>
            </div>
          </div>
        </div>
        <div class="menu_rt none">
          <div class="now_box" style="border: 0;">
            <div class="now_air">
              <img src="@/assets/img/teachroom/playing2.gif" />正在播放
            </div>
            <div class="now_img">
              <img :src="state.vidInfo.cover" style="transform: scale(1.1);" v-if="state.vidInfo.cover" />
              <img src="@/assets/img/teachroom/novid.png" v-else />
              <!-- <div class="now_status status2" v-if="state.vidInfo.studyStatus==2">已学完</div>
              <div class="now_status status1" v-else-if="state.vidInfo.studyStatus==1">未学完</div>
              <div class="now_status status0" v-else>未学习</div> -->
            </div>
            <div class="now_name" v-html="ReplaceMathString(state.vidInfo.videoTag0||state.vidInfo.videoTag)"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 购买会员弹窗 -->
  <buyVip :show="state.showVip" @close="quitHide"></buyVip>
  <!-- 积分弹窗 -->
  <coinAlert :show="state.jfShow" :hide="state.jfHide" :num="state.jfNum" :source="state.jfSource" @close="jfHide">
  </coinAlert>
</template>

<script lang="ts" setup>
  import { reactive, onMounted, watch } from 'vue';
  import { ElMessage } from "element-plus"
  import { useUserStore } from "@/store/modules/user"
  import { subjectList } from '@/utils/user/enum'
  import { ReplaceMathString } from '@/utils/user/util'
  import { setStudyTimeApi, analyseVideoNumApi } from "@/api/user"
  import { getVideoUrlApi, videoModifyApi, getVideoReviewNumApi, setUserVideoViewNumberApi } from "@/api/video"
  import { useRoute } from "vue-router"
  import buyVip from "@/views/components/buyVip/index.vue"
  import coinAlert from "@/views/components/coinAlert/index.vue"
  import { setLearnKey } from '@/utils/user/learntime'

  defineOptions({
    name: "TeachRoomTeachVideo2"
  })

  const route = useRoute()
  const query = reactive<any>(route.query)

  const state : any = reactive({
    showVip: false,
    dp: null,
    chapterId: '',
    subActive: '',
    vidInfo: {},
    isFirst: 1,
    isShow: false,
    data: [],
    poster: '',
    videoId: '',
    videoUrl: '',
    videoList: [], //视频
    videoIndex: 0,
    autoplay: false,
    subject: '',
    dataType: 0, //0带概述 1例题
    dataI: 0,
    dataI2: 0,
    dataI3: 0,
    isOne: '', //是否第一个视频
    vidType: '',
    //视频组件
    video: '',
    controls: false,
    videoBtn: true,
    playstate: 0, //0暂停，1播放
    speed: '1.0', //速度
    speedArr: ['0.5', '0.75', '1.0', '1.25', '1.5', '2.0'],
    rate: 0, //显示倍速
    playbtn: true,
    title: '',
    isVidMenu: 0,
    //进度条
    showSlider: true,
    showComp: 0,
    updateState: false,
    slider: 0,
    curtime: 0,
    nowtime: '00:00', // 当前时间
    endtime: '00:00', // 总时长
    duration: '', // 视频长度秒
    isFull: false,
    //积分
    jfShow: false,
    jfHide: true,
    jfNum: '0',
    jfSource: '0'
  })

  onMounted(() => {
    init()
  })

  //监听路由参数
  watch(
    () => route.query,
    (newQ) => {
      let vid = newQ.vid
      if (vid && route.name == "TeachRoomTeachVideo2") {
        init()
      }
    }
  )

  // 显示积分-看视频
  const setJfShow2 = () => {
    state.jfShow = true
    state.jfHide = true
    state.jfNum = '5'
    state.jfSource = '2'
  }

  // 隐藏积分
  const jfHide = () => {
    state.jfShow = false
  }

  // 属性赋值
  const setData = (obj : any) => {
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        state[key] = obj[key]
      }
    }
  }

  const quitHide = () => {
    state.showVip = false
    if (useUserStore().memberInfo) {
      init()
    }
  }

  const init = () => {
    //跳转第0秒
    state.dp?.seek(0)
    if (query.vid) {
      let { id, vid, subKey } = route.query
      let info = JSON.parse(localStorage.teachVidInfo || '[]')
      info.userLike = info.isLike
      info.userCollect = info.userCollect === false ? false : true
      setData({
        chapterId: id,
        subActive: subKey,
        videoId: vid,
        subject: subjectList[subKey].key,
        vidInfo: info,
        vidType: info.type
      })
      getVideoUrl()
      //记录学习学科
      setLearnKey(state.subject)
    }
  }

  //判断视频播放次数
  const getVideoUrl = () => {
    if (useUserStore().memberInfo) {
      getVidSrc()
    } else {
      getVideoReviewNumApi().then((res : any) => {
        const num = res.data
        if (num < 2) {
          ElMessage.success(`剩余免费观看次数：${2 - num - 1}`)
          getVidSrc()
        } else {
          setData({
            isShow: true
          })
          //开通会员弹窗
          state.showVip = true
        }
      })
    }
  }
  //获取知识点视频url
  const getVidSrc = () => {
    const param = {
      type: state.vidType, //1优学派 2菁优网
      videoId: state.videoId
    }
    getVideoUrlApi(param)
      .then((res : any) => {
        let data = res.data || ''
        setData({
          isShow: true,
          videoUrl: data,
          videoList: [data]
        })
        if (state.isFirst) {
          //初始化视频控件
          initPlayers()
        } else {
          switchVideo()
        }
        setData({
          isFirst: 0
        })
        if (data) {
          //先注释
          videoPause()
        }
        setVideo()
        videoScoreSave()
        setStudyState(1)
      })
      .catch(() => {
        setData({
          isShow: true
        })
      })
  }
  //设置视频信息,默认第1个
  const setVideo = () => {
    const videos = state.videoList
    if (videos.length) {
      setData({
        videoUrl: videos[0],
        autoplay: true
      })
      //先注释
      videoPlay()
    } else {
      setData({
        videoUrl: '',
        autoplay: false
      })
    }
  }

  //记录视频播放次数
  const setUserVideoViewNumber = () => {
    const { chapterId, pointId, videoId, subject } = state
    const param = {
      time: 0,
      videoId,
      type: state.vidType, //1优学派 2菁优网
      subject,
      pointId,
      viewType: true, //true:观看次数+1；false:增加观看次数
      chapterId: ''
    }
    if (chapterId && chapterId != 'undefined') {
      param.chapterId = chapterId
    }
    if (!videoId) {
      return
    }
    setUserVideoViewNumberApi(param)
  }
  // 记录每日查看视频数量
  const analyseVideoNum = () => {
    const { subject } = state
    analyseVideoNumApi({ subject })
  }
  //记录视频播放状态
  const videoScoreSave = () => {
    analyseVideoNum()
    setUserVideoViewNumber()
  }
  // 设置学习状态-收藏接口没status
  const setStudyState = (status : any) => {
    const { chapterId, pointId, videoId, subject, data, dataI, dataI2, dataI3, dataType, title, vidType,vidInfo } = state
    const param = {
      chapterId,
      pointId,
      videoId,
      type: vidType, //1优学派 2菁优网
      subject,
      source: vidType, //1同步章节 2知识点
      knowledgeName: title,
      status //0未学习 1正在学 2已学完
    }
    if (status == 2) {
      videoModifyApi(param)
    }
    //改变学习状态
    vidInfo.studyStatus = status
    setData({
      vidInfo
    })
  }

  // 点赞
  const setThumbs = () => {
    const { chapterId, pointId, videoId, subject, data, dataType, title, vidType } = state
    const param : any = {
      chapterId,
      pointId,
      videoId,
      type: vidType, //1优学派 2菁优网
      subject,
      source: vidType, //1同步章节 2知识点
      knowledgeName: title
    }
    const isThumbs = state.vidInfo.userLike ? 0 : 1
    if (isThumbs) {
      param.like = 1
      videoModifyApi(param)
        .then(() => {
          ElMessage.success('点赞成功')
          if (dataType == 0) {
            //概述
            state.vidInfo.isLike = true
            state.vidInfo.userLike = true
          } else {
            //例题
            state.vidInfo.isLike = true
            state.vidInfo.userLike = true
          }
          setData({
            data
          })
          //缓存视频信息
          localStorage.teachVidInfo = JSON.stringify(state.vidInfo)
        })
    } else {
      param.like = 0
      videoModifyApi(param)
        .then(() => {
          ElMessage.success('已取消点赞')
          if (dataType == 0) {
            //概述
            state.vidInfo.isLike = false
            state.vidInfo.userLike = false
          } else {
            //例题
            state.vidInfo.isLike = false
            state.vidInfo.userLike = false

          }
          setData({
            data
          })
          //缓存视频信息
          localStorage.teachVidInfo = JSON.stringify(state.vidInfo)
        })
    }
  }
  // 收藏
  const setCollect = () => {
    const { chapterId, pointId, videoId, subject, data, dataType, vidType } = state
    const param : any = {
      chapterId,
      pointId,
      videoId,
      type: vidType, //1优学派 2菁优网
      subject,
      source: vidType //1同步章节 2知识点
    }
    const isCollect = state.vidInfo.userCollect ? 0 : 1
    if (isCollect) {
      param.collect = 1
      videoModifyApi(param)
        .then(() => {
          ElMessage.success('收藏成功')
          if (dataType == 0) {
            //概述
            state.vidInfo.userCollect = true
          } else {
            //例题
            state.vidInfo.userCollect = true
          }
          setData({
            data
          })
          //缓存视频信息
          localStorage.teachVidInfo = JSON.stringify(state.vidInfo)
        })
    } else {
      param.collect = 0
      videoModifyApi(param)
        .then(() => {
          ElMessage.success('已取消收藏')
          if (dataType == 0) {
            //概述
            state.vidInfo.userCollect = false
          } else {
            //例题
            state.vidInfo.userCollect = false
          }
          setData({
            data
          })
          //缓存视频信息
          localStorage.teachVidInfo = JSON.stringify(state.vidInfo)
        })
    }
    localStorage.isLoad = 1
  }

  //组件-播放
  const videoPlay = () => {
    if (state.videoUrl) {
      // state.dp.play()
      //自动播放-延迟且用户点击
      setTimeout(()=>{
        let video:any=document.getElementById('video')
        video.play()
      },100)
      setData({
        videoBtn: false
      })
    } else {
      ElMessage.error('请先选择一个视频')
    }
  }

  //组件-暂停
  const videoPause = () => {
    state.dp.pause()
    setData({
      videoBtn: true
    })
  }

  function initPlayers() {
    let { videoName3, videoName, type, cover } = state.vidInfo
    let window1 : any = window
    let DPlayer : any = window1?.DPlayer
    state.dp = new DPlayer({
      container: document.getElementById('dplayer1'),
      autoplay: true, //没效果
      theme: '#1DDFAC', //进度条、音量颜色
      preload: 'auto',
      volume: 1,
      contextmenu: [],
      video: {
        title: videoName3 || videoName,
        url: state.videoUrl,
        pic: cover,
        type: type == 1 ? 'hls' : 'normal'
      }
    });

    state.dp.on('loadstart', function () {
      showPlayIcon()
      // 修改设置为倍速弹窗
      let setbtn : any = document.querySelector('.dplayer-setting-icon')
      setbtn.addEventListener('mouseenter', function () {
        if (document.querySelectorAll('.dplayer-hide-controller').length) {
          // 底部菜单隐藏时不显示
          return
        }
        //显示倍速弹窗
        let setting : any = document.querySelector('.dplayer-setting-box')
        setting.className =
          'dplayer-setting-box dplayer-setting-box-narrow dplayer-setting-box-speed dplayer-setting-box-open'
      })
      //倍速弹窗hover隐藏
      let setting : any = document.querySelector('.dplayer-setting-box')
      setting.addEventListener('mouseleave', function () {
        setting.className = 'dplayer-setting-box'
      })
      setting.addEventListener('click', function () {
        setting.className = 'dplayer-setting-box'
      })

      //隐藏视频标题
      let vidClass : any = document.querySelector('.dplayer-video-current')
      vidClass.addEventListener('mouseleave', function () {
        if (document.querySelectorAll('.dplayer-paused').length) {
          // 暂停时不隐藏
          return
        }
        let title : any = document.getElementById('fulltit')
        title.style.display = 'none'
      })
    })

    //设置倍速文字
    state.dp.on('ratechange', function () {
      speedChange('')
    });

    state.dp.on('ended', function () {
      videoPause()
      setStudyState(2)
      setJfShow2()
    });
    state.dp.on('pause', function () {
      showPlayIcon()
    });
    state.dp.on('play', function () {
      hidePlayIcon()
    });

    state.dp.on('volumechange', function () {
    })
  }

  fullScreenListeners()
  //监听全屏
  function fullScreenListeners() {
    document.addEventListener('fullscreenchange', function () {
      let fullIcon : any = document.querySelector('#fullIcon')
      if (document.fullscreenElement) {
        fullIcon.setAttribute('data-title', '退出全屏')
      } else {
        fullIcon.setAttribute('data-title', '全屏')
      }
    })
  }

  //显示播放图标
  function showPlayIcon() {
    let icon : any = document.querySelector('.dplayer-bezel-icon')
    if(icon){
      icon.className = 'dplayer-bezel-icon play'
    }
  }

  //隐藏播放图标
  function hidePlayIcon() {
    setTimeout(() => {
      let icon : any = document.querySelector('.dplayer-bezel-icon')
      if(icon){
        icon.className = 'dplayer-bezel-icon'
      }
    }, 0)
  }

  //切换视频
  function switchVideo() {
    let { videoName3, videoName, type, cover } = state.vidInfo
    let obj = {
      title: videoName3 || videoName,
      url: state.videoUrl,
      pic: cover,
      type: type == 1 ? 'hls' : 'normal'
    }
    let src = state.dp.video.src
    if (src == obj.url) {
      return
    }
    state.dp.switchVideo(obj);
    speedChange(1)
    //自动播放-必须延迟且是用户点击
    setTimeout(()=>{
      let video:any=document.getElementById('video')
      video.play()
    },100)
  }
  //倍速切换
  function speedChange(num : any) {
    let video : any = document.getElementById('video')
    let speed = num || video.playbackRate
    let sptxt : any = document.querySelector('.dplayer-icon.dplayer-setting-icon')
    if (speed == 1) {
      sptxt.innerHTML = '倍速'
    } else if (speed == 2) {
      sptxt.innerHTML = '2.0X'
    } else {
      sptxt.innerHTML = speed + 'X'
    }
    //倍速弹窗选中变色
    let list : any = document.querySelectorAll('.dplayer-setting-speed-item')
    for (let i = 0; i < list.length; i++) {
      let num = Number(list[i].attributes['data-speed'].value)
      if (num == speed) {
        list[i].className = 'dplayer-setting-speed-item green'
      } else {
        list[i].className = 'dplayer-setting-speed-item'
      }
    }
  }
    //视频控件e
</script>

<style lang="scss" scoped>
  @import url('@/assets/styles/reset.css');

  .header_seat {
    width: 100%;
    height: 4.375rem;
    float: left;
  }

  .none {
    display: none !important
  }

  .nowrap {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .nowrap2 {
    width: 100%;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    word-break: break-all;
    -webkit-line-clamp: 2;
  }

  .content {
    width: 100%;
    background: #F5F5F5;
    overflow-y: auto;
  }

  .inner {
    margin: 0 auto;
    width: 81.25rem;
  }

  .wrap {
    float: left;
    width: 100%;
    /* height: calc(100vh - 7.4375rem);
    overflow-y: auto; */
  }

  .wrap>div {
    /* height: calc(100vh - 7.4375rem);
    overflow-y: auto; */
    border: .0625rem solid #eaeaea;
    box-sizing: border-box;
    background: #fff;
  }

  /* 视频 */
  .menu_lt {
    float: left;
    width: calc(100% - 21.125rem - .625rem);
    padding: .625rem;
  }

  .wk_box {
    width: 100%;
  }

  .wk_box div {
    float: left;
  }

  .wk_tit {
    width: 100%;
  }

  .wk_p {
    line-height: 1.375rem;
    color: #2a2b2a;
    font-size: 1rem;
    margin: 0 .625rem 0 0;
  }

  .wk_status {
    width: 2.875rem;
    line-height: 1.375rem;
    border-radius: .25rem;
    text-align: center;
    font-size: .75rem;
  }

  .wk_status.status0 {
    color: #fff;
    background: #999;
  }

  .wk_status.status1 {
    color: #EF9D19;
    background: #FEF8E9;
  }

  .wk_status.status2 {
    color: #009C7F;
    background: #E5F9F6;
  }

  .wk_video {
    width: 100%;
    height: 41.8125rem;
    background: #000;
    margin: 1.25rem 0 0;
  }

  .wk_nodata {
    width: 100%;
    height: 41.8125rem;
    background: #000;
    margin: 1.25rem 0 0;
    color: #999999;
    font-size: .875rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-flow: column;
  }

  .wk_nodata img {
    width: 7.4375rem;
    height: 8rem;
    margin: 0 0 .625rem;
  }

  /* 点赞收藏 */
  .wk_opt {
    width: 100%;
    height: 4rem;
    box-sizing: border-box;
    padding: 1.25rem .625rem;
    border-bottom: .0625rem solid #eaeaea;
  }

  .wk_collect {
    margin: 0 3.125rem 0 0;
    cursor: pointer;
  }

  .wk_collect img {
    width: 1.5rem;
    height: 1.5rem;
  }

  .wk_collect img:first-child,
  .wk_collect.active img:last-child {
    display: inline-block;
  }

  .wk_collect img:last-child,
  .wk_collect.active img:first-child {
    display: none;
  }

  .wk_thumbs {
    line-height: 1.5rem;
    color: #666666;
    font-size: .875rem;
    float: right;
    cursor: pointer;
  }

  .wk_thumbs img {
    float: left;
    width: 1.5rem;
    height: 1.5rem;
    margin: 0 .375rem 0 0;
  }

  .wk_thumbs img:nth-child(1),
  .wk_thumbs.active img:nth-child(2) {
    display: inline-block;
  }

  .wk_thumbs img:nth-child(2),
  .wk_thumbs.active img:nth-child(1) {
    display: none;
  }

  /* 知识点 */
  .iframe {
    border: 0;
    width: 100%;
    height: 100vh;
  }

  .wkinfo {
    float: left;
    width: 100%;
    color: #2a2b2a;
    font-size: .875rem;
    font-weight: 400;
    line-height: 1.625rem;
    box-sizing: border-box;
    padding: .625rem 1.25rem;
  }

  /* 正在播放 */
  .menu_rt {
    float: right;
    width: 21.125rem;
    border: .0625rem solid #eaeaea;
    background: #ffffff;
  }

  .menu_rt div {
    float: left;
  }

  .now_box {
    width: 100%;
    border-bottom: .0625rem solid #eaeaea;
  }

  .now_air {
    width: 100%;
    line-height: 1rem;
    color: #5a85ec;
    font-size: 1rem;
    margin: .8125rem 0 1.25rem;
  }

  .now_air img {
    float: left;
    width: 1rem;
    height: 1rem;
    margin: 0 .375rem 0 .625rem;
  }

  .now_img {
    width: 19.875rem;
    height: 11.1875rem;
    border-radius: .25rem;
    border: .0625rem solid #5a85ec;
    box-sizing: border-box;
    overflow: hidden;
    margin: 0 .625rem;
  }

  .now_img img {
    float: left;
    width: 19.875rem;
    height: 11.1875rem;
  }

  .now_name {
    width: 100%;
    line-height: 1.1875rem;
    color: #5a85ec;
    font-size: .875rem;
    box-sizing: border-box;
    padding: .375rem .625rem 1.25rem;
  }

  div.now_status {
    float: right;
    width: 3.75rem;
    line-height: 1.5rem;
    border-radius: 0 0 0 .25rem;
    text-align: center;
    font-size: .75rem;
    color: #fff;
    position: relative;
    z-index: 2;
    margin: -11.1875rem 0 0;
  }

  .now_status.status0 {
    background: #999;
  }

  .now_status.status1 {
    background: linear-gradient(150.8deg, #f6d22b 0%, #f29500 100%);
  }

  .now_status.status2 {
    background: linear-gradient(166.7deg, #08d8b8 0%, #00b392 100%);
  }

  /* 列表 */
  .vid_h2 {
    width: calc(100% - 1.25rem);
    line-height: 1.1875rem;
    border-radius: .25rem;
    background: #f5f5f5;
    color: #2a2b2a;
    font-size: .875rem;
    box-sizing: border-box;
    padding: .5625rem .625rem;
    margin: .625rem 0 0 .625rem;
  }

  .vid_ul {
    width: 100%;
    box-sizing: border-box;
    padding: .625rem;
  }

  .vid_li {
    width: 100%;
    height: 5.0625rem;
    overflow: hidden;
    margin: 0 0 .625rem;
    cursor: pointer;
  }

  .vid_li:last-child {
    margin: 0;
  }

  .vid_img,
  .vid_img img {
    float: left;
    width: 9rem;
    height: 5.0625rem;
    border-radius: .25rem;
  }

  .vid_rt {
    width: 10.375rem;
    margin: 0 0 0 .375rem;
  }

  .vid_name {
    width: 10.5rem;
    max-height: 4rem;
    line-height: 1.5;
    color: #2a2b2a;
    font-size: .75rem;
  }

  .vid_li .vid_name {
    color: #5A85EC;
  }

  .vid_state {
    width: 100%;
    margin: 1rem 0 0;
  }

  .vid_status {
    width: 2.875rem;
    line-height: 1.3125rem;
    border-radius: .25rem;
    text-align: center;
    color: #ffffff;
    font-size: .75rem;
  }

  .vid_status.status0 {
    color: #fff;
    background: #999;
  }

  .vid_status.status1 {
    color: #EF9D19;
    background: #FEF8E9;
  }

  .vid_status.status2 {
    color: #009C7F;
    background: #E5F9F6;
  }

  .vid_h1 {
    line-height: 1.3125rem;
    color: #999999;
    font-size: .75rem;
    margin: 0 .3125rem 0 .625rem;
  }

  div.vid_thumbs {
    float: right;
    line-height: 1.125rem;
    color: #666666;
    font-size: .75rem;
    float: right;
  }

  .vid_thumbs img {
    float: left;
    width: 1.125rem;
    height: 1.125rem;
    margin: 0 .375rem 0 0;
  }

  .vid_thumbs img:nth-child(1),
  .vid_thumbs.active img:nth-child(2) {
    display: inline-block;
  }

  .vid_thumbs img:nth-child(2),
  .vid_thumbs.active img:nth-child(1) {
    display: none;
  }

  .vid_tit:hover,
  .vid_thumbs img:hover {
    cursor: pointer;
  }
</style>
