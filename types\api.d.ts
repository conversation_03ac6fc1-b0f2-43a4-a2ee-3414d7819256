/**
 * 该文件用于维护所有接口返回的字段类型定义
 */
/** 所有 api 接口的响应数据都应该准守该格式 */
interface IApiResponseData<T> {
  code: number
  data: T
  msg: string
}
interface IPageInfos {
  current?: number // 当前第几页
  size?: number // 每页多少条数据
  pages?: number // 一共有多少页
  total?: number // 一共多少条数据
}

/**
 * 接口名称：查询所有学段学科
 * api: /web/book/getSectionSubject
 */
interface ISubjectInfos {
  name: string
  desc: string
}
interface ISectionInfo {
  sectionName: string // 学段名称
  subjectInfos: ISubjectInfos[] // 学段学科信息
}
/**
 * 接口名称：试题篮详细信息-顶部和列表
 * api: /web/ques/basketDetail
 */
interface ITestInfos {
  quesId: number
  content: string
}
interface ITestTypeInfos {
  cateId: number
  cateName: string
  quesItemVoList: ITestInfos[] // 试题列表
}
interface IBasketInfo {
  quesCount: number // 试题数量
  degree: number // 平均难度
  quesItemTreeVos: ITestTypeInfos[] // 题型列表
}
/**
 * 接口名称：根据学科查询教材版本
 * api: /web/book/version
 */
interface IGredeInfo {
  bookId: number
  bookName: string
  gradeId: number
  termId: number
  typeId: number
  academic: number
  gradeName: string // 年级名称
  termName: string
  typeName: string
}
interface IVersionInfo {
  editionId: number
  editionName: string
  gradeInfos: IGredeInfo[] // 教材版本信息
}
/**
 * 树形结构
 */
interface Tree {
  id?: string
  gradeId?: number
  label: string
  value: number | string
  children?: Tree[]
}

//收藏试题类型
interface ICollection {
  id: number
  collectName: string
  cate: number
}
//收藏试卷类型
interface ICollectionReport {
  userCollectReportTypeId: number
  collectName: string
  cate: number
}

//地区类型
interface IArea {
  id: number
  name: string
  children?: IArea[]
}
