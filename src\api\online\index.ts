import { request } from "@/utils/axios"
const SDKPORT = "/api/xiaoyeoo/sdkapi/online/assignments"

/** 提交作业 */
export function submitAssignmentApi(data: Object) {
  return request({
    url: `${SDKPORT}/submitAssignment`,
    method: "POST",
    data
  })
}
/** 试卷详情 */
export function getReportGetApi(params: Object) {
  return request({
    url: `${SDKPORT}/reportGet`,
    method: "GET",
    params
  })
}
/** 下载试卷 */
export function downloadReportApi(params: Object) {
  return request({
    url: `${SDKPORT}/downloadReport`,
    method: "GET",
    params
  })
}
/** 我的试卷列表 */
export function getLearnReportListApi(data: Object) {
  return request({
    url: `${SDKPORT}/getLearnReportList`,
    method: "POST",
    data
  })
}
/** 删除我的试卷 */
export function cancelCollectApi(data: Object) {
  return request({
    url: `${SDKPORT}/cancelCollect`,
    method: "POST",
    data
  })
}
/** 加入我的试卷 */
export function collectReportApi(data: Object) {
  return request({
    url: `${SDKPORT}/collectReport`,
    method: "POST",
    data
  })
}
/** 学生批改作业 */
export function correctAssignmentApi(data: Object) {
  return request({
    url: `${SDKPORT}/correct/assignment`,
    method: "POST",
    data
  })
}

// 组卷试题
export function paperQuesApi(params: Object) {
  return request({
    url: `${SDKPORT}/paperQues`,
    method: "GET",
    params
  })
}
// 组卷答题
export function submitAssignment(data: Object) {
  return request({
    url: `${SDKPORT}/submitAssignment`,
    method: "POST",
    data
  })
}
// 报告汇总
export function assignmentsReportApi(params: Object) {
  return request({
    url: `${SDKPORT}/report`,
    method: "GET",
    params
  })
}

// 作业列表  
export function assignmentsListApi(params: Object) {
  return request({
    url: `${SDKPORT}/list`,
    method: "GET",
    params
  })
}
