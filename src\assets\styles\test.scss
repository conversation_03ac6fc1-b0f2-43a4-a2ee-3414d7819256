/*试题*/
.artpreview fieldset {
  padding-top: 10px;
  font-size: 14px;
  clear: both;
  overflow: hidden;
  zoom: 1;
  line-height: 24px;
  font-family: "微软雅黑";
  position: relative;
}
.artpreview fieldset legend {
  padding: 5px 0;
  display: block;
  margin: 5px;
  background: #f1f1f1;
  color: #000;
  overflow: hidden;
  zoom: 1;
}
.queserror {
  border: 1px dotted #f00;
  padding: 2px;
}
fieldset.quesborder {
  font-size: 13px;
  display: block;
  padding: 0; /* line-height: 25px; */
  letter-spacing: 1px;
  word-break: break-all;
  border-radius: 10px 10px 0 0;
}
fieldset.queserror {
  border: 1px solid #f00;
  font-size: 12px;
  padding: 2px;
  margin-bottom: 1px;
}
fieldset.quesborder em,
fieldset.queserror em {
  font-style: normal;
  font-weight: bold;
  position: absolute;
  left: 20px;
}
fieldset[s^="english"] .pt1,
fieldset[s^="english"] .pt2,
fieldset[s^="english"] .pt3,
fieldset[s^="english"] .pt4,
fieldset[s^="english"] .pt5,
fieldset[s^="english"] .pt6,
fieldset[s^="english"] .pt7,
fieldset[s^="english"] .pt8,
fieldset[s^="english"] .pt9 {
  text-align: justify;
  letter-spacing: 0;
  word-break: break-word;
}
fieldset.thiserror1 {
  border: 1px solid #f00;
}
fieldset.thiserror1 legend {
  border: 4px solid #f00;
}
fieldset.thiserror2 {
  border: 1px solid #adcd3c;
}
fieldset.thiserror2 legend {
  border: 4px solid #adcd3c;
}
fieldset.thisques {
  border: 1px solid blue;
}
fieldset.thison {
  border: 1px solid #a9c9e2;
}
fieldset.thison div.border {
  border: 1px solid #adcd3c;
  background-color: #f2fddb;
}
fieldset,
img {
  border: 0 none;
}
table.thison {
  border: 1px solid #00f;
}
table.thiserr {
  border: 1px solid #f00;
}
fieldset.thisvip1 {
  border: 1px solid #00f;
}
fieldset.thisvip1 legend {
  border: 4px solid #00f;
}
fieldset.status17 {
  border: 1px solid #ff00ff;
}
fieldset.status17 legend {
  border: 4px solid #ff00ff;
}
.selectoption {
  vertical-align: middle;
  font-size: 14px;
  padding: 2px;
}
.selectoption:hover {
  color: #ea8511;
}
.selectoption label {
  padding: 4px;
  line-height: 24px;
}
fieldset.quesbordere {
  border: 2px dotted #f00;
}
.answer {
  border: 1px dotted #ffffff;
}
ol.answer li,
ul.answer li {
  padding: 1px;
  font-size: 14px;
}
ol.answer li:hover {
  background: #f2f2f2;
}
.collapseContainerPanel {
  border: 0;
}
.collapsePanelHeader {
  height: 30px;
  font-weight: bold;
  padding: 6px 0 0 0;
}
.collapseHeaderContent {
  float: left;
  padding-left: 5px;
}
.collapseContent {
  margin: 0;
  padding: 0;
  border: 1px solid #ccc;
  border-top: 0;
}
.pt0 {
  padding: 2px 0 5px 0;
  font-size: 14px;
  font-family: "微软雅黑";
  font-weight: 700;
}
.pt1 {
  overflow: hidden;
  zoom: 1;
  clear: both;
  line-height: 25px;
  font-size: 14px;
  padding: 20px 20px 20px 20px;
  position: relative; /* word-break: break-word; */
  cursor: pointer;
}
fieldset.quesborder .pt1 em {
  position: static;
}
.pt1 img {
  position: relative;
}
.pt2 {
  padding: 0px 20px 20px 20px;
}
.pt3,
.pt4,
.pt5,
.pt6,
.pt7,
.pt11 {
  clear: both;
  zoom: 1;
  position: relative;
  padding: 0px 20px 20px 80px;
}
.pt8 a:link,
.pt8 a:visited {
  margin-right: 10px;
  padding: 2px 5px;
}
.pt8 a:hover {
  background: #fc0;
}
.pt9 {
  padding: 20px;
  border: 0 none;
  color: #999999;
  font-size: 12px;
}
.fieldtip {
  height: 36px;
  line-height: 36px;
  background-color: #f4f4f4;
  border-top: 1px solid #dadada;
  padding: 0 20px;
  color: #666666;
  border-radius: 0 0 10px 10px;
  position: relative;
  font-size: 12px;
}
li.ques-add,
div.ques-add {
  border-color: #ffe3c2;
}
li.ques-add:hover,
div.ques-add:hover {
  box-shadow: 0 0 0 3px #ffe3c2;
}
li.ques-add .fieldtip,
div.ques-add .fieldtip {
  background-color: #fff0bb;
}
li.ques-add fieldset.quesborder,
div.ques-add fieldset.quesborder {
  background-color: #fffae9;
}
li.ques-add .add,
div.ques-add .add {
  background-color: #fdcb91;
}
.newFieldtip .pt1,
.newFieldtip .pt2,
.newFieldtip .pt3,
.newFieldtip .pt4,
.newFieldtip .pt5,
.newFieldtip .pt6,
.newFieldtip .pt7,
.newFieldtip .pt8,
.newFieldtip.pt9,
.newFieldtip + .fieldtip {
  padding: 0;
}
fieldset img {
  max-width: 100%;
}
.fieldtip-left {
  float: left;
}
.fieldtip-left span {
  margin-right: 10px;
}
.fieldtip-right {
  float: right;
}
.fieldtip-right a {
  margin-left: 10px;
  display: inline-block;
  color: #666666;
}
.fieldtip-right .btn {
  color: #fff;
}
.fieldtip-right a i {
  margin-right: 3px;
}
.fieldtip-right a:hover .tip-pop-black {
  visibility: visible;
  opacity: 1;
}
/*填空题*/
div.quizPutTag:hover {
  color: #f60;
}
div.quizPutTag img {
  cursor: pointer;
  margin-left: 10px;
  max-width: 200px;
}
.sanwser {
  padding: 4px 10px;
  margin: 0px;
  border: 1px solid #adcd3c;
  background-color: #f2fddb;
  color: #000;
  display: none;
}
/*答案*/
.selectoption label.s,
div.s {
  border: 1px solid #91cbed;
  background-color: #deeeff;
  display: inline-block;
}
.selectoption label.s.sh,
div.s.sh {
  margin: 1px;
  border: none;
  background: none;
}
/*试题中用到的黑色实线表格*/
table.edittable,
table.edittable2,
table.edittable3,
table.edittable4 {
  border-collapse: collapse;
  margin: 2px;
}
table.edittable th,
table.edittable td,
table.edittable2 th,
table.edittable2 td,
table.edittable3 th,
table.edittable3 td,
table.edittable4 th,
table.edittable4 td {
  line-height: 30px;
  padding: 5px;
  white-space: normal;
  word-break: break-all;
  border: 1px solid #000;
  vertical-align: middle;
}
table.edittable {
  text-align: center;
} /*居中对齐*/
table.edittable2 {
  text-align: left;
} /*左对齐*/
table.edittable3 {
  text-align: left;
} /*第一行居中,其余左对齐*/
table.edittable3 tr:first-child td {
  text-align: center;
}
table.edittable4 {
  border: 1px solid #000;
} /*只有外边框，无内边框*/
table.edittable4 td {
  border: none;
}
table.composition {
  border-collapse: collapse;
  text-align: left;
  margin: 2px;
  width: 98%;
}
table.composition th,
table.composition td {
  line-height: 30px;
  white-space: normal;
  word-break: break-all;
  border-width: 0px;
  vertical-align: middle;
}
table.composition2 {
  border-collapse: collapse;
  width: auto;
}
table.composition2 th,
table.composition2 td {
  text-align: left;
  line-height: 30px;
  white-space: normal;
  word-break: break-all;
  border: none;
  border-width: 0px;
  vertical-align: middle;
}

.MathJye {
  border: 0 none;
  direction: ltr;
  line-height: normal;
  display: inline-block;
  float: none;
  font-family: "微软雅黑";
  font-size: 15px;
  font-style: normal;
  font-weight: normal;
  letter-spacing: 1px;
  line-height: normal;
  margin: 0;
  padding: 0;
  text-align: left;
  text-indent: 0;
  text-transform: none;
  white-space: nowrap;
  word-spacing: normal;
  word-wrap: normal;
  -webkit-text-size-adjust: none;
}
.MathJye div,
.MathJye span {
  border: 0 none;
  margin: 0;
  padding: 0;
  line-height: normal;
  text-align: left;
  height: auto;
  _height: auto;
  white-space: normal;
}
.MathJye table {
  border-collapse: collapse;
  margin: 0;
  padding: 0;
  text-align: center;
  vertical-align: middle;
  line-height: normal;
  font-size: inherit;
  font-style: normal;
  font-weight: normal;
  border: 0;
  float: none;
  display: inline-block;
  zoom: 0;
}
.MathJye table td {
  padding: 0;
  font-size: inherit;
  line-height: normal;
  white-space: nowrap;
  border: 0 none;
  width: auto;
  _height: auto;
}
.flipv {
  -ms-transform: scaleX(-1);
  -moz-transform: scaleX(-1);
  -webkit-transform: scaleX(-1);
  -o-transform: scaleX(-1);
  transform: scaleX(-1);
  filter: FlipH;
}
.fliph {
  -ms-transform: scaleY(-1);
  -moz-transform: scaleY(-1);
  -webkit-transform: scaleY(-1);
  -o-transform: scaleY(-1);
  transform: scaleY(-1);
  filter: FlipV;
}

/*加粗*/
.mathjye-bold {
  font-weight: 800;
}
/*删除线*/
.mathjye-del {
  text-decoration: line-through;
}
/*斜体*/
.mathjye-italic {
  font-style: italic;
}
/*楷体*/
.mathjye-kaiti {
  font-family: KaiTi;
}
/*下划线*/
:deep(.mathjye-underline) {
  border-bottom: 1px solid #000;
  padding-bottom: 2px;
  min-width: 2em;
  min-height: 1em;
  display: inline;
}
@-moz-document url-prefix() {
  .mathjye-underline {
    padding-bottom: 0px;
  }
}
/*点线*/
:deep(.mathjye-underpline) {
  border-bottom: 2px dotted #000;
  padding-bottom: 3px;
}
@-moz-document url-prefix() {
  .mathjye-underpline {
    padding-bottom: 1px;
  }
}
/*加点*/
:deep(.mathjye-underpoint) {
  background: url(@/assets/img/test-page/point.png) no-repeat center bottom;
  padding-bottom: 4px;
}
:deep(.mathjye-underpoint2) {
  background: url(@/assets/img/test-page/dot.png) repeat-x 0 18px;
  border-bottom: none;
  padding-bottom: 6px;
}
@-moz-document url-prefix() {
  .mathjye-underpoint {
    padding-bottom: 1px;
  }
}
/*波浪线*/
:deep(.mathjye-underwave) {
  background: url(@/assets/img/test-page/wave.png) bottom repeat-x;
  padding-bottom: 4px;
}
@-moz-document url-prefix() {
  .mathjye-underwave {
    padding-bottom: 1px;
  }
}
/*左对齐*/
:deep(.mathjye-alignleft) {
  display: block;
  text-align: left;
}
/*居中对齐*/
:deep(.mathjye-aligncenter) {
  display: block;
  text-align: center;
}
/*右对齐*/
:deep(.mathjye-alignright) {
  display: block;
  text-align: right;
}
