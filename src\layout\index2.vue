<template>
  <topbar></topbar>
  <template v-if="!hideTab">
    <div class="middle-tab flex-center" v-show="showTab">
      <div class="middle-tab-box flex-center">
        <template v-for="item in routeData.menuList">
          <!-- 知识点视频 -->
          <template v-if="item.path=='/point_room'">
            <template v-if="gradeId>=10">
              <div class="middle-tab-box-child flex-center" :class="item.selected?'middle-tab-box-child-selected':''"
                @click="setRoute(item)">
                <div v-if="item.meta.icon == 'ai-companion'" class="flex-center">
                  <img class="ai-companion-img" :src="getUrl(item.meta.icon)" :alt="item.meta.icon">
                </div>
                <div v-else class="flex-center">
                  <img class="middle-tab-box-child-icon" :src="getUrl(item.meta.icon)" :alt="item.meta.icon">
                  <span class="middle-tab-box-child-text">{{ item.meta.title }}</span>
                </div>
              </div>
            </template>
          </template>
          <!-- 名师课堂 -->
          <template v-else-if="item.path=='/teach_room'">
            <template v-if="gradeId<10">
              <div class="middle-tab-box-child flex-center" :class="item.selected?'middle-tab-box-child-selected':''"
                @click="setRoute(item)">
                <div v-if="item.meta.icon == 'ai-companion'" class="flex-center">
                  <img class="ai-companion-img" :src="getUrl(item.meta.icon)" :alt="item.meta.icon">
                </div>
                <div v-else class="flex-center">
                  <img class="middle-tab-box-child-icon" :src="getUrl(item.meta.icon)" :alt="item.meta.icon">
                  <span class="middle-tab-box-child-text">{{ item.meta.title }}</span>
                </div>
              </div>
            </template>
          </template>
          <div class="middle-tab-box-child flex-center" :class="item.selected?'middle-tab-box-child-selected':''"
            @click="setRoute(item)" v-else>
            <div v-if="item.meta.icon == 'ai-companion'" class="flex-center">
              <img class="ai-companion-img" :src="getUrl(item.meta.icon)" :alt="item.meta.icon">
            </div>
            <div v-else class="flex-center">
              <img class="middle-tab-box-child-icon" :src="getUrl(item.meta.icon)" :alt="item.meta.icon">
              <span class="middle-tab-box-child-text">{{ item.meta.title }}</span>
            </div>
          </div>
        </template>
      </div>
      <!-- <div v-if="!orgName && !isAiDomain" class="link-box" @click="toWeb"><span>老师端</span></div> -->
    </div>
    <div class="crumb-box flex-center" v-show="!showTab">
      <div class="crumb-box-box flex-center">
        <div class="back-route" @click="goBack">
          < 返回 </div>
            <div v-html="crumbsText"></div>
        </div>
      </div>
  </template>
  <!-- 全屏展示页面 -->
  <!-- <div class="main-full">
    <template v-if="isThree">
      <router-view v-slot="{ Component, route }">
        <keep-alive v-if="route.meta.keepAlive">
          <component :is="Component"></component>
        </keep-alive>
        <component :is="Component" v-else></component>
      </router-view>
    </template>
    <template v-else>
      <router-view v-slot="{ Component, route }">
        <keep-alive v-if="route.meta.keepAlive">
          <component :is="Component"></component>
        </keep-alive>
        <component :is="Component" v-else></component>
      </router-view>
    </template>
  </div> -->
  <div class="main-full" v-if="isFull">
    <template v-if="isThree">
      <router-view v-slot="{ Component, route }">
        <keep-alive v-if="route.meta.keepAlive">
          <component :is="Component"></component>
        </keep-alive>
        <component :is="Component" v-else></component>
      </router-view>
    </template>
    <template v-else>
      <router-view v-slot="{ Component, route }">
        <keep-alive v-if="route.meta.keepAlive">
          <component :is="Component"></component>
        </keep-alive>
        <component :is="Component" v-else></component>
      </router-view>
    </template>
  </div>
  <div class="content-box" v-else>
      <div class="main-box" :class="showTab?'top10':''">
          <template v-if="isThree">
            <router-view v-slot="{ Component, route }">
              <keep-alive v-if="route.meta.keepAlive">
                <component :is="Component"></component>
              </keep-alive>
              <component :is="Component" v-else></component>
            </router-view>
          </template>
          <template v-else>
            <router-view v-slot="{ Component, route }">
              <keep-alive v-if="route.meta.keepAlive">
                <component :is="Component"></component>
              </keep-alive>
              <component :is="Component" v-else></component>
            </router-view>
          </template>
      </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref, onMounted} from 'vue';
  import { RouterView } from 'vue-router'
  import { useRoute } from 'vue-router';
  import router from '@/router/index'
  const route = useRoute();
  import { useRouteStoreHook } from '@/store/modules/route'
  import topbar from '@/layout/topbar.vue'
  let orgName = localStorage.userInfo?.orgName
 
  
  onMounted(() => {
    // console.log(route.path);
  })
  let gradeId = ref(0)
  if (localStorage.learnNow) {
    gradeId.value = JSON.parse(localStorage.learnNow).gradeId
  }
  useRouteStoreHook().initMenuList()
  const routeData = useRouteStoreHook()
  const showTab = computed(() => {
    return router.currentRoute.value.meta.showTab
  })
  const hideTab = computed(() => {
    return router.currentRoute.value.meta.hideTab
  })

  const isFull = computed(() => {
    return router.currentRoute.value.meta.isFull
  })

  const isThree = computed(() => {
    return router.currentRoute.value.meta.requiresNestedView
  })
  const crumbsText = computed(() => {
    let text = ''
    router.currentRoute.value.matched.map((item : any, index : number) => {
      if (index == 0) {
        text += item.meta.title
      } else {
        text += `<span class='mag'> > </span>${item.meta.title}`

      }
    })
    return text
  })
  // const showTab = router.currentRoute.value.meta.showTab
  // 检查当前域名是否为ai.xyedu.com
const isAiDomain = computed(() => {
  const currentUrl = window.location.href
  return currentUrl.startsWith('https://ai.xyedu.com/')
//   return currentUrl.startsWith('https://ai-test.xiaoyeoo.com/')
})
  const getUrl = (url : string) => {
    return new URL(`../assets/img/layout/${url}.png`, import.meta.url).href //静态资源引入为url，相当于require()
  }
  const setRoute = (item : any) => {
    router.push({
      path: item.path
    })
    useRouteStoreHook().setSelectMenu(item.name)
  }
  const goBack = () => {
    router.go(-1)
  }

  const toWeb = () => {
    window.open('https://tiku.xiaoyeoo.com/login')
  }
</script>
<style lang="scss" scoped>
  .flex-center {
    display: flex;
    align-items: center;
  }

  .crumb-box {
    width: 100%;
    justify-content: center;
    height: 3.0625rem;
    background-color: #F5F5F5;

    &-box {
      width: 81.25rem;
      height: 3.0625rem;
      color: #666666;
      font-size: .875rem;
      font-weight: 400;

      .back-route {
        cursor: pointer;
        margin-right: 1.875rem;
      }
    }
  }

  .middle-tab {
    float: left;
    width: 100%;
    justify-content: center;
    height: 5.8125rem;
    background-image: url(@/assets/img/layout/middle-bg.png);
    background-size: cover;
    background-repeat: no-repeat;
    position: relative;

    &-box {
      // width: 81.25rem;
      // width: 95.25rem;
      height: 2.75rem;

      &-child {
        width: 8.875rem;
        height: 2.75rem;
        border-radius: 1.375rem;
        justify-content: center;
        margin-right: 1.875rem;
        cursor: pointer;

        &:hover {
          background: rgb(236, 249, 234);
          color: #2A2B2A;
        }

        &-selected {
          color: rgb(255, 255, 255);
          background: linear-gradient(147.38deg, rgb(54, 226, 194) 14.694%, rgb(0, 183, 208) 83.07%);
        }

        &-icon {
          width: 1.625rem;
          height: 1.625rem;
          margin-right: .375rem;
        }

        &-text {
          font-size: 1.25rem;
          font-weight: 400;
        }
      }

      .ai-companion-img {
        width: 7rem;
        height: 2.25rem;
      }
    }
  }

  .link-box {
    position: absolute;
    top: 2rem;
    right: 0;
    cursor: pointer;
    background-image: url(@/assets/img/layout/to-web.png);
    background-size: contain;
    width: 2.1875rem;
    height: 6.4375rem;
    letter-spacing: .375rem;
    writing-mode: vertical-lr;
    background-repeat: no-repeat;
    color: #ffffff;
    font-size: 1rem;
    font-weight: 700;
    z-index: 899;

    span {
      margin-left: .6875rem;
      margin-top: 1.375rem;
      display: inline-block;
    }
  }

  .top10 {
    margin-top: .625rem;
  }

  .content-box {
    display: flex;
    justify-content: center;
    background: #f5f5f5;

    .main-box {
      width: 81.25rem;
      height: 100%;
    }
  }

  .main-full {
    width: 100%;
    display: flex;
    position: relative;
  }
</style>