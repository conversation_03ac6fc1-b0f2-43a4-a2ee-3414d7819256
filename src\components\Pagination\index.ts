// packages / index.js

// 导入单个组件
import Pagination from "./index.vue"

// 以数组的结构保存组件，便于遍历
const components = [Pagination]

// 定义 install 方法
const install: any = function (Vue: any) {
  if (install.installed) return
  install.installed = true
  // 遍历并注册全局组件
  components.map((component) => {
    Vue.component(component.name, component)
  })
}

if (typeof window !== "undefined" && (window as any).Vue) {
  install((window as any).Vue)
}

export default {
  // 导出的对象必须具备一个 install 方法
  install,
  // 组件列表
  ...components
}
