<!-- 名师课堂-我的收藏 -->
<template>
  <div class="content" v-loading.screen="state.loading">
    <div class="inner">
      <div class="wrap">
        <!-- 添加tab切换 -->
        <div class="subject-tabs" v-if="!state.showBox&&state.versions?.length > 0">
          <div 
            v-for="(item, index) in state.versions" 
            :key="index" 
            class="tab-item" 
            :class="{ active: state.activeSubjectIndex === index }"
            @click="changeSubject(index, item)">
            {{ item.subjectName }}
          </div>

          <div class="batch" style="border-bottom: 0;height: auto;" v-show="state.colData.len&&!state.showBox">
            <div class="operate" @click="boxShow">批量操作</div>
          </div>
        </div>

        <div class="batch" v-show="state.showBox">
          <div class="select" :class="state.colData.selAll" @click="setSelAll">
            <img src="@/assets/img/teachroom/check.svg" />
            <img src="@/assets/img/teachroom/checksel.svg" />
            <div>全选</div>
          </div>
          <div class="del_box">
            <div class="del_ok" @click="delItem">取消收藏</div>
            <div class="del_back" @click="boxHide">返回</div>
          </div>
        </div>
        <!-- hide隐藏复选框 -->
        <div class="vid_box" :class="state.showBox?'':'hide'" v-if="state.colData.len">
          <div class="vid_item" v-if="state.colData.today.length">
            <div class="vid_tit" :class="state.colData.todayAll" @click="setTodayAll">
              <img src="@/assets/img/teachroom/check.svg" />
              <img src="@/assets/img/teachroom/checksel.svg" />
              <div>今天</div>
            </div>
            <div class="vid_ul">
              <div class="vid_libox" v-for="(item,index) in state.colData.today" :key="index" @click="setTodayOne"
                :data-i="index">
                <div class="vid_li" :class="item.active" @click="goVideo" :data-i="index" data-type="0">
                  <div class="vid_img">
                    <img :src="item.videoCover" class="wk_img" v-if="item.videoCover" />
                    <img src="@/assets/img/teachroom/novid.png" class="wk_img" v-else />
                    <div class="vid_h1" :class="item.videoTag1?'':'opac'" v-if="item.videoTag1">{{item.videoTag1}}</div>
                  </div>
                  <div class="vid_name nowrap" v-html="ReplaceMathString(item.videoTag0)"></div>
                  <div class="vid_check active" :class="item.active">
                    <img src="@/assets/img/teachroom/check2.png" />
                    <img src="@/assets/img/teachroom/check2sel.png" />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="vid_item" v-if="state.colData.yestoday.length">
            <div class="vid_tit" :class="state.colData.yestodayAll" @click="setYestodayAll">
              <img src="@/assets/img/teachroom/check.svg" />
              <img src="@/assets/img/teachroom/checksel.svg" />
              <div>昨天</div>
            </div>
            <div class="vid_ul">
              <div class="vid_libox" v-for="(item,index) in state.colData.yestoday" :key="index" @click="setYestodayOne"
                :data-i="index">
                <div class="vid_li" :class="item.active" @click="goVideo" :data-i="index" data-type="1">
                  <div class="vid_img">
                    <img :src="item.videoCover" class="wk_img" v-if="item.videoCover" />
                    <img src="@/assets/img/teachroom/novid.png" class="wk_img" v-else />
                    <div class="vid_h1" :class="item.videoTag1?'':'opac'" v-if="item.videoTag1">{{item.videoTag1}}</div>
                  </div>
                  <div class="vid_name nowrap" v-html="ReplaceMathString(item.videoTag0)"></div>
                  <div class="vid_check" :class="item.active">
                    <img src="@/assets/img/teachroom/check2.png" />
                    <img src="@/assets/img/teachroom/check2sel.png" />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="vid_item" v-if="state.colData.moreday.length">
            <div class="vid_tit" :class="state.colData.moredayAll" @click="setMoredayAll">
              <img src="@/assets/img/teachroom/check.svg" />
              <img src="@/assets/img/teachroom/checksel.svg" />
              <div>更早</div>
            </div>
            <div class="vid_ul">
              <div class="vid_libox" v-for="(item,index) in state.colData.moreday" :key="index" @click="setMoredayOne"
                :data-i="index">
                <div class="vid_li" :class="item.active" @click="goVideo" :data-i="index" data-type="2">
                  <div class="vid_img">
                    <img :src="item.videoCover" class="wk_img" v-if="item.videoCover" />
                    <img src="@/assets/img/teachroom/novid.png" class="wk_img" v-else />
                    <div class="vid_h1" :class="item.videoTag1?'':'opac'" v-if="item.videoTag1">{{item.videoTag1}}</div>
                  </div>
                  <div class="vid_name nowrap" v-html="ReplaceMathString(item.videoTag0)"></div>
                  <div class="vid_check" :class="item.active">
                    <img src="@/assets/img/teachroom/check2.png" />
                    <img src="@/assets/img/teachroom/check2sel.png" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 无数据 -->
        <div class="nodata" v-if="state.isShow && !state.colData.len">
          <img src="@/assets/img/user/nodata.png" />暂无收藏
        </div>
      </div>
    </div>
  </div>

  <!-- 取消收藏 -->
  <div v-show="state.isDel">
    <div class="alert_bg"></div>
    <div class="alert_box">
      <div class="alert_inner">
        <div class="alert_top">
          <div class="alert_h1">取消收藏</div>
          <img src="@/assets/img/user/close.svg" class="alert_x" @click="state.isDel=false" />
        </div>
        <div class="alert_wrap">
          <div class="alert_tit">确定要取消收藏所选中的视频吗？</div>
          <img src="@/assets/img/teachroom/nofav.png" class="alert_fav" />
          <div class="alert_btns">
            <div class="alert_quit" @click="state.isDel=false">取消</div>
            <div class="alert_ok" @click="quitCollect">确定</div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 购买会员弹窗 -->
  <buyVip :show="state.showVip" @close="state.showVip = false"></buyVip>
</template>

<script lang="ts" setup>
  import { reactive, onMounted, watch } from 'vue';
  import router from '@/router/index'
  import { useUserStore } from "@/store/modules/user"
  import { ElMessage } from "element-plus"
  import { subjectList } from '@/utils/user/enum'
  import { ReplaceMathString } from '@/utils/user/util'
  import { setVideoCollectApi, videoDayCollectApi, videoMoreCollectApi, getVideoReviewNumApi } from "@/api/video"
  import { useRoute } from "vue-router"
  import buyVip from "@/views/components/buyVip/index.vue"
  import { setLearnKey } from '@/utils/user/learntime'
  const learnNow = useUserStore().learnNow
  defineOptions({
    name: "synchronousCollect"
  })

  const route = useRoute()
  const query = reactive<any>(route.query)

  const state : any = reactive({
    showVip: false,
    loading: false,
    subName: '',
    subject: '',
    isShow: 0,
    showBox: '',
    isDel: false,
    colData: {
      selAll: '',
      todayAll: '',
      yestodayAll: '',
      moredayAll: '',
      today: [],
      yestoday: [],
      moreday: [],
      len: 0
    },
    // 添加版本相关数据
    versions: [],
    activeSubjectIndex: 0,
    currentSubject: null
  })

  onMounted(() => {
    initVersions()
    init()
    console.log(learnNow,"打印 一下")
  })

  // 初始化版本数据
  const initVersions = () => {
    if (learnNow && learnNow.versions && learnNow.versions.length > 0) {
         // 添加"全部"选项
         const allOption = {
              subject: 'all',
              subjectName: '全部',
              isAllOption: true
          }
      
      state.versions = [allOption, ...learnNow.versions]
      // state.versions = learnNow.versions
      // 如果路由中有subKey参数，则根据subKey设置当前选中的科目
      if (route.query.subKey) {
        const subKey = route.query.subKey
        const index = state.versions.findIndex((item: any) => {
          return item.subject === subjectList[subKey].key
        })
        if (index !== -1) {
          state.activeSubjectIndex = index
          state.currentSubject = state.versions[index]
        } else {
          // 如果没找到匹配的科目，默认选中第一个
          state.currentSubject = state.versions[0]
        }
      } else {
        // 默认选中第一个科目
        state.currentSubject = state.versions[0]
      }
    } else {
      // 如果没有版本数据，使用默认值
      state.currentSubject = {
        subject: subjectList[route.query.subKey || 'chinese3'].key,
        subjectName: subjectList[route.query.subKey || 'chinese3'].name
      }
    }
  }

  // 切换科目
  const changeSubject = (index: number, item: any) => {
    state.activeSubjectIndex = index
    state.currentSubject = item
    // 更新学习科目
    setLearnKey(item.subject)
    // 重新加载数据
    init()
  }

  //监听路由参数
  watch(
    () => route.query,
    (newQ) => {
      let subName = newQ.subName
      if (subName && route.name == "TeachRoomTeachCollect") {
        localStorage.removeItem('isLoad')
        initVersions()
        init()
      }
    }
  )

  // 属性赋值
  const setData = (obj : any) => {
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        state[key] = obj[key]
      }
    }
  }

  const init = () => {
    let subject = ''
    
    // 如果有当前选中的科目，则使用当前科目
    if (state.currentSubject.subject == 'all') { 
      subject=''
    } else if (state.currentSubject && state.currentSubject.subject) {
      subject = subjectList[state.currentSubject.subject].key

    } else {
      // 否则使用路由参数或默认值
      subject = subjectList[route.query.subKey || 'chinese3'].key
    }
    
    const param = {
      source: 3, //1同步章节视频 2知识点视频,
      type: 1,
      subject
    }
    setData({
      loading: true,
      subject
    })
    
    //记录学习学科
    setLearnKey(subject)
    
    //今天昨天
    videoDayCollectApi(param).then((res : any) => {
      const list = res.data || []
      //更多
      videoMoreCollectApi(param, 1, 99999).then((res2 : any) => {
        const list2 = res2.data.records || []
        let data = {
          today: list?.today?.length ? list.today : [],
          yestoday: list?.yesterday?.length ? list.yesterday : [],
          moreday: list2,
          len: 0
        }
        data = setColDefault(data)
        setData({
          loading: false,
          colData: data,
          isShow: 1
        })
      })
    })
  }
  //设置复选框不选中,统计视频数
  const setColDefault = (data : any) => {
    let len = 0
    data.selAll = ''
    data.todayAll = ''
    data.yestodayAll = ''
    data.moredayAll = ''
    if (data.today) {
      for (const i of data.today) {
        const tag = i.videoTag
        if (tag.indexOf('(例题') > -1) {
          const str = tag.split('(例题')
          i.videoTag0 = `<div class="nowrap">${str[0]}</div>`
          i.videoTag1 = `例题${str[1].split(')')[0]}`
        } else {
          i.videoTag0 = `<div class="nowrap">${i.videoTag}</div>`
        }
        i.active = ''
        len++
      }
    }
    if (data.yestoday) {
      for (const i of data.yestoday) {
        const tag = i.videoTag
        if (tag.indexOf('(例题') > -1) {
          const str = tag.split('(例题')
          i.videoTag0 = `<div class="nowrap">${str[0]}</div>`
          i.videoTag1 = `例题${str[1].split(')')[0]}`
        } else {
          i.videoTag0 = `<div class="nowrap">${i.videoTag}</div>`
        }
        i.active = ''
        len++
      }
    }
    if (data.moreday) {
      for (const i of data.moreday) {
        const tag = i.videoTag
        if (tag.indexOf('(例题') > -1) {
          const str = tag.split('(例题')
          i.videoTag0 = `<div class="nowrap">${str[0]}</div>`
          i.videoTag1 = `例题${str[1].split(')')[0]}`
        } else {
          i.videoTag0 = `<div class="nowrap">${i.videoTag}</div>`
        }
        i.active = ''
        len++
      }
    }
    data.len = len
    return data
  }
  //复选框显示
  const boxShow = () => {
    // 重置没选中
    const data = setColDefault(state.colData)
    setData({
      colData: data,
      showBox: 'show'
    })
  }
  //复选框隐藏
  const boxHide = () => {
    setData({
      showBox: '',
      isDel: false
    })
  }

  //复选框全选-所有
  const setSelAll = () => {
    const { colData } = state
    let active = colData.selAll
    if (active) {
      active = ''
      for (const i of colData.today) {
        i.active = ''
      }
      for (const i of colData.yestoday) {
        i.active = ''
      }
      for (const i of colData.moreday) {
        i.active = ''
      }
      colData.todayAll = ''
      colData.yestodayAll = ''
      colData.moredayAll = ''
    } else {
      active = 'active'
      for (const i of colData.today) {
        i.active = 'active'
      }
      for (const i of colData.yestoday) {
        i.active = 'active'
      }
      for (const i of colData.moreday) {
        i.active = 'active'
      }
      colData.todayAll = 'active'
      colData.yestodayAll = 'active'
      colData.moredayAll = 'active'
    }
    colData.selAll = active
    setData({
      colData
    })
  }

  //复选框全选-今天
  const setTodayAll = () => {
    const { colData } = state
    let active = colData.todayAll
    if (active) {
      active = ''
      for (const i of colData.today) {
        i.active = ''
      }
    } else {
      active = 'active'
      for (const i of colData.today) {
        i.active = 'active'
      }
    }
    colData.todayAll = active
    setData({
      colData
    })
  }
  //复选框单选-今天
  const setTodayOne = (e : any) => {
    const { i } = e.currentTarget.dataset
    const { colData } = state
    const active = colData.today[i].active
    if (active) {
      colData.today[i].active = ''
    } else {
      colData.today[i].active = 'active'
    }
    //判断全选
    let len = 0
    for (const i of colData.today) {
      if (i.active) {
        len++
      }
    }
    if (len == colData.today.length) {
      colData.todayAll = 'active'
    } else {
      colData.todayAll = ''
    }
    setData({
      colData
    })
  }
  //复选框全选-昨天
  const setYestodayAll = () => {
    const { colData } = state
    let active = colData.yestodayAll
    if (active) {
      active = ''
      for (const i of colData.yestoday) {
        i.active = ''
      }
    } else {
      active = 'active'
      for (const i of colData.yestoday) {
        i.active = 'active'
      }
    }
    colData.yestodayAll = active
    setData({
      colData
    })
  }
  //复选框单选-昨天
  const setYestodayOne = (e : any) => {
    const { i } = e.currentTarget.dataset
    const { colData } = state
    const active = colData.yestoday[i].active
    if (active) {
      colData.yestoday[i].active = ''
    } else {
      colData.yestoday[i].active = 'active'
    }
    //判断全选
    let len = 0
    for (const i of colData.yestoday) {
      if (i.active) {
        len++
      }
    }
    if (len == colData.yestoday.length) {
      colData.yestodayAll = 'active'
    } else {
      colData.yestodayAll = ''
    }
    setData({
      colData
    })
  }
  //复选框全选-更多
  const setMoredayAll = () => {
    const { colData } = state
    let active = colData.moredayAll
    if (active) {
      active = ''
      for (const i of colData.moreday) {
        i.active = ''
      }
    } else {
      active = 'active'
      for (const i of colData.moreday) {
        i.active = 'active'
      }
    }
    colData.moredayAll = active
    setData({
      colData
    })
  }
  //复选框单选-更多
  const setMoredayOne = (e : any) => {
    const { i } = e.currentTarget.dataset
    const { colData } = state
    const active = colData.moreday[i].active
    if (active) {
      colData.moreday[i].active = ''
    } else {
      colData.moreday[i].active = 'active'
    }
    //判断全选
    let len = 0
    for (const i of colData.moreday) {
      if (i.active) {
        len++
      }
    }
    if (len == colData.moreday.length) {
      colData.moredayAll = 'active'
    } else {
      colData.moredayAll = ''
    }
    setData({
      colData
    })
  }
  // 跳知识点视频-判断会员
  const goVideo = (e : any) => {
    if (state.showBox) {
      return
    }
    const isVip = useUserStore().memberInfo
    if (isVip) {
      goVideo2(e)
    } else {
      getVideoReviewNumApi().then((res : any) => {
        const num = res.data
        if (num < 2) {
          ElMessage.success(`剩余免费观看次数：${2 - num - 1}`)
          goVideo2(e)
        } else {
          state.showVip = true
        }
      })
    }
  }
  // 跳知识点视频-仅全屏播放
  const goVideo2 = (e : any) => {
    const { type, i } = e.currentTarget.dataset
    const { colData, subject } = state
    let videoId = '',
      info : any = {}
    if (type == 0) {
      //今天
      info = colData.today[i]
      videoId = info.videoId
    } else if (type == 1) {
      //昨天
      info = colData.yestoday[i]
      videoId = info.videoId
    } else if (type == 2) {
      //更早
      info = colData.moreday[i]
      videoId = info.videoId
    }
    // //缓存视频信息
    // localStorage.teachVidInfo = JSON.stringify(info)
    // router.push({ name: "TeachRoomTeachVideo2", query: { subject, vid: videoId, subKey: query.subKey } })
      console.log("subject---",subject,info)
      const query: any = route.query;
      query.vid = info?.videoId
      query.type = info?.type
      query.source = info?.source
      console.log("subject---",subject,info)
      query.subject =info?.subject
      query.id = info?.chapterId
      query.sourceType = 'collect'
      query.videoTitle=info?.videoTag
      router.push({ name: "SynchronousVideo", query:query })
  }
  //显示收藏弹窗
  const delItem = () => {
    const { colData } = state
    const { today, yestoday, moreday } = colData
    //提取选中的数组
    const idArr : any = []
    if (today.length) {
      for (const i of today) {
        if (i.active) {
          idArr.push(i.videoId)
        }
      }
    }
    if (yestoday.length) {
      for (const i of yestoday) {
        if (i.active) {
          idArr.push(i.videoId)
        }
      }
    }
    if (moreday.length) {
      for (const i of moreday) {
        if (i.active) {
          idArr.push(i.videoId)
        }
      }
    }
    if (idArr.length) {
      setData({
        isDel: true
      })
      // app.confirm(this, '确定要取消收藏吗', '确定', '取消')
    } else {
      ElMessage.error('请选择你要删除的视频')
    }
  }
  // 取消收藏
  const quitCollect = (e : any) => {
    const { colData } = state
    const { today, yestoday, moreday } = colData
    //提取选中id数组、没选中的数组
    const ids : any = [[], [], []],
      idArr : any = []
    if (today.length) {
      for (const i of today) {
        if (i.active) {
          ids[0].push(i.videoId)
          idArr.push(i.videoId)
        }
      }
    }
    if (yestoday.length) {
      for (const i of yestoday) {
        if (i.active) {
          ids[0].push(i.videoId)
          idArr.push(i.videoId)
        }
      }
    }
    if (moreday.length) {
      for (const i of moreday) {
        if (i.active) {
          ids[0].push(i.videoId)
          idArr.push(i.videoId)
        }
      }
    }
    const z = 0
    if (idArr.length) {
      setCollect()
    } else {
      ElMessage.error('请选择你要删除的视频')
    }

    function setCollect() {
      const param = {
        deleteType: 0, //0全部 1今天 2昨天 3更多
        videoIds: idArr
      }
      setVideoCollectApi(param)
        .then(() => {
          ElMessage.success('已取消收藏')
          boxHide()
          setTimeout(() => {
            init()
          }, 1000)
        })
    }
  }
</script>

<style lang="scss" scoped>
  @import url('@/assets/styles/reset.css');

  // 添加tab样式
  .subject-tabs {
    display: flex;
    padding: 40px  1.25rem 0 1.25rem;
    border-bottom: .0625rem solid #eaeaea;
    overflow-x: auto;
    align-items: center;
  }

  .tab-item {
    position: relative;
    padding: .5rem 1rem;
    margin-right: 1rem;
    cursor: pointer;
    border-radius: .25rem;
    font-size: .875rem;
    color: #666;
    white-space: nowrap;
    transition: all 0.3s;
  }

  .tab-item.active {
    color: rgba(0, 156, 127, 1);
    font-weight: bold;
  }
  .tab-item.active::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 4rem;
    height: 2px;
    background-color: rgba(0, 156, 127, 1);
  }

  .tab-item:hover {
    color: rgba(0, 156, 127, 1);
  }
  .opac {
    opacity: 0;
  }

  .none {
    display: none !important
  }

  .nowrap {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .content {
    width: 100%;
    overflow-y: auto;
    background: #F5F5F5;
  }

  .inner {
    margin: 0 auto;
    width: 81.25rem;
  }

  .wrap {
    float: left;
    width: 100%;
    border: .0625rem solid #eaeaea;
    border-bottom: none;
    height: calc(100vh - 7.4375rem);
    box-sizing: border-box;
    background: #fff;
    display: flex;
    flex-flow: column;
  }

  /* 全选 */
  .batch {
    float: left;
    width: 100%;
    height: 5rem;
    border-bottom: .0625rem solid #eaeaea;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding: 0 1.5625rem 0 1.25rem;
    position: relative;
  }

  .operate {
    float: right;
    width: 6.875rem;
    line-height: 2.25rem;
    text-align: center;
    border-radius: .25rem;
    border: .0625rem solid #eaeaea;
    color: #666666;
    font-size: 1rem;
    // margin: 0 0 0 auto;
    position: absolute;
    bottom: 0px;
    right: 10px;
  }

  .select div {
    float: left;
    color: #666666;
    font-size: .875rem;
    line-height: 1.25rem;
  }

  .select img {
    float: left;
    width: 1.25rem;
    height: 1.25rem;
    margin: 0 .625rem 0 0;
  }

  .select img:nth-child(1),
  .select.active img:nth-child(2) {
    display: inline-block;
  }

  .select img:nth-child(2),
  .select.active img:nth-child(1) {
    display: none;
  }

  .del_box {
    margin: 0 0 0 auto;
  }

  .del_ok {
    float: left;
    width: 6.875rem;
    height: 2.25rem;
    line-height: 2.25rem;
    border-radius: .25rem;
    background: #dd2a2a1a;
    font-size: 1rem;
    color: #DD2A2A;
    text-align: center;
    margin: 0 1.25rem 0 0;
  }

  .del_back {
    float: left;
    width: 6.875rem;
    height: 2.25rem;
    line-height: 2.25rem;
    border-radius: .25rem;
    font-size: 1rem;
    color: #666;
    text-align: center;
    border: .0625rem solid #eaeaea;
    box-sizing: border-box;
  }

  .operate:hover,
  .select:hover,
  .del_box div:hover {
    cursor: pointer;
  }

  /* 列表 */
  .vid_box {
    flex: 1;
    box-sizing: border-box;
    padding: 0 1.25rem;
    overflow-y: auto;
  }

  .vid_box div {
    float: left;
  }

  .vid_item {
    width: 100%;
    line-height: 2.375rem;
    color: #666666;
    font-size: .875rem;
  }

  .vid_tit {
    width: 100%;
    height: 2.375rem;
    box-sizing: border-box;
    padding: .625rem 0;
  }

  .vid_tit div {
    float: left;
    line-height: 1.25rem;
    color: #666666;
    font-size: .875rem;
  }

  .vid_tit img {
    float: left;
    width: 1.25rem;
    height: 1.25rem;
    margin: 0 .625rem 0 0;
  }

  .vid_tit img:nth-child(1),
  .vid_tit.active img:nth-child(2) {
    display: inline-block;
  }

  .vid_tit img:nth-child(2),
  .vid_tit.active img:nth-child(1) {
    display: none;
  }

  .vid_box.hide .vid_tit img,
  .vid_box.hide .vid_check img {
    display: none !important;
  }

  .vid_ul {
    width: 100%;
  }

  .vid_libox {
    width: calc((100% - 2.5rem) / 5);
    margin: 0 .625rem 1.25rem 0;
  }

  .vid_li {
    width: 100%;
  }

  .vid_li:hover {
    cursor: pointer;
  }

  .vid_li:nth-child(5n+5) {
    margin-right: 0;
  }

  .vid_img,
  .vid_img img {
    float: left;
    width: 100%;
    height: 8.5625rem;
    border-radius: .25rem;
  }

  div.vid_h1 {
    float: right;
    line-height: 1.5rem;
    border-radius: .75rem;
    background: rgba(0, 0, 0, 0.6);
    padding: 0 .875rem;
    color: #ffffff;
    font-size: .75rem;
    margin: -7.9375rem .625rem 0 0;
  }

  .vid_name {
    width: 100%;
    line-height: 1.1875rem;
    color: #2a2b2a;
    font-size: .875rem;
    padding: .375rem 0 0;
  }

  :deep(.vid_name .nowrap){
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .vid_check {
    margin: -9.5rem 0 0 .625rem;
    position: relative;
    z-index: 5;
  }

  .vid_check img {
    float: left;
    width: 1.25rem;
    height: 1.25rem;
    margin: 0 .625rem 0 0;
  }

  .vid_check img:nth-child(1),
  .vid_li.active .vid_check img:nth-child(2) {
    display: inline-block;
  }

  .vid_check img:nth-child(2),
  .vid_li.active .vid_check img:nth-child(1) {
    display: none;
  }

  .vid_box.hide .vid_tit img,
  .vid_box.hide .vid_check img {
    display: none !important;
  }

  /* 暂无数据 */
  .nodata {
    flex: 1;
    color: #999999;
    font-size: .875rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-flow: column;
  }

  .nodata img {
    width: 7.4375rem;
    height: 8rem;
    margin: 0 0 .625rem;
  }

  /* 收藏弹窗 */
  .alert_bg {
    z-index: 50;
    background: rgba(0, 0, 0, .7);
    position: fixed;
    width: 100%;
    height: 100vh;
    top: 0;
    left: 0;
  }

  .alert_box {
    z-index: 99;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .alert_inner {
    width: 49.875rem;
    border-radius: 1.25rem;
    background: #ffffff;
    box-sizing: border-box;
    padding: 0 1.25rem;
  }

  .alert_top {
    float: left;
    width: 100%;
    height: 3.375rem;
    border-bottom: .0625rem solid #eee;
  }

  .alert_h1 {
    float: left;
    width: 100%;
    line-height: 3.375rem;
    text-align: center;
    color: #2a2b2a;
    font-size: 1rem;
    font-weight: bold;
  }

  .alert_x {
    float: right;
    width: .9375rem;
    height: .9375rem;
    padding: 1.1875rem;
    margin: -3.125rem -1.125rem 0 0;
  }

  .alert_x:hover {
    cursor: pointer;
  }

  .alert_wrap {
    width: 100%;
    float: left;
    display: flex;
    align-items: center;
    flex-flow: column;
  }

  .alert_tit {
    line-height: 1.3125rem;
    color: #2a2b2a;
    font-size: 1rem;
    margin: 2.4375rem 0 1rem;
  }

  .alert_fav {
    width: 5.5rem;
    height: 6.5rem;
    margin: 0 0 3.625rem;
  }

  .alert_btns {
    display: flex;
    margin: 0 0 3.625rem;
  }

  .alert_btns div {
    width: 7.625rem;
    line-height: 2.375rem;
    text-align: center;
    border-radius: 1.1875rem;
    font-size: 1rem;
  }

  .alert_btns div:hover {
    cursor: pointer;
  }

  .alert_quit {
    color: #666666;
    background: #f5f5f5;
  }

  .alert_ok {
    color: #DD2A2A;
    background: #fee9e9;
    margin: 0 0 0 2.125rem;
  }
</style>
