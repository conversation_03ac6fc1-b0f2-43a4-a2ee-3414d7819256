<template>
    <photoShoot v-if="fileList.length < 5" :length="fileList.length" @setImg="setImg"></photoShoot>
    <div class="upload-container">
      <div class="img-box"  v-for="(item, index) in fileList" :key="index">
        <img class="img-item" :src="item.url" />
        <img @click="handleDel(index)" class="del-img" src="@/assets/img/percision/del-icon.png" />
        <div @click="showImg(item.url)" class="mask"></div>
      </div>
      <div class="upload-box" v-show="fileList.length < 5">
        <el-upload
            class="upload-demo"
            v-model:file-list="list"
            :http-request="uploadFiles"
            :on-remove="handleRemove"
            multiple
            :limit="limit"
            :show-file-list="false"
            accept="image/png, image/jpeg,image/jpg"
        >
            <div class="upload-btn"> + 上传本地照片（{{ fileList.length }} / {{ limit }}）</div>
        </el-upload>
      </div>
    </div>
    <el-image-viewer
        v-if="showPreview"
        :url-list="srcList"
        show-progress
        :initial-index="0"
        @close="closeImg"
      />
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import photoShoot from '@/components/photoShoot/index.vue'
import type { UploadProps, UploadUserFile } from 'element-plus'
import { uploadApi } from "@/api/user"
const props = defineProps({
  imgList: {
    type: Array as () => any[],
    default: () => []
  },
  index: {
    type: Number,
    default: 0
  },
  limit: {
    type: Number,
    default: 5
  }
})
const emit = defineEmits(['getImgList'])
const fileList = ref<UploadUserFile[]>([])
const list = ref<UploadUserFile[]>([])
const showPreview = ref(false)
const srcList = ref([] as any[])
watch(
  () => props.imgList,
  (newVal) => {
    fileList.value = newVal?.map((item, ind) => ({
      name: `img${ind}.png`,
      url: item.url,
    })) || [];
  },
  { immediate: true }
);
const handleDel = (index: number) => {
  fileList.value.splice(index, 1)
  list.value.splice(index, 1)
  emit('getImgList', props.index, fileList.value)
}
const handleRemove: UploadProps['onRemove'] = (uploadFile, uploadFiles) => {
  console.log(uploadFile, uploadFiles)
}
const showImg = (url: any) => {
  showPreview.value = true
  srcList.value = [url]
}
const closeImg = () => {
  showPreview.value = false
  srcList.value = []
}
const uploadFiles = async (e: any) => {
  try {
    const formdata = new FormData()
    console.log(e.file)
    formdata.append("file", e.file)
    const res = (await uploadApi(formdata)) as any
    const { data = {} } = res
    fileList.value.push({
      name: data.key,
      url: data.url + data.key
    })
    list.value = fileList.value
    emit('getImgList', props.index, fileList.value)
  } catch (err) {
    console.log(err)
  }
}
const setImg = (data: any) => {
  fileList.value.push({
    name: data.key,
    url: data.url + data.key
  })
  list.value = fileList.value
  emit('getImgList', props.index, fileList.value)
}
</script>
<style lang="scss" scoped>
.upload-container {
    display: flex;
    position: relative;
    .img-box {
        width: 10.8125rem;
        height: 10.8125rem;
        border-radius: .25rem;
        position: relative;
        margin-right: .625rem;
        .img-item {
            width: 100%;
            height: 100%;
        }
        .del-img {
            position: absolute;
            top: .625rem;
            right: .625rem;
            width: 1.625rem;
            height: 1.625rem;
            display: none;
            border-radius: 50%;
        }
        .mask {
            background: #00000080;
            display: none;
            width: 100%;
            height: 100%;
            position: absolute;
            border-radius: .25rem;
            top: 0;
            left: 0;
        }
        &:hover {
            .mask {
                display: block;
                z-index: 10;
            }
            .del-img {
                display: block;
                z-index: 11;
                cursor: pointer;
            }
        }
    }
    .upload-box {
        position: absolute;
        right: 0;
        .upload-btn {
            padding: .375rem .625rem;
            border-radius: .25rem;
            border: .0625rem solid #dddddd;
            background: #ffffff;
            color: #666666;
            font-size: .875rem;
            font-weight: 400;
            position: absolute;
            right: 0;
            top: -44px;
            width: 145px;
        }
    }
}
</style>
