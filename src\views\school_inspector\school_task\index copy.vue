<template>
    <div class="supervisor-main">
    <div class="supervisor-page">
     <!-- <div class="stap">
      <img src="@/assets/img/note/stap.png" class="stap_lt" />
      <img src="@/assets/img/note/stap.png" class="stap_rt" />
    </div> -->
      <!-- 左侧信息栏 -->
      <aside class="sidebar">
        <div class="profile-card">
          <img class="avatar" :src="userInfo.avatar" alt="avatar" />
          <div class="name">{{ userInfo.nickName }}</div>
          <div class="info">{{ userInfo.gradeText }} | {{ userInfo.termText }} | {{ userInfo.academicText }}</div>
          <div class="school-info-btn">
            <img src="@/assets/img/entranceAssessment/grade.png" width="14px" height="14px" alt="">
            {{ userInfo.orgName }} | {{ userInfo.gradeText }}1班
          </div>
        </div>
        <div class="textbook-version">教材版本</div>
        <ul class="menu">
          <li v-for="item in subjectList" :key="item.subject" >
            <svg width="16" height="16" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg"><path fill="currentColor" d="M128 160h384v128H128zm0 256h256v128H128zm0 256h512v128H128zm640-384h128v128H768zm0 256h128v128H768zm0-512h128v128H768zM128 64h64v576H128z"/></svg>
            {{ item.subjectName }}
            <span class="vertical"></span>
            <span class="textbook-link">{{ item.editionName }}</span>
          </li>
        </ul>
      </aside>
  
      <!-- 右侧任务栏 -->
      <main class="task-main-container">

        <!-- 标题区块 -->
        <div class="task-header-block">
          <div class="task-list-header">
            <h2>你当前的督学任务</h2>
            <a href="#" class="view-more" v-if="!noTasks" @click.prevent="goToTaskListPage">查看更多 ></a>
          </div>
        </div>
        
        <!-- 内容区块 -->
        <div class="task-content-block" @scroll="handleScroll" ref="scrollContainer">
          <div v-if="noTasks" class="no-tasks-placeholder">
            <p>督学任务空空如也</p>
          </div>

          <template v-else>
            <section v-for="subject in displayedTasks" :key="subject.subject" class="subject-section">
              <h3 class="subject-title">
                <span class="subject-icon"></span>
                {{ subject.subject }}
              </h3>
              <div class="task-list-container">
                <div v-for="(group, groupIndex) in subject.groups" :key="groupIndex" class="task-group">
                    <div class="task-group-header" v-if="group.typeName">
                        <div class="header-left">
                            <span class="tag lesson-tag" v-if="group.lesson">{{ group.lesson }}</span>
                            <span class="tag type-tag">{{ group.typeName }}</span>
                        </div>
                        <div class="header-right" v-if="group.teacher || group.time || group.remainingTime || group.deadline">
                            <span class="teacher" v-if="group.teacher">{{ group.teacher }}</span>
                            <span class="publish-time" v-if="group.time">布置时间: {{ group.time }}</span>
                            <span v-if="group.remainingTime" class="remaining-time">
                                <img class="time-icon" src="@/assets/img/percision/time.png" alt="time">
                                <span class="remaining-time-label">剩余时间:</span>
                                <span class="time-box">{{ group.remainingTime.hours }}</span>:
                                <span class="time-box">{{ group.remainingTime.minutes }}</span>:
                                <span class="time-box">{{ group.remainingTime.seconds }}</span>
                            </span>
                            <span v-if="group.deadline" class="deadline-time">截止时间: {{ group.deadline }}</span>
                        </div>
                    </div>
                    <div class="task-items">
                        <div v-for="task in group.tasks" :key="task.id" class="task-item">
                            <div class="task-item-left">
                                <img class="task-icon" :src="task.icon" @error="handleImageError" alt="icon">
                                <span class="task-title">{{ task.title }}</span>
                            </div>
                            <div class="task-item-right">
                                <a href="#" class="go-to-task">去查看 ></a>
                            </div>
                        </div>
                    </div>
                </div>
              </div>
            </section>
          </template>
        </div>
      </main>
    </div>
    </div>

  </template>
  
  <script lang="ts" setup>
  import { ref, computed, onMounted, onBeforeUnmount,reactive } from 'vue';
  import { useRouter } from 'vue-router';
  import { taskHomepageApi, taskListApi } from "@/api/video"
  import { storeToRefs } from 'pinia'
  import { useUserStore } from "@/store/modules/user"
  const userStore = useUserStore()
  const { learnNow } = storeToRefs(userStore)
  const router = useRouter();
  const state = reactive({
    visible: false,
    id: ""
  })
  interface Task {
    id: number;
    icon: string;
    title: string;
  }

  interface TaskGroup {
    lesson?: string;
    typeName?: string;
    teacher?: string;
    time?: string;
    deadline?: string;
    remainingTime?: {
        hours: string;
        minutes: string;
        seconds: string;
    };
    tasks: Task[];
  }

  interface SubjectTasks {
    subject: string;
    groups: TaskGroup[];
  }

  const getIcon = (type: 'video' | 'ai' | 'doc' | 'ppt' | 'mp4' | 'jpg' | 'word') => {
    const icons = {
        video: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyOCIgaGVpZ2h0PSIyOCIgdmlld0JveD0iMCAwIDI4IDI4Ij4KICA8Y2lyY2xlIGN4PSIxNCIgY3k9IjE0IiByPSIxMyIgZmlsbD0iI0ZGRjdFMSIgdHJhbnNmb3JtPSJyb3RhdGUoLTkwIDE0IDE0KSIvPgogIDxwYXRoIGQ9Ik0xMS41IDguNjY5ODdMMTguNSA4LjY2OTg4VjE5LjMzMDFMMTEuNSAxOS4zMzAxVjguNjY5ODdaIiBmaWxsPSIjRkZGN0UxIi8+CiAgPHBhdGggZD0iTTE5LjI1IDE0TDExLjUgMTguMzMwMXYtOC42NjAyTDE5LjI1IDE0WiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+`,
        ai: `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgiIGhlaWdodD0iMjgiIHZpZXdCb3g9IjAgMCAyOCAyOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTQiIGN5PSIxNCIgcj0iMTMiIGZpbGw9IiM0RUNBQUEiLz4KPHJlY3QgeD0iOCIgeT0iOSIgd2lkdGg9IjEyIiBoZWlnaHQ9IjEwIiByeD0iMiIgZmlsbD0id2hpdGUiLz4KPHBhdGggZD0iTTExIDEyLjVIMTcuNU0xMSAxNS41SDE2IiBzdHJva2U9IiM0RUNBQUEiIHN0cm9rZS1widthD0iMS4yIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPC9zdmc+`,
        doc: `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgiIGhlaWdodD0iMjgiIHZpZXdCb3g9IjAgMCAyOCAyOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTQiIGN5PSIxNCIgcj0iMTMiIGZpbGw9IiM0RUNBQUEiLz4KPHJlY3QgeD0iOCIgeT0iOSIgd2lkdGg9IjEyIiBoZWlnaHQ9IjEwIiByeD0iMiIgZmlsbD0id2hpdGUiLz4KPHBhdGggZD0iTTExIDEyLjVIMTcuNU0xMSAxNS41SDE2IiBzdHJva2U9IiM0RUNBQUEiIHN0cm9rZS1widthD0iMS4yIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPC9zdmc+`,
        ppt: `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgiIGhlaWdodD0iMjgiIHZpZXdCb3g9IjAgMCAyOCAyOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTQiIGN5PSIxNCIgcj0iMTMiIGZpbGw9IiNGRjg3MUMiLz4KPHBhdGggZD0iTTguNSA4SDIwQzIwLjU1MjMgOCAyMSA4LjQ0NzcyIDIxIDlWMTlDMjEgMTkuNTUyMyAyMC41NTIzIDIwIDIwIDIwSDguNUM4LjIyMzU1IDIwIDcuNzUgMTkuNTUyMyA3Ljc1IDE5VjguNzVDNy43NSA4LjMyMjg0IDguMDczMSA4IDguNSA4WiIgZmlsbD0id2hpdGUiLz4KPHRleHQgeD0iMTAiIHk9IjE2IiBmaWxsPSIjRkY4NzFDIiBmb250LXNpemU9IjYiIGZvbnQtd2VpZ2h0PSI5MDAiPkI8L3RleHQ+Cjwvc3ZnPg==`,
        mp4: `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgiIGhlaWdodD0iMjgiIHZpZXdCb3g9IjAgMCAyOCAyOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTQiIGN5PSIxNCIgcj0iMTMiIGZpbGw9IiM3NkVCRkYiLz4KPHJlY3QgeD0iOCIgeT0iOSIgd2lkdGg9IjEyIiBoZWlnaHQ9IjEwIiByeD0iMiIgZmlsbD0id2hpdGUiLz4KPHBhdGggZD0iTTE3Ljc1IDE0TDExLjI1IDE3LjczMlYxMC4yNjhMMTcuNzUgMTRaIiBmaWxsPSIjNzZFQkZGIi8+Cjwvc3ZnPg==`,
        jpg: `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgiIGhlaWdodD0iMjgiIHZpZXdCb3g9IjAgMCAyOCAyOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTQiIGN5PSIxNCIgcj0iMTMiIGZpbGw9IiNGQ0M1NEYiLz4KPHBhdGggZD0iTTguNSA5QzguNSA4LjQ0NzcyIDguOTQ3NzIgOCA5LjUgOEgxOC41QzE5LjA1MjMgOCAxOS41IDguNDQ3NzIgMTkuNSA5VjE5QzE5LjUgMTkuNTUyMyAxOS4wNTIzIDIwIDE4LjUgMjBICS41QzguOTQ3NzIgMjAgOC41IDE5LjU1MjMgOC41IDE5VjlaIiBmaWxsPSJ3aGl0ZSIvPgo8Y2lyY2xlIGN4PSIxMSIgeD0iMTIiIHI9IjEiIGZpbGw9IiNGQ0M1NEYiLz4KPHBhdGggZD0iTTguMjUgMjAuMjVMMTMuNzUgMTIuNzVMMTkuNzUgMTcuMjUiIHN0cm9rZT0iI0ZDQzU0RiIgc3Ryb2tlLXdpZHRoPSIxLjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4=`,
        word: `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgiIGhlaWdodD0iMjgiIHZpZXdCb3g9IjAgMCAyOCAyOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTQiIGN5PSIxNCIgcj0iMTMiIGZpbGw9IiM1Njk1RkYiLz4KPHBhdGggZD0iTTguNSA4SDIwQzIwLjU1MjMgOCAyMSA4LjQ0NzcyIDIxIDlWMTlDMjEgMTkuNTUyMyAyMC41NTIzIDIwIDIwIDIwSDguNUM4LjIyMzU1IDIwIDcuNzUgMTkuNTUyMyA3Ljc1IDE5VjguNzVDNy43NSA4LjMyMjg0IDguMDczMSA4IDguNSA4WiIgZmlsbD0id2hpdGUiLz4KPHBhdGggZD0iTTEwIDE1TDEwLjUgMTMuNUwxMSAxNSBNMTIgMTBMMTAuMjUgMTVIMTEuNzVMMTMuNSAxMEgxMiZaIiBmaWxsPSIjNTY5NUZGIiBzdHJva2U9IiM1Njk1RkYiIHN0cm9rZS13aWR0aD0iMC41IiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxwYXRoIGQ9Ik0xNy41IDE1TDE2IDEwSDE0LjVMMTYuMjUgMTVIMTcuNVoiIGZpbGw9IiM1Njk1RkYiIHN0cm9rZT0iIzU2OTVGRiIgc3Ryb2tlLXdpZHRoPSIwLjUiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+`
    };
    return icons[type];
  };

  // 年级映射
  const gradeMap: Record<number, string> = {
    1: '一年级',
    2: '二年级', 
    3: '三年级',
    4: '四年级',
    5: '五年级',
    6: '六年级',
    7: '七年级',
    8: '八年级',
    9: '九年级',
    10: '十年级',
    11: '十一年级',
    12: '十二年级'
  };

  // 学期映射
  const termMap: Record<number, string> = {
    1: '上学期',
    2: '下学期'
  };

  // 学制映射
  const academicMap: Record<number, string> = {
    1: '六三制',
    2: '五四制'
  };

  // 用户信息计算属性
  const userInfo = computed(() => {
    if (!learnNow.value) {
      return {
        avatar: 'https://xiaoyin-test.obs.cn-south-1.myhuaweicloud.com/education/assets/img/1.0/vip/head-boy.svg',
        nickName: '用户',
        gradeText: '七年级',
        termText: '上学期',
        academicText: '六三制',
        orgName: '学校'
      };
    }
    
    return {
      avatar: learnNow.value.avatar || 'https://xiaoyin-test.obs.cn-south-1.myhuaweicloud.com/education/assets/img/1.0/vip/head-boy.svg',
      nickName: learnNow.value.nickName || '用户',
      gradeText: gradeMap[learnNow.value.gradeId] || '七年级',
      termText: termMap[learnNow.value.termId] || '上学期', 
      academicText: academicMap[learnNow.value.academic] || '六三制',
      orgName: learnNow.value.orgName || '学校'
    };
  });

  // 科目列表计算属性
  const subjectList = computed(() => {
    if (!learnNow.value?.versions) {
      return [];
    }
    
    return learnNow.value.versions.map((version: any) => ({
      subject: version.subject,
      subjectName: version.subjectName,
      editionName: version.editionName,
      bookId: version.bookId
    }));
  });

  const allTasks = ref<SubjectTasks[]>([
    {
      subject: '数学',
      groups: [
        {
          lesson: '第1课时',
          typeName: '预习任务',
          teacher: '王老师',
          time: '2025/01/07 09:12',
          tasks: [
            { id: 1, icon: getIcon('video'), title: '名师同步学: 第一章 有理数--1.1正数和负数' },
            { id: 2, icon: getIcon('video'), title: '名师知识点: 视频名称视频名称视频名称视频名称' },
          ]
        },
        {
          lesson: '第1课时',
          typeName: '课堂随练',
          teacher: '王老师',
          time: '2025/01/07 09:12',
          remainingTime: { hours: '02', minutes: '49', seconds: '49' },
          tasks: [
            { id: 3, icon: getIcon('ai'), title: 'AI一键智能作业: 第一章 有理数--1.1正数和负数' },
            { id: 4, icon: getIcon('doc'), title: '作业名称作业名称作业名称作业名称' },
            { id: 5, icon: getIcon('doc'), title: '作业名称作业名称作业名称作业名称' },
          ]
        },
        {
          lesson: '第1课时',
          typeName: '复习任务',
          teacher: '王老师',
          time: '2025/01/07 09:12',
          deadline: '2025/01/07 12:00',
          tasks: [
            { id: 6, icon: getIcon('ppt'), title: '我的资源: 名称名称名称名称.ppt' },
            { id: 7, icon: getIcon('mp4'), title: '春天到了, 又到了动物们繁殖的季节.mp4' },
            { id: 8, icon: getIcon('jpg'), title: '春天又到了, 又双叒叕到了动物们繁殖的季节.jpg' },
            { id: 9, icon: getIcon('doc'), title: '作业名称作业名称作业名称作业名称' },
          ]
        },
        {
          lesson: '第2课时',
          typeName: '预习任务',
          teacher: '王老师',
          time: '2025/01/07 09:12',
          tasks: [
            { id: 10, icon: getIcon('video'), title: '名师同步学: 第一章 有理数--1.1正数和负数' },
          ]
        },
        {
          lesson: '第2课时',
          typeName: '复习任务',
          teacher: '王老师',
          time: '2025/01/07 09:12',
          deadline: '2025/01/07 12:00',
          tasks: [
            { id: 11, icon: getIcon('word'), title: '春天又到了, 又双叒叕到了动物们繁殖的季节.doc' },
          ]
        },
        {
          typeName: '个性化任务',
          teacher: '王老师',
          time: '2025/01/07 09:12',
          deadline: '2025/01/07 12:00',
          tasks: [
            { id: 12, icon: getIcon('doc'), title: '任务名称任务名称任务名称任务名称任务名称' },
            { id: 13, icon: getIcon('doc'), title: '任务名称任务名称任务名称任务名称任务名称' },
          ]
        },
      ]
    },
     {
      subject: '语文',
       groups: [
        {
          lesson: '第1课时',
          typeName: '预习任务',
          teacher: '李老师',
          time: '2025/01/08 10:00',
          tasks: [
            { id: 14, icon: getIcon('video'), title: '名师同步学: 第二章 文言文' },
          ]
        },
      ]
    },
    { subject: '英语', groups: [] },
    { subject: '道德', groups: [] },
    { subject: '历史', groups: [] },
    { subject: '地理', groups: [] },
  ]);

  const noTasks = computed(() => displayedTasks.value.length === 0);

  const goToTaskListPage = () => {
    console.log('Navigating to full task list page...');
 
    router.push({ name: 'InspectorTasks' })
  };

  const handleImageError = (event: Event) => {
    const target = event.target as HTMLImageElement;
    target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgiIGhlaWdodD0iMjgiIHZpZXdCb3g9IjAgMCAyOCAyOCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBmaWxsPSIjZGVkZWRlIiBkPSJNMCAwaDI4djI4SDB6Ii8+PC9zdmc+';
  };

  const scrollContainer = ref<HTMLElement | null>(null);
  const displayedTasks = ref<SubjectTasks[]>([]);
  let taskIndex = 0;
  const tasksPerLoad = 2;

  const loadMore = () => {
    if (taskIndex >= allTasks.value.length) {
      return;
    }
    const nextTasks = allTasks.value.slice(taskIndex, taskIndex + tasksPerLoad);
    displayedTasks.value.push(...nextTasks);
    taskIndex += tasksPerLoad;
  };

  const handleScroll = () => {
    const container = scrollContainer.value;
    if (container) {
      const isAtBottom = container.scrollTop + container.clientHeight >= container.scrollHeight - 10;
      if (isAtBottom) {
        loadMore();
      }
    }
  };

  onMounted(() => {
    console.log(learnNow.value,"learnNowlearnNowlearnNow")
    getTaskList()
    getTaskHomepage()
    loadMore();
    const container = scrollContainer.value;
    if (container) {
        container.addEventListener('scroll', handleScroll);
    }
  });

  const getInfor = async () =>{
    const res: any = await taskHomepageApi()

    if (res.code == 200) { 
      console.log( res,"打印一下 res")
    }
  }

  // 督学任务列表
  const getTaskList = async () =>{
    const data:any ={
      status:0,
      xk:"math"
    }
    const res: any = await taskListApi(data)
    if (res.code == 200) { 
      console.log( res,"打印一下 res")
    }
  }
  // 督学列表
  const getTaskHomepage = async () =>{
    const res: any = await taskHomepageApi()
    if (res.code == 200) { 
      console.log( res,"打印一下 res")
    }
  }
  onBeforeUnmount(() => {
    const container = scrollContainer.value;
    if (container) {
        container.removeEventListener('scroll', handleScroll);
    }
  });

  </script>
  
<style lang="scss" scoped>
.supervisor-main {
  width: 100vw;
  height: 100vh;
  background: #f0f2f5;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
  box-sizing: border-box;
}

.supervisor-page {
  width: 100%;
  max-width: 1400px;
  height: 100%;
  display: flex;
  position: relative;
}

.stap {
  position: absolute;
  top: -2px;
  left: 20px;
  width: calc(100% - 40px);
  display: flex;
  justify-content: space-between;
  z-index: 1;
  pointer-events: none;

  img {
    height: 40px;
  }
  .stap_lt {
    margin-left: 230px;
  }
  .stap_rt {
    margin-right: -10px;
  }
}

.sidebar {
  width: 250px;
  flex-shrink: 0;
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-right: 15px;
  height: 100%;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);

  .profile-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
    margin-top: 20px;
    border-bottom: 1px solid #EAEAEA;
    .avatar {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      margin-bottom: 10px;
    }

    .name {
      font-weight: bold;
      font-size: 16px;
    }

    .info {
      font-size: 12px;
      color: #666;
      margin: 5px 0;
    }

    .school-info-btn {
      width:90%;
      background-color: #EEF3FD;
      border: none;
      padding: 5px 10px;
      border-radius: 15px;
      font-size: 12px;
      color: #5A85EC;
      margin-top: 5px;
      text-align: center;
      margin-bottom: 40px;
      svg {
        margin-right: 5px;
      }
    }
  }

  .textbook-version {
    color: #333;
    font-size: 14px;
    margin-bottom: 10px;
  }

  .menu {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      display: flex;
      align-items: center;
      padding: 15px 10px;
      cursor: pointer;
      border-radius: 5px;
      font-size: 14px;
      color: #333;
      transition: background-color 0.3s;

      // &:hover,
      // &.active {
      //   background-color: #f0f7ff;
      //   color: #4a90e2;
      // }

      svg {
        margin-right: 10px;
      }

      .vertical {
        border-left: 1px solid #e0e0e0;
        height: 16px;
        margin: 0 10px;
      }
      .textbook-link {
        color: #5a85ec;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

.task-main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.task-header-block {
  background-color: #ffffff;
  padding: 15px 20px;
  border-radius: 8px;
  margin-bottom: 15px;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.task-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h2 {
    margin: 0;
    font-size: 18px;
  }

  .view-more {
    color: #4a90e2;
    text-decoration: none;
    font-size: 14px;
  }
}

.task-content-block {
  flex: 1;
  background-color: #ffffff;
  // padding: 20px;
  border-radius: 8px;
  overflow-y: auto;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
  }
}

.no-tasks-placeholder {
  text-align: center;
  padding-top: 100px;
  color: #999;
  
  p {
    font-size: 16px;
  }
}

.subject-section {
  margin-bottom: 25px;
  &:last-of-type {
    margin-bottom: 0;
  }
}

.subject-title {
  padding: 0;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  color: #333;
  background: #eef3fd;

  .subject-icon {
    display: inline-block;
    width: 6px;
    height: 18px;
    background-color: #4a90e2;
    margin-right: 12px;
    border-radius: 2px;
  }
}

.task-list-container {
  padding-left: 18px;
}

.task-group {
    margin-bottom: 20px;
}

.task-group-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;

    .header-left {
        display: flex;
        align-items: center;
    }

    .header-right {
        display: flex;
        align-items: center;
        gap: 15px;
        
        .teacher {
            color: #666;
            font-size: 13px;
        }
        
        .publish-time {
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 12px;
            background-color: #FFFBE6;
            color: #D48806;
            white-space: nowrap;
        }

        .remaining-time {
            display: flex;
            align-items: center;
            white-space: nowrap;
            color: #ff7d00;
            
            .time-icon {
                width: 16px;
                height: 16px;
                margin-right: 6px;
            }
            .remaining-time-label {
                margin-right: 8px;
                font-size: 12px;
            }
            .time-box {
                background-color: #555;
                color: white;
                padding: 3px 5px;
                border-radius: 3px;
                font-weight: bold;
                font-size: 12px;
                margin: 0 2px;
                min-width: 20px;
                text-align: center;
            }
        }

        .deadline-time {
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 12px;
            background-color: #FFF1F0;
            color: #CF1322;
            white-space: nowrap;
        }
    }

    .tag {
        padding: 3px 12px;
        border-radius: 12px;
        font-size: 12px;
        margin-right: 10px;
        border: 1px solid transparent;
    }

    .lesson-tag {
        border-color: #4a90e2;
        color: #4a90e2;
        background-color: #f0f7ff;
    }

    .type-tag {
        border-color: #dcdfe6;
        color: #606266;
        background-color: #f4f4f5;
    }
}

.task-items {
  background-color: #fafbfc;
  border-radius: 8px;
  padding: 5px 20px;
  border: 1px solid #f0f0f0;
}

.task-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 18px 0;
    border-bottom: 1px dashed #e8e8e8;
    transition: all 0.2s ease-in-out;

    &:last-child {
        border-bottom: none;
    }

    &:hover {
        background-color: #f5f7fa;
        margin: 0 -20px;
        padding: 18px 20px;
        border-radius: 4px;
        border-bottom-color: transparent;
    }
}

.task-item-left {
    display: flex;
    align-items: center;
    gap: 15px;

    .task-icon {
        width: 28px;
        height: 28px;
        flex-shrink: 0;
    }

    .task-title {
        font-size: 14px;
        color: #333;
    }
}

.task-item-right {
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 13px;
    color: #888;
    flex-shrink: 0;



    .tag-like {
      padding: 4px 10px;
      border-radius: 12px;
      font-size: 12px;
      white-space: nowrap;
    }


    


    .go-to-task {
        color: #4a90e2;
        text-decoration: none;
        font-size: 14px;
        font-weight: 500;
        margin-left: 10px;
    }
}
</style>