<!-- 学习用户列表-家长信息 -->
<template>
  <div class="content">
    <div class="header">
      <div class="inner">
        <div class="logo">
          <img src="@/assets/img/login/logo.png" class="logo_img" />
          <div class="logo_h1">AI伴学</div>
        </div>
      </div>
    </div>
    <div class="head">
      <div class="inner">
        <!-- 平板没有 -->
        <!-- <div class="head_img">
          <img :src="state.userInfo.avatar" v-if="state.userInfo.avatar">
          <img src="@/assets/img/login/logo.png" v-else>
          <div v-if="state.memberInfo">{{state.memberInfo.memberType=='vip'?'VIP':state.memberInfo.memberType=='svip'?'SVIP':state.memberInfo.memberType}}</div>
        </div>
        <div class="head_name">{{state.userInfo.name||''}}</div>
        <div class="head_btn" onclick="document.querySelector('.file').click()">
          <img src="@/assets/img/user/edit2.svg" />修改头像
          <input type="file" id="file" accept="image/png, image/jpeg,image/jpg" class="file none">
        </div>
        <div class="head_btn">
          <img src="@/assets/img/user/head.svg" />修改昵称
        </div> -->
        <div class="hd_back" @click="goToKnowledgeGraph()">< 返回</div>
        <div class="quit" @click="quitShow" style="margin-top: .625rem;">
          <img src="@/assets/img/user/quit.svg" />退出登录
        </div>
      </div>
    </div>
    <div class="inner">
      <div class="wrap">
        <div class="info_lt">
          <img src="@/assets/img/user/pttag.png" class="pt_tag" />
          <div class="pt_white"></div>
          <!-- <div class="pt_lab">
            昵称：{{state.userInfo.name || ''}}
          </div> -->
          <div class="pt_lab">
            账号：{{state.userInfo.phone2}}
          </div>
          <div class="pt_lab2" v-if="state.memberInfo">
            <div>
              用户类型：{{state.memberInfo.memberType=='vip'?'VIP':state.memberInfo.memberType=='svip'?'VIP':state.memberInfo.memberType}}用户
            </div>
            <div>（{{state.memberInfo.expirationTime}}到期）</div>
          </div>
          <!-- <div class="pt_lab2">
            <div>自习室用户：自习室会员</div>
            <div>（2025/02/26 09:42:16到期）</div>
          </div> -->
          <template v-else>
            <div class="pt_lab2">
              <div>
                用户类型：VIP用户<!-- 注册用户 -->
              </div>
            </div>
            <!-- <div class="buy_btn" @click="vipAlertShow">购买会员</div> -->
          </template>
          <div v-if="!orgName">
            <div class="pt_code">
              <div class="pt_qr">
                <img :src="state.qrcode1" :class="state.qrcode1?'':'opac'" />
              </div>
              <div class="pt_scan">扫码进入“伴学家长端”小程序，<br/>可同步查看学生学情报告</div>
            </div>
            <div class="agent">
              <router-link class="treaty" :to="'/user_agent'">《平台用户协议》</router-link><router-link class="treaty"
                :to="'/user_agent2'">《隐私政策》</router-link>
            </div>
          </div>
          <!-- <div class="warning">
            <div class="warn_h1">
              <img src="@/assets/img/user/warning.svg" />温馨提示
            </div>
            <div class="warn_tip">
              家长也可以用本账号登录小优精准教学平台，给孩子布置作业。
            </div>
            <div class="addr">
              网址：<a href="http://tiku.xiaoyeoo.com" target="_blank" class="addr_link">tiku.xiaoyeoo.com</a>
            </div>
          </div> -->
        </div>
        <div class="info_rt">
          <div class="learn_tip">
            欢迎您，本账号已有{{state.list.length}}个学生用户已同步，请选择当前学生用户后进入学习功能。
          </div>
          <div class="lean_h1">学生用户</div>
          <div class="lean_ul">
            <!-- 当前学生 -->
            <template v-for="(item,i) in state.list" :key="item.learnId">
              <template v-if="item.learnId == state.usersNow.learnId">
                <div class="lean_li active">
                  <div class="lean_top">
                    <img :src="item.avatar" class="lean_pic" v-if="item.avatar" />
                    <img src="@/assets/img/login/logo.png" class="lean_pic" v-else />
                    <div class="lean_name">
                      <div class="learn_nick">
                        <span class="nowrap">{{ item.nickName || '' }}</span>
                        <span>（{{ item.gradeName }}）</span>
                      </div>
                      <div class="learn_id">ID：{{ item.learnNo }}</div>
                    </div>
                    <template v-if="item.learnId == state.usersNow.learnId">
                      <div class="lean_now">当前学生</div>
                      <!-- 开始学习 -->
                      <img src="@/assets/img/user/study.png" class="leaan_go" @click="goToKnowledgeGraph()" />
                    </template>
                    <div class="leaan_change" @click="changeId(i)" v-else>切换学生</div>
                    <div class="lean_edit" v-if="item.isStudyRoomStudent" style="color: #999;">
                      自习室用户,由老师修改信息
                    </div>
                    <div class="lean_edit" @click="learnEdit(item.learnId)" v-else>
                      <img src="@/assets/img/user/edit.svg" />编辑信息
                    </div>
                  </div>
                  <div class="lean_book">
                    <div v-for="item2 in item.versions" :key="item2.bookId">
                      {{item.gradeId<7?'小学':item.gradeId<10?'初中':'高中'}}{{ item2.subjectName }}({{ item2.editionName + (item2.typeName || '') }})
                    </div>
                  </div>
                </div>
              </template>
            </template>
            <!-- 非当前 -->
            <template v-for="(item,i) in state.list" :key="item.id">
              <template v-if="item.learnId != state.usersNow.learnId">
                <div class="lean_li">
                  <div class="lean_top">
                    <img :src="item.avatar" class="lean_pic" v-if="item.avatar" />
                    <img src="@/assets/img/login/logo.png" class="lean_pic" v-else />
                    <div class="lean_name">
                      <div class="learn_nick">
                        <span class="nowrap">{{ item.nickName || '' }}</span>
                        <span>（{{ item.gradeName }}）</span>
                      </div>
                      <div class="learn_id">ID：{{ item.learnNo }}</div>
                    </div>
                    <template v-if="item.learnId == state.usersNow.learnId">
                      <div class="lean_now">当前学生</div>
                      <!-- 开始学习 -->
                      <img src="@/assets/img/user/study.png" class="leaan_go" @click="goToKnowledgeGraph()" />
                    </template>
                    <div class="leaan_change" @click="changeId(i)" v-else>切换学生</div>
                    <div class="lean_edit" v-if="item.isStudyRoomStudent" style="color: #999;">
                      自习室用户,由老师修改信息
                    </div>
                    <div class="lean_edit" @click="learnEdit(item.learnId)" v-else>
                      <img src="@/assets/img/user/edit.svg" />编辑信息
                    </div>
                  </div>
                  <div class="lean_book">
                    <div v-for="item2 in item.versions" :key="item2.bookId">
                      {{item.gradeId<7?'小学':item.gradeId<10?'初中':'高中'}}{{ item2.subjectName }}({{ item2.editionName + (item2.typeName || '') }})
                    </div>
                  </div>
                </div>
              </template>
            </template>
          </div>
          <!-- <div class="addbtn" v-if="state.list.length<3" @click="learnAdd">添加学生</div> -->
        </div>
      </div>
    </div>
    <!-- 修改昵称 -->
    <div class="none">
      <div class="alert_bg"></div>
      <div class="alert_box">
        <div class="alert_inner">
          <div class="alert_top">
            <div class="alert_h1">修改昵称</div>
            <img src="@/assets/img/user/close.svg" class="alert_x" />
          </div>
          <div class="alert_wrap">
            <div class="alert_form">
              <div class="alert_item">
                <div class="alert_label">昵称</div>
                <el-input class="alert_input" autocomplete="off" placeholder="请输入昵称（最多不超过8个字符）"></el-input>
              </div>
              <div class="alert_foot">
                <el-button type="primary" class="alert_ok">确定</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 退出弹窗 -->
  <div class="quit_box" v-if="state.isQuit">
    <div class="alert_bg"></div>
    <div class="alert_box">
      <div class="alert_inner">
        <div class="alert_top">
          <div class="alert_h1">提示</div>
          <img src="@/assets/img/user/close.svg" class="alert_x" @click="quitHide" />
        </div>
        <div class="alert_wrap">
          <div class="alert_tit">确定要退出当前账号吗？</div>
          <div class="alert_btns">
            <div class="alert_quit" @click="quitHide">取消</div>
            <div class="alert_ok" @click="useUserStore().logout()">确定</div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 购买会员弹窗 -->
  <buyVip :show="state.showVip" @close="quitHide"></buyVip>
</template>

<script lang="ts" setup>
  import { reactive, onMounted } from 'vue';
  import router from '@/router/index'
  import { useUserStore } from "@/store/modules/user"
  import { userGetAllApi, userVipInfoApi, userDefaultApi, createAppletCodeApi, exchangeCodeApi, memberOrderByNativeApi, goodsfindByIdApi, getOrderByNativeApi } from "@/api/user"
  import { gradeNameList } from '@/utils/user/enum'
  import { ElMessage } from "element-plus"
  import QRCode from 'qrcode';
  import buyVip from "@/views/components/buyVip/index.vue"

  onMounted(() => {
    getList()
    getUserInfo()
    userVipInfo()
    createHomeCode()
  })
  let orgName = localStorage.userInfo?.orgName
  let learnNow = localStorage.learnNow ? JSON.parse(localStorage.learnNow) : ''
  const state : any = reactive({
    list: [],
    usersNow: '',
    learnId: learnNow?.learnId || '',
    isQuit: false,
    userId: localStorage.sysUserId || '',
    userInfo: localStorage.userInfo ? JSON.parse(localStorage.userInfo) : '',
    memberInfo: '',
    qrcode1: '',
    showVip: false,
    showOk: false
  })

  //学生列表
  const getList = () => {
    let learnNow = '', learnUsers = ''
    let deviceLen = 0
    userGetAllApi().then((res : any) => {
      const arr : any = [res.data]
      if (arr.length) {
        for (const i of arr) {
          //获取小程序用户数
          deviceLen++
          i.gradeName = gradeNameList[i.gradeId]
          if (i.isDefault) {
            learnNow = i
          }
        }
        learnUsers = arr
        if (!learnNow) {
          // 默认第一个
          arr[0].isDefault = true
          learnNow = arr[0]
        }
        //  else {
        //   learnNow = JSON.parse(localStorage.learnNow)
        // }
      }
      state.list = learnUsers
      state.usersNow = learnNow
      //缓存学生列表
      learnUsers = JSON.stringify(learnUsers)
      useUserStore().setlearnNow(learnNow)
      useUserStore().learnUsers = learnUsers
      localStorage.learnUsers = learnUsers
    })
  }

  //切换学生
  const changeId = (i : any) => {
    ElMessage.success('切换成功')
    const arr = state.list
    arr[i].isDefault = true
    const { versions, avatar, childId, gradeId, learnId, region, nickName, sex, learnNo } = arr[i]
    const versions2 : any = []
    //处理教材
    for (const n of versions) {
      versions2.push({
        subject: n.subject,
        bookId: n.bookId
      })
    }
    const param = {
      versions: versions2,
      avatar: 'education' + avatar.split('/education')[1],
      nickName,
      childId,
      gradeId,
      gradeNum: learnNo,
      sex,
      region,
      learnId,
      isDefault: true
    }
    userDefaultApi(param).then(() => {
      //缓存默认用户
      const list = state.list
      for (const x of list) {
        x.isDefault = false
        if (x.learnId == learnId) {
          x.isDefault = true
        }
      }
      state.list = list
      state.usersNow = arr[i]
      let learnNow = JSON.stringify(arr[i])
      const learnUsers = JSON.stringify(list)
      useUserStore().learnNow = learnNow
      localStorage.learnNow = learnNow
      useUserStore().learnUsers = learnUsers
      localStorage.learnUsers = learnUsers
    })
  }

  //家长信息-平板没家长修改这块
  const getUserInfo = () => {
    let phone = state.userInfo.phone
    //手机号脱敏
    state.userInfo.phone2 = phone.substr(0, 3) + "****" + phone.substr(7, 4)
    // getUserApi() .then((res : any) => { })
  }
  //会员信息
  const userVipInfo = () => {
    let param = {
      userId: state.userId
    }
    userVipInfoApi(param).then((res : any) => {
      let memberInfo = res.data || '1'//自习室默认有会员
      state.memberInfo = memberInfo
      if (memberInfo) {
        //处理到期时间
        if (memberInfo?.expirationTime) {
          memberInfo.expirationTime = memberInfo.expirationTime.substr(0, 16)
          state.memberInfo = memberInfo
        }
        memberInfo = JSON.stringify(memberInfo)
      }
      localStorage.memberInfo = memberInfo
      useUserStore().memberInfo = memberInfo
    })
  }

  //跳转路由
  const goUrl = (name : string) => {
    router.push({ name })
  }
  
  //跳转到知识图谱页面
  const goToKnowledgeGraph = () => {
    router.push({ name: 'KnowledgeGraph' })
  }

  //添加学生
  const learnAdd = () => {
    router.push({ name: "UserAdd", query: { pageType: 'add' } })
  }

  //编辑学生
  const learnEdit = (learnId : any) => {
    router.push({ name: "UserAdd", query: { pageType: 'edit', learnId } })
  }

  //关闭弹窗
  const quitHide = () => {
    state.isQuit = false
    state.showOk = false
    state.showVip = false
  }
  //显示退出弹窗
  const quitShow = () => {
    state.isQuit = true
  }
  //购买会员弹窗
  const vipAlertShow = () => {
    state.showVip = true
  }

  //购买成功弹窗
  const buyOkShow = () => {
    quitHide()
    state.showOk = true
  }

  //生成小程序-首页
  const createHomeCode = () => {
    let param = {
      page: 'addModule/index/index',
      scene: '__4',
      type: 4,
      appletType: 2 //1.小优同学，2.伴印家长端
    }
    createAppletCodeApi(param).then((res : any) => {
      // blob图片
      state.qrcode1 = window.URL.createObjectURL(res.data)
    })
  }
</script>

<style lang="scss" scoped>
  @import url('@/assets/styles/reset.css');

  .opac {
    opacity: 0;
  }

  .none {
    display: none !important
  }

  .nowrap {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .content {
    width: 100%;
    height: 100vh;
    overflow-y: auto;
    background: #F5F5F5;
  }

  .inner {
    margin: 0 auto;
    width: 81.25rem;
  }

  .header {
    width: 100%;
    background: url(@/assets/img/user/ptbg.png) #fff no-repeat;
    background-size: 120rem 4.375rem;
    height: 4.375rem;
  }

  .logo_img {
    float: left;
    width: 2.875rem;
    height: 2.875rem;
    margin: .75rem .625rem .75rem 0;
  }

  .logo_h1 {
    float: left;
    height: 1.625rem;
    color: #2a2b2a;
    font-size: 1.25rem;
    font-weight: 700;
    margin: 1.375rem 0 0;
  }

  .head {
    width: 100%;
    background: url(@/assets/img/user/ptbg2.png) #fff no-repeat;
    background-size: 120rem 5.75rem;
    height: 5.75rem;
    box-sizing: border-box;
    padding: 1rem 0 0;
    margin: 0 0 1.25rem;
  }

  .hd_back{
    color:#fff;
    font-size: 1.175rem;
    line-height: 1.1875rem;
    margin: 1rem 0 .375rem;
    cursor: pointer;
  }

  .head .inner {
    display: flex;
    align-items: center;
  }

  .head_img {
    width: 3.75rem;
    height: 3.75rem;
    display: flex;
    flex-flow: column;
    align-items: center;
    margin: 0 .625rem 0 0;
  }

  .head_img img {
    width: 3.75rem;
    height: 3.75rem;
    border: .125rem solid #ffffff;
    border-radius: 50%;
    box-sizing: border-box;
  }

  .head_img div {
    width: 2.125rem;
    height: .9375rem;
    line-height: 1rem;
    border-radius: 1.125rem;
    background: linear-gradient(150.8deg, #36e2c2 0%, #00b7d0 100%);
    color: #ffffff;
    font-size: .625rem;
    text-align: center;
    margin: -0.9375rem 0 0;
    position: relative;
    z-index: 1;
  }

  .head_name {
    line-height: 1.3125rem;
    color: #ffffff;
    font-size: 1rem;
    font-weight: 700;
  }

  .head_btn {
    width: 6.0625rem;
    height: 1.75rem;
    border-radius: 1.125rem;
    border: .0625rem solid #ffffff;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: .875rem;
    margin: 0 0 0 1.25rem;
  }

  .head_btn img {
    width: .8125rem;
    height: .8125rem;
    margin: 0 .25rem 0 0;
  }

  .head_btn:hover,
  .quit:hover {
    cursor: pointer;
  }

  .quit {
    margin: 0 0 0 auto;
    width: 6.875rem;
    height: 2.25rem;
    border-radius: .25rem;
    border: .0625rem solid #ffffff;
    color: #ffffff;
    font-size: 1rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .quit img {
    width: 1rem;
    height: 1rem;
    margin: 0 .375rem 0 0;
  }

  /* 家长信息 */
  .info_lt {
    float: left;
    width: 18.9375rem;
    height: calc(100vh - 11.375rem);
    border: .0625rem solid #eaeaea;
    border-bottom: 0;
    box-sizing: border-box;
    background: #ffffff;
    overflow-y: auto;
  }

  .info_lt div {
    float: left;
  }

  .pt_tag {
    width: 11.1875rem;
    height: 3.875rem;
    margin: -0.5rem 0 .375rem -0.875rem;
    position: absolute;
    z-index: 9;
  }

  .pt_white {
    width: 100%;
    height: 3.875rem;
  }

  .pt_lab,
  .pt_lab2 {
    width: 100%;
    line-height: 1.1875rem;
    color: #2a2b2a;
    font-size: .875rem;
    box-sizing: border-box;
    padding: 0 1.25rem;
    margin: 1.25rem 0 0;
  }

  .pt_lab2 {
    margin: 1.25rem 0 0;
  }

  .pt_lab2 div {
    width: 100%;
    height: 1.1875rem;
    line-height: 1.1875rem;
    color: #2A2B2A;
    font-size: .875rem;
  }

  .pt_lab2 div:nth-child(1) {
    margin: 0 0 .625rem;
  }

  .pt_lab2 div:nth-child(2) {
    color: #dd2a2a;
  }

  .buy_btn {
    width: 16.4375rem;
    line-height: 2.5625rem;
    text-align: center;
    border-radius: 1.2813rem;
    background: linear-gradient(180deg, #ffe995 21%, #ffb362 100%);
    margin: .25rem 0 0 1.25rem;
    color: #6b4818;
    font-size: 1rem;
    font-weight: 700;
    cursor: pointer;
  }

  .pt_code {
    width: 100%;
    margin: 1.875rem 0 1.875rem;
  }

  .pt_qr {
    width: 12rem;
    height: 12rem;
    border-radius: 1.875rem;
    border: .0625rem solid #ececec;
    background: #ffffff;
    box-shadow: 0 0 5.625rem 0 #0000001a;
    margin: 0 3.4375rem 1.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .pt_qr img {
    width: 10.25rem;
    height: 10.25rem;
  }

  .pt_scan {
    width: 100%;
    text-align: center;
    line-height: 1rem;
    color: #999999;
    font-size: .75rem;
  }

  .agent {
    width: 100%;
    text-align: center;
    margin: 0 0 1.25rem;
  }

  .treaty {
    line-height: 1.1875rem;
    color: #009c7f;
    font-size: .875rem;
  }

  .warning {
    width: 100%;
    border-top: .0625rem solid #eaeaea;
  }

  .warn_h1 {
    width: 100%;
    box-sizing: border-box;
    padding: 1.1875rem 0 1.5rem 1.875rem;
    line-height: 1rem;
    color: #2a2b2a;
    font-size: .875rem;
    display: flex;
    align-items: center;
    font-weight: bold;
  }

  .warn_h1 img {
    width: .8125rem;
    height: 1rem;
    margin: 0 .375rem 0 0;
  }

  .warn_tip {
    line-height: 1.5;
    color: #2a2b2a;
    font-size: .875rem;
    width: 100%;
    box-sizing: border-box;
    padding: 0 1.4375rem 0 2.1875rem;
  }

  .addr {
    width: 100%;
    line-height: 1.1875rem;
    color: #2a2b2a;
    font-size: .875rem;
    box-sizing: border-box;
    padding: .625rem 1.4375rem .625rem 2.1875rem;
  }

  .addr_link {
    color: #009C7F;
  }

  /* 学生用户 */
  .info_rt {
    float: left;
    width: 61.0625rem;
    height: calc(100vh - 11.375rem);
    border: .0625rem solid #eaeaea;
    border-bottom: 0;
    box-sizing: border-box;
    background: #ffffff;
    overflow-y: auto;
    margin: 0 0 0 1.25rem;
  }

  .learn_tip {
    width: 100%;
    line-height: 3.6875rem;
    color: #2a2b2a;
    font-size: .875rem;
    border-bottom: .0625rem solid #eaeaea;
    box-sizing: border-box;
    padding: 0 1.25rem;
  }

  .lean_h1 {
    width: 100%;
    line-height: 4rem;
    color: #009c7f;
    font-size: 1.25rem;
    font-weight: 700;
    box-sizing: border-box;
    padding: 0 1.25rem;
  }

  .lean_ul {
    width: 100%;
    box-sizing: border-box;
    padding: 0 1.25rem;
  }

  .lean_li {
    width: 100%;
    min-height: 13.5625rem;
    border-radius: .625rem;
    border: .125rem solid #eaeaea;
    margin: 0 0 1.25rem;
    box-sizing: border-box;
    overflow: hidden;
  }

  .lean_li.active {
    border-color: #00C9A3;
  }

  .lean_top {
    width: 100%;
    height: 5rem;
    background: #F5F5F5;
  }

  .lean_li.active .lean_top {
    background: #E5F9F6;
  }

  .lean_pic {
    float: left;
    width: 3.75rem;
    height: 3.75rem;
    border-radius: 50%;
    margin: .625rem;
  }

  .lean_name {
    float: left;
    width: 21.25rem;
  }

  .learn_nick {
    float: left;
    width: 100%;
    height: 1.3125rem;
    line-height: 1.3125rem;
    color: #2a2b2a;
    font-size: 1rem;
    font-weight: 700;
    padding: .9375rem 0 .625rem;
  }

  .learn_nick span {
    float: left;
    display: inline-block;
  }

  .learn_nick span:nth-child(1) {
    max-width: 15.625rem;
  }

  .learn_id {
    width: 100%;
    line-height: 1.1875rem;
    color: #666666;
    font-size: .875rem;
  }

  .lean_now {
    float: left;
    width: 6rem;
    line-height: 1.9375rem;
    text-align: center;
    color: #ffffff;
    font-size: .875rem;
    border-radius: 0 0 .625rem .625rem;
    background: linear-gradient(135deg, #36e2c2 0%, #00b7d0 100%);
  }

  .lean_edit {
    float: right;
    line-height: 1.1875rem;
    color: #009c7f;
    font-size: .875rem;
    display: flex;
    align-items: center;
    margin: 1.875rem 0 0;
  }

  .lean_edit:hover,
  .leaan_go:hover,
  .leaan_change:hover {
    cursor: pointer;
  }

  .lean_edit img {
    width: .8125rem;
    height: .8125rem;
    margin: 0 .375rem 0 0;
  }

  .leaan_go {
    float: right;
    width: 7.8125rem;
    height: 2.5rem;
    margin: 1.25rem 1.25rem 0 2.5rem;
  }

  .leaan_change {
    float: right;
    width: 7.8125rem;
    line-height: 2.5rem;
    text-align: center;
    margin: 1.25rem 1.25rem 0 2.5rem;
    box-sizing: border-box;
    border: .0625rem solid #009C7F;
    border-radius: 1.25rem;
    font-size: .875rem;
    color: #009C7F;
  }

  .lean_book {
    float: left;
    width: 100%;
    box-sizing: border-box;
    padding: .8125rem 1.25rem .4375rem;
  }

  .lean_book div {
    float: left;
    width: 33.33%;
    line-height: 2.0625rem;
    margin: 0 0 .375rem;
    color: #2a2b2a;
    font-size: .875rem;
    box-sizing: border-box;
    padding: 0 .625rem 0 0;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .addbtn {
    width: 15.25rem;
    line-height: 3.5625rem;
    border-radius: 2.1563rem;
    background: #00c9a3;
    box-shadow: 0 .25rem .9375rem 0 #00000040;
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 700;
    margin: 1.875rem 0 1.875rem 22.875rem;
    text-align: center;
  }

  .addbtn:hover {
    cursor: pointer;
  }

  /* 修改昵称 */
  .alert_bg {
    z-index: 50;
    background: rgba(0, 0, 0, .7);
    position: fixed;
    width: 100%;
    height: 100vh;
    top: 0;
    left: 0;
  }

  .alert_box {
    z-index: 99;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .alert_inner {
    width: 49.875rem;
    border-radius: 1.25rem;
    background: #ffffff;
    box-sizing: border-box;
    padding: 0 1.25rem;
  }

  .alert_top {
    float: left;
    width: 100%;
    height: 3.375rem;
    border-bottom: .0625rem solid #eee;
  }

  .alert_h1 {
    float: left;
    width: 100%;
    line-height: 3.375rem;
    text-align: center;
    color: #2a2b2a;
    font-size: 1rem;
    font-weight: bold;
  }

  .alert_x {
    float: right;
    width: .9375rem;
    height: .9375rem;
    padding: 1.1875rem;
    margin: -3.125rem -1.125rem 0 0;
  }

  .alert_x:hover {
    cursor: pointer;
  }

  .alert_wrap {
    width: 100%;
    float: left;
  }

  .alert_form {
    width: 28.875rem;
    margin: 0 auto;
  }

  .alert_item {
    margin: 3.6875rem 0 0;
  }

  .alert_label {
    line-height: 1.3125rem;
    color: #2a2b2a;
    font-size: 1rem;
    padding: 0 0 .5rem;
  }

  .alert_input {
    width: 28.875rem;
    height: 3.3125rem;
  }

  .alert_input .el-input__inner {
    height: 3.3125rem;
    border-radius: .375rem;
    background: #f5f5f6;
    border: .0625rem solid #f5f5f6;
  }

  .alert_foot {
    width: 100%;
    float: left;
    margin: 16rem 0 1.875rem;
    display: flex;
    justify-content: center;
  }

  .alert_ok {
    width: 7.625rem;
    height: 2.375rem;
    border-radius: 1.1875rem !important;
    background: #00c9a3 !important;
    border-color: #00c9a3 !important;
  }


  /* 退出弹窗 */
  .alert_bg {
    z-index: 50;
    background: rgba(0, 0, 0, .7);
    position: fixed;
    width: 100%;
    height: 100vh;
    top: 0;
    left: 0;
  }

  .alert_box {
    z-index: 99;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .quit_box {
    .alert_inner {
      width: 31.25rem;
      border-radius: 1.25rem;
      background: #ffffff;
      box-sizing: border-box;
      padding: 0 1.25rem;
    }

    .alert_top {
      float: left;
      width: 100%;
      height: 3.375rem;
      border-bottom: .0625rem solid #eee;
    }

    .alert_h1 {
      float: left;
      width: 100%;
      line-height: 3.375rem;
      text-align: center;
      color: #2a2b2a;
      font-size: 1rem;
      font-weight: bold;
    }

    .alert_x {
      float: right;
      width: .9375rem;
      height: .9375rem;
      padding: 1.1875rem;
      margin: -3.125rem -1.125rem 0 0;
    }

    .alert_x:hover {
      cursor: pointer;
    }

    .alert_wrap {
      width: 100%;
      float: left;
      display: flex;
      align-items: center;
      flex-flow: column;
    }

    .alert_tit {
      line-height: 1.3125rem;
      color: #2a2b2a;
      font-size: 1rem;
      margin: 2.4375rem 0 1rem;
    }

    .alert_btns {
      display: flex;
      margin: 1.25rem 0 2.1875rem;
    }

    .alert_btns div {
      width: 7.625rem;
      line-height: 2.375rem;
      text-align: center;
      border-radius: 1.1875rem;
      font-size: 1rem;
    }

    .alert_btns div:hover {
      cursor: pointer;
    }

    .alert_quit {
      color: #666666;
      background: #f5f5f5;
    }

    .alert_btns .alert_ok {
      color: #DD2A2A;
      background: #fee9e9 !important;
      margin: 0 0 0 2.125rem;
    }
  }
</style>
