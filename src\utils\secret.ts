import CryptoJS from "crypto-js"

// 必须为16、24、32位
const KEY = "W7oCufy99F6enlcR"
const IV = "5nwoWBsZBA4ujTkC"

/**
 * AES加密 ：字符串 key iv  返回base64
 */
export const Encrypt = (word: any, keyStr = KEY, ivStr = IV): any => {
  const key = CryptoJS.enc.Utf8.parse(keyStr)
  const iv = CryptoJS.enc.Utf8.parse(ivStr)
  const srcs = CryptoJS.enc.Utf8.parse(word)
  const encrypted = CryptoJS.AES.encrypt(srcs, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  })
  return encrypted.toString()
}

/**
 * AES 解密 ：字符串 key iv  返回base64
 *
 * @return {string}
 */
export const Decrypt = (word: any, keyStr = KEY, ivStr = IV): any => {
  const key = CryptoJS.enc.Utf8.parse(keyStr)
  const iv = CryptoJS.enc.Utf8.parse(ivStr)
  const decrypt = CryptoJS.AES.decrypt(word, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  })
  return CryptoJS.enc.Utf8.stringify(decrypt)
}

/**
 * 传参加密 ：字符串 key iv  返回base64
 */
export const dataEncrypt = (word: any, keyStr = KEY): any => {
  return CryptoJS.AES.encrypt(JSON.stringify(word), keyStr).toString()
}

/**
 * 传参解密 ：字符串 key iv  返回base64
 *
 * @return {string}
 */
export const dataDecrypt = (word: any, keyStr = KEY): any => {
  const bytes = CryptoJS.AES.decrypt(word, keyStr)
  const aa = bytes.toString(CryptoJS.enc.Utf8)
  return JSON.parse(aa)
}

/**
 * 合并对象属性更新值 ：后者属性值更新到前者，单只返回前者原有的属性
 *
 * @return {object}
 */
export const mergeObject = (obj1: any, obj2: any): any => {
  for (const key in obj2) {
    if (obj2.hasOwnProperty(key)) {
      obj1[key] = obj2[key]
    }
  }
  return obj1
}
/**
 * @param {*} degreeType 难度类型
 * @returns {String}
 * */
export function getDegreeName(degreeType: any) {
  let name = ""
  switch (degreeType) {
    case 1:
      name = "容易"
      break
    case 2:
      name = "较易"
      break
    case 3:
      name = "中档"
      break
    case 4:
      name = "较难"
      break
    case 5:
      name = "难"
      break
  }
  return name
}

