<template>
  <div>
    <!-- <h1>Welcome to the Home Page</h1>
    <el-button @click="logout">Logout</el-button> -->
  </div>
</template>

<script lang="ts" setup>
  import { useRouter } from 'vue-router'
  import { useUserStore } from "@/store/modules/user"

  const router = useRouter()
  const checkToken = () => {
    const token = useUserStore().token, learnUsers = useUserStore().learnUsers
    if (token) {
      if (learnUsers.length) {
        router.replace({ name: 'KnowledgeGraph' })
      } else {
        router.push({ name: "UserAdd", query: { pageType: 'add' } })
      }
    } else {
      router.replace({ name: 'Login' })
    }
  }
  checkToken()
</script>