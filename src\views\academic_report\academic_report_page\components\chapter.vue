<template>
    <div class="base-data">
        <div class="base-data-title width700">综合概览
            <!-- <div class="scroll-container"> -->
                <span class="title-tips">(当前章节：{{ chapterNameAll }})</span>
            <!-- </div> -->
        </div>
        <div class="knowledge-data-content">
            <div class="knowledge-data-content-item yellow">
                <div class="knowledge-data-content-item-title"><span></span>累计学练时长</div>
                <div class="knowledge-data-content-item-data"><span>{{ overviewVo.hours }}</span>小时<span>{{ overviewVo.minutes }}</span>分钟</div>
            </div>
            <div class="knowledge-data-content-item green">
                <div class="knowledge-data-content-item-title"><span></span>总观看学习视频</div>
                <div class="knowledge-data-content-item-data"><span>{{ overviewVo.videoCount }}</span>次</div>
            </div>
            <div class="knowledge-data-content-item red">
                <div class="knowledge-data-content-item-title"><span></span>累计答题数</div>
                <div class="knowledge-data-content-item-data"><span>{{ overviewVo.quesCount }}</span>道</div>
            </div>
            <div class="knowledge-data-content-item blue">
                <div class="knowledge-data-content-item-title"><span></span>题目整体难度</div>
                <div class="knowledge-data-content-item-data"><span>{{ overviewVo.degreeType }}</span></div>
            </div>
            <div class="knowledge-data-content-item blue">
                <div class="knowledge-data-content-item-title"><span></span>平均正确率</div>
                <div class="knowledge-data-content-item-data"><span>{{ (overviewVo.quesCorrectRate * 100).toFixed(2) }}</span>%</div>
            </div>
        </div>
        <div style="display: flex;align-items: center;flex-direction: column;">
            <EchartsComponent :width="'39.1875rem'" :height="'23.75rem'" :option="optionsOverView"></EchartsComponent>
            <div class="echarts-tip">知识点总数<span>{{ overviewVo.pointCount }}</span>个，整体掌握度<span>{{ (overviewVo.pointMastery * 100).toFixed(2) }}</span>%</div>
        </div>
        <div class="knowledge-point" v-if="!isWen">
            <div class="knowledge-point-item" v-for="(item,i) in masterPointVos" :key="i">
                <div class="knowledge-point-item-left">
                    <div class="knowledge-point-item-left-top">
                        <div>{{ item.name }}</div>
                        <div>掌握度：<span :class="Number(item.correctRate) > 0.85?'green-text':Number(item.correctRate) > 0.65?'yellow-text':Number(item.correctRate) > 0?'red-text':''">{{ Number(item.correctRate) < 0 ? '未训练':((Number(item.correctRate) * 100).toFixed(2) + '%') }}</span></div>
                    </div>
                    <div>
                        <el-progress :percentage="Number(item.correctRate) < 0? 0: (Number(item.correctRate) * 100)" :show-text="false" :status="Number(item.correctRate) > 0.85?'success':Number(item.correctRate) > 0.65?'warning':'exception'" />
                    </div>
                </div>
                <div class="knowledge-point-item-right">
                    <div class="btn" @click="goGraphDetail">查看图谱</div>
                </div>
            </div>
            <div class="pagination-box">
                <Pagination
                    :total="pageData.total"
                    :layout="'total, prev, pager, next, jumper'"
                    :current="pageData.current"
                    @currentSizeChange="currentSizeChange" />
            </div>
        </div>
    </div>
    <div class="advice-text">
        <div class="advice-text-top">
            <img src="@/assets/img/analyse/sign.png" />
            <img src="@/assets/img/analyse/xuexijianyi.png" />
        </div>
        <div class="advice-text-middle">
            <span>通过本章节的学习，检查出你有部分知识点未完全掌握，继续巩固已掌握的知识点，重点攻克未掌握部分，保持耐心和积极态度，持续努力，相信你会不断进步！</span>
            <img src="@/assets/img/analyse/smile.png" />
        </div>
        <div class="advice-text-bottom">
            <img src="@/assets/img/analyse/text.png" />
            <img src="@/assets/img/analyse/double-down.png" />
        </div>
    </div>
    <div class="base-data" v-if="!isWen">
        <div class="base-data-title">弱项检测</div>
        <div class="knowledge-data-content">
            <div class="knowledge-data-content-item blue width262">
                <div class="knowledge-data-content-item-title"><span></span>完成题目</div>
                <div class="knowledge-data-content-item-data"><span>{{ weakItemVo.quesCount }}</span>道</div>
            </div>
            <div class="knowledge-data-content-item green width262">
                <div class="knowledge-data-content-item-title"><span></span>正确题数</div>
                <div class="knowledge-data-content-item-data"><span>{{ weakItemVo.correctCount }}</span>道</div>
            </div>
            <div class="knowledge-data-content-item red width262">
                <div class="knowledge-data-content-item-title"><span></span>错误题数</div>
                <div class="knowledge-data-content-item-data"><span>{{ weakItemVo.errorCount }}</span>道</div>
            </div>
            <div class="knowledge-data-content-item yellow width262">
                <div class="knowledge-data-content-item-title"><span></span>正确率</div>
                <div class="knowledge-data-content-item-data"><span>{{ (weakItemVo.correctRate * 100).toFixed(2) }}</span>%</div>
            </div>
        </div>
        <div style="display: flex;align-items: center;flex-direction: column;">
            <EchartsComponent :width="'39.1875rem'" :height="'23.75rem'" :option="optionsWeakItem"></EchartsComponent>
            <div class="echarts-tip">知识点总数<span>{{ weakItemVo.pointNum }}</span>个</div>
        </div>
    </div>
    <div class="base-data">
        <div class="base-data-title">{{isWen?'学习分析':'针对学习'}}</div>
        <div class="knowledge-data-content">
            <div class="knowledge-data-content-item blue width262">
                <div class="knowledge-data-content-item-title"><span></span>观看知识点数</div>
                <div class="knowledge-data-content-item-data"><span>{{ videoVo.pointCount }}</span>个</div>
            </div>
            <div class="knowledge-data-content-item blue width262">
                <div class="knowledge-data-content-item-title"><span></span>观看个数</div>
                <div class="knowledge-data-content-item-data"><span>{{ videoVo.videoCount }}</span>个</div>
            </div>
            <div class="knowledge-data-content-item blue width262">
                <div class="knowledge-data-content-item-title"><span></span>观看总次数</div>
                <div class="knowledge-data-content-item-data"><span>{{ videoVo.totalCount }}</span>个</div>
            </div>
            <div class="knowledge-data-content-item blue width262">
                <div class="knowledge-data-content-item-title"><span></span>观看总时长</div>
                <div class="knowledge-data-content-item-data"><span>{{ videoVo.hours }}</span>小时<span>{{ videoVo.minutes }}</span>分钟</div>
            </div>
        </div>
        <div>
            <EchartsComponent :width="'69.25rem'" :height="'25rem'" :option="optionsVideoVo"></EchartsComponent>
        </div>
    </div>

    <div class="base-data" v-if="!isWen">
        <div class="base-data-title">学后检测</div>
        <div class="knowledge-data-content">
            <div class="knowledge-data-content-item blue">
                <div class="knowledge-data-content-item-title"><span></span>练习次数</div>
                <div class="knowledge-data-content-item-data"><span>{{ weakItemVo.trainCount }}</span>次</div>
            </div>
            <div class="knowledge-data-content-item blue">
                <div class="knowledge-data-content-item-title"><span></span>完成题目</div>
                <div class="knowledge-data-content-item-data"><span>{{ weakItemVo.quesCount }}</span>道</div>
            </div>
            <div class="knowledge-data-content-item green">
                <div class="knowledge-data-content-item-title"><span></span>正确题数</div>
                <div class="knowledge-data-content-item-data"><span>{{ weakItemVo.correctCount }}</span>道</div>
            </div>
            <div class="knowledge-data-content-item red">
                <div class="knowledge-data-content-item-title"><span></span>错误题数</div>
                <div class="knowledge-data-content-item-data"><span>{{ weakItemVo.errorCount }}</span>道</div>
            </div>
            <div class="knowledge-data-content-item green">
                <div class="knowledge-data-content-item-title"><span></span>正确率</div>
                <div class="knowledge-data-content-item-data"><span>{{(weakItemVo.correctRate * 100).toFixed(2) }}</span>%</div>
            </div>
        </div>
        <div style="display: flex;justify-content: space-between;align-items: center;">
            <div class="half-echarts">
                <EchartsComponent :width="'31.25rem'" :height="'14.25rem'" :option="optionsWeakItem1"></EchartsComponent>
                <div class="ab-position">
                    <div><span>{{ weakItemVo.beforeAccuracy?weakItemVo.beforeAccuracy:'--' }}</span>{{ weakItemVo.beforeAccuracy?'%':'' }}</div>
                    <div>学习前掌握度</div>
                </div>
            </div>
            <img src="@/assets/img/analyse/arrow.png" class="arrow-img" />
            <div class="half-echarts">
                <EchartsComponent :width="'31.25rem'" :height="'14.25rem'" :option="optionsWeakItem2"></EchartsComponent>
                <div class="ab-position">
                    <div><span>{{ weakItemVo.afterAccuracy?weakItemVo.afterAccuracy:'--' }}</span>{{ weakItemVo.afterAccuracy?'%':'' }}</div>
                    <div>学习后掌握度</div>
                </div>
            </div>
        </div>
        <div class="record-box" v-loading="analyseState.recordloading">
            <el-table :data="pointTrainVos" style="width: 100%">
                <el-table-column prop="pointName" align="left" label="知识点" width="240" />
                <el-table-column prop="oldAccuracy" label="学前掌握度" width="210">
                    <template #default="scope">
                        <div class="center-box">
                            <div class="table-status" :class="getStatusClass(scope.row.status)">{{ scope.row.oldAccuracy }}% {{ getStatus(scope.row.status) }}</div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="accuracy" label="学后掌握度" width="210">
                    <template #default="scope">
                        <div class="center-box">
                            <div class="table-status" :class="getStatusClass(scope.row.oldStatus)">{{ scope.row.accuracy }}% {{ getStatus(scope.row.oldStatus) }}</div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="promote" label="学习对比">
                    <template #default="scope">
                        <div class="img-text"> <img v-if="Number(scope.row.promote) != 0" :src="getUrl(Number(scope.row.promote))" />{{ Math.abs(Number(scope.row.promote)*100).toFixed(2) }}%</div>
                    </template>
                </el-table-column>
                <template #empty>
                    <div class="empty">
                        <img src="@/assets/img/percision/empty.png" alt="no-data" />
                        <p>空空如也</p>
                    </div>
                </template>
            </el-table>
            <div class="pagination-box">
                <Pagination
                    :total="pageData1.total"
                    :current="pageData1.current"
                    :layout="'total, prev, pager, next, jumper'"
                    @currentSizeChange="currentSizeChange1" />
            </div>
        </div>
    </div>

    <div class="base-data">
        <div class="base-data-title">{{isWen?'训练分析':'阶段检测'}}</div>
        <div class="knowledge-data-content">
            <div class="knowledge-data-content-item blue">
                <div class="knowledge-data-content-item-title"><span></span>练习次数</div>
                <div class="knowledge-data-content-item-data"><span>{{ stageItemVo.trainCount }}</span>次</div>
            </div>
            <div class="knowledge-data-content-item blue">
                <div class="knowledge-data-content-item-title"><span></span>完成题目</div>
                <div class="knowledge-data-content-item-data"><span>{{ stageItemVo.quesCount }}</span>道</div>
            </div>
            <div class="knowledge-data-content-item green">
                <div class="knowledge-data-content-item-title"><span></span>正确题数</div>
                <div class="knowledge-data-content-item-data"><span>{{ stageItemVo.correctCount }}</span>道</div>
            </div>
            <div class="knowledge-data-content-item red">
                <div class="knowledge-data-content-item-title"><span></span>错误题数</div>
                <div class="knowledge-data-content-item-data"><span>{{ stageItemVo.errorCount }}</span>道</div>
            </div>
            <div class="knowledge-data-content-item green">
                <div class="knowledge-data-content-item-title"><span></span>正确率</div>
                <div class="knowledge-data-content-item-data"><span>{{ (stageItemVo.correctRate * 100) .toFixed(1) }}</span>%</div>
            </div>
        </div>
        <div class="margtop20">
            <EchartsComponent :width="'67.5rem'" :height="'18.75rem'" :option="optionsStageItemVo"></EchartsComponent>
        </div>
    </div>

    <div class="base-data">
        <div class="base-data-title">做题时长统计</div>
        <EchartsComponent :width="'67.5rem'" :height="'18.75rem'" :option="optionsDoQuesVos"></EchartsComponent>
    </div>

    <div class="base-data">
        <div class="base-data-title">错题消化</div>
        <div class="wrong-data">
            当前章节累计产生错题<span>{{ errorVo.errorCount||0 }}</span>道，已订正<span>{{ errorVo.correctCount||0 }}</span>道，未订正<span>{{ errorVo.unCorrectCount||0 }}</span>道。
        </div>
        <div class="record-box" v-loading="analyseState.recordloading">
            <el-table :data="errorVoData" class="table">
                <el-table-column prop="pointName" label="知识点" show-overflow-tooltip></el-table-column>
                <el-table-column prop="errorCount" label="错题总数" />
                <el-table-column prop="correctCount" label="已订正" />
                <el-table-column prop="unCorrectCount" label="未订正" />
                <el-table-column prop="simpleCount" label="简单题" />
                <el-table-column prop="easyCount" label="较易题" />
                <el-table-column prop="middleCount" label="中等题" />
                <el-table-column prop="difficultCount" label="较难题" />
                <el-table-column prop="hardCount" label="难题" />
                <template #empty>
                    <div class="empty">
                        <img src="@/assets/img/percision/empty.png" alt="no-data" />
                        <p>空空如也</p>
                    </div>
                </template>
            </el-table>
            <!-- <div class="pagination-box">
                <Pagination
                    :total="pageData2.total"
                    :current="pageData2.current"
                    :layout="'total, prev, pager, next, jumper'"
                    @currentSizeChange="currentSizeChange2" />
            </div> -->

            <div class="start-learn-btn" v-if="errorVoData.length > 0" @click="toCorrect">去订正</div>
        </div>
    </div>

    <div class="advice-text height84" v-if="errorVoData.length > 0">
        <div class="advice-text-top">
            <img src="@/assets/img/analyse/sign.png" />
            <img src="@/assets/img/analyse/xiaojie.png" />
        </div>
        <div class="advice-text-middle">
            <span>你在{{ chapterNameAll }}章节中一共累积了{{ errorVo.errorCount }}道错题，其中【{{ errorVo.pointName }}】的知识点错题最多，请针对错题进行学习提升，再接再厉哦~</span>
            <img src="@/assets/img/analyse/smile.png" />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'
import { useUserStore } from "@/store/modules/user"
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { dataDecrypt, dataEncrypt, getDegreeName, mergeObject } from "@/utils/secret"
import { userAnalyseApi, userAnalyseToChapterApi ,userAnalyseToPointApi} from '@/api/analyse'
import { listApi } from '@/api/training'
import EchartsComponent from "@/components/echarts/index.vue"
import { optionsOverView, optionsWeakItem, optionsVideoVo, optionsWeakItem1, optionsWeakItem2, optionsStageItemVo, optionsDoQuesVos } from "./data"
import { storeToRefs } from 'pinia'
import { wenke } from '@/utils/user/enum'

const userStore = useUserStore()
const { learnNow, chapterObj, subjectObj } = storeToRefs(userStore)
const router = useRouter()
const props = defineProps({
  options: {
    type: Object,
    default: () => {}
  },
  chapterId: {
    type: String,
    default: ''
  },
  chapterName: {
    type: String,
    default: ''
    },
  apiType: {
    type: String,
    default: ''
    },
})
const isWen = computed(()=>{
  return wenke.includes(Number(subjectObj.value.id))
})
watch(
    () => props.chapterId,
	(newChapterId, oldChapterId) => {
        chapterNameAll.value = props.chapterName
        
        // 当 chapterId 从无效变为有效时，重新获取数据
        if (newChapterId && newChapterId !== oldChapterId) {
            console.log('chapterId 已更新，重新获取分析数据:', newChapterId)
            getAnalyseToChapter()
        }
	}
)

// 监听 subjectObj 变化，确保数据完整时重新获取分析数据
watch(
    () => subjectObj.value,
    (newVal, oldVal) => {
        // 当 subjectObj 数据完整且与之前状态不同时，重新获取数据
        if (props.chapterId && newVal?.id && (props.apiType === 'improvement' || newVal?.bookId)) {
            const wasIncomplete = !oldVal?.id || (props.apiType !== 'improvement' && !oldVal?.bookId)
            if (wasIncomplete) {
                console.log('subjectObj 数据已完整，重新获取分析数据')
                getAnalyseToChapter()
            }
        }
    },
    { deep: true, immediate: false }
)
const chapterNameAll = ref(props.chapterName)
const analyseState = reactive({
  loading: false,
  recordloading: false
})
class IPage {
    total = 0
    current = 0
    size = 0
}
class IOver {
    studyTime = 0
    minutes = 0
    hours = 0
    secs = 0
    videoCount = 0
    pointMastery = 0
    quesCount = 0
    pointCount = 0
    quesCorrectRate = 0
    degree = 0
    correctDefeatRate = 0
    degreeType = ""
}
class IWeak {
    correctCount = 0
    errorCount = 0
    correctRate = 0
    quesCount = 0
    pointNum = 0
    trainCount = 0
    beforeAccuracy = 0
    afterAccuracy = 0
}
class IVideo {
    pointCount = 0
    videoCount = 0
    totalCount = 0
    minutes = 0
    hours = 0
    secs = 0
    totalTime = 0
}
class IStage {
    correctCount = 0
    errorCount = 0
    correctRate = 0
    quesCount = 0
    pointNum = 0
    trainCount = 0
    beforeAccuracy = 0
    afterAccuracy = 0
}
class IError {
    correctCount = 0
    errorCount = 0
    unCorrectCount = 0
    pointName = 0
}
let pageData = reactive(new IPage())
let pageData1 = reactive(new IPage())
let pageData2 = reactive(new IPage())

// 总体情况数据
let overviewVo = ref(new IOver())
// 弱项检测数据&学后检测
let weakItemVo = ref(new IWeak())
// 针对学习数据
let videoVo = ref(new IVideo())
// 阶段检测数据
let stageItemVo = ref(new IStage())
// 错题消化数据
let errorVo = ref(new IError())
const masterPointVos = ref([] as any[])
const masterPointVosAll = ref([] as any[])
const errorVoData = ref([])
const errorVoDataAll = ref([])
const pointTrainVosAll = ref([] as any[])
const pointTrainVos = ref([] as any[]) // 学后检测知识点
onMounted(()=> {
    init()
})
const init = () => {
    // 检查必要参数是否存在，如果存在则获取数据
    if (props.chapterId && subjectObj.value?.id && (props.apiType === 'improvement' || subjectObj.value?.bookId)) {
        getAnalyseToChapter()
    } else {
        console.log('等待必要参数准备完成...', {
            chapterId: props.chapterId,
            subjectId: subjectObj.value?.id,
            bookId: subjectObj.value?.bookId,
            apiType: props.apiType
        })
    }
}
const getStatus = (status: number) => {
    switch (status) {
        case 1:
            return "已掌握"
        case 2:
            return "一般"
        case 3:
            return "未掌握"
        default:
            return ""
    }
}
const getStatusClass = (status: number) => {
    switch (status) {
        case 1:
            return "green-box"
        case 2:
            return "yellow-box"
        case 3:
            return "red-box"
        default:
            return ""
    }
}
const getUrl = (data: number) => {
    let url = "rise"
    if (data < 0) {
        url = "decline"
    }
    return new URL(`../../../assets/img/percision/${url}.png`, import.meta.url).href //静态资源引入为url，相当于require()
}
const goGraphDetail = () => {
    router.push({
        path: '/academic_report/knowledge_graph_detailA'
    })
}
const getAnalyseToChapter = async () => {
    analyseState.loading = true
    let res: any = {}
    
    // 检查必要的参数是否存在
    if (!props.chapterId) {
        console.warn('chapterId 参数缺失，无法获取分析数据')
        analyseState.loading = false
        return
    }
    
    if (!subjectObj.value?.id) {
        console.warn('subjectObj.id 缺失，无法获取分析数据')
        analyseState.loading = false
        return
    }
    
    try {
        if (props.apiType === 'improvement') {
            res = await userAnalyseToPointApi({ subject: subjectObj.value.id, pointId: props.chapterId})
        } else { 
            if (!subjectObj.value?.bookId) {
                console.warn('subjectObj.bookId 缺失，无法获取章节分析数据')
                analyseState.loading = false
                return
            }
            res = await userAnalyseToChapterApi({bookId: subjectObj.value.bookId, subject: subjectObj.value.id, chapterId: props.chapterId})
        }
    } catch (error) {
        console.error('获取分析数据失败:', error)
        analyseState.loading = false
        return
    }
    if (res?.data) {
            await resetdata()
            if (res.data.videoVo){
                secondsToHMS2(Number(res.data.videoVo.totalTime)/1000)
                // console.log(secondsToHMS2,"secondsToHMS2")
                videoVo.value = mergeObject(videoVo.value, res.data.videoVo)
                let dataArrvideo1 = [] as any[]
                let dataArrvideo2 = [] as any[]
                let nameArr = [] as any[]
                res.data.videoVo?.watchItemVo.map((item: any) => {
                    nameArr.push(item.videoName)
                    // 保持原始毫秒值，在tooltip中进行转换
                    dataArrvideo1.push(item.watchTime)
                    dataArrvideo2.push(item.watchCount)
                })
                optionsVideoVo.xAxis[0].data = nameArr
                optionsVideoVo.series[0].data = dataArrvideo1
                optionsVideoVo.series[1].data = dataArrvideo2
            }
            if (res.data.overviewVo) {
                secondsToHMS(Number(res.data.overviewVo.studyTime)/1000)
                overviewVo.value = mergeObject(overviewVo.value, res.data.overviewVo)
                overviewVo.value.degreeType = getDegreeType(res.data.overviewVo.degree) as string
                masterPointVosAll.value = res.data.overviewVo.masterPointVos || []
                masterPointVos.value = setPageData(1,masterPointVosAll.value)
                let dataArr = [] as any[]
                res.data.overviewVo.pointMasteryVos?.map((item: any) => {
                    dataArr.push({name: getName(item.type), value: item.num})
                })
                optionsOverView.series[0].data = dataArr
            }
            if(res.data.weakItemVo) {
                weakItemVo.value = mergeObject(weakItemVo.value, res.data.weakItemVo)
                pointTrainVosAll.value = res.data.weakItemVo.pointTrainVos || []
                pointTrainVos.value = setPageData(1,pointTrainVosAll.value)
                const beforeAccuracy = weakItemVo.value.beforeAccuracy != null? weakItemVo.value.beforeAccuracy: 0
                const afterAccuracy = weakItemVo.value.afterAccuracy != null? weakItemVo.value.afterAccuracy: 0
                optionsWeakItem1.series[0].data = [
                    {name: '学习前掌握度', value: beforeAccuracy},
                    {name: '', value: 100 - beforeAccuracy}
                ]
                optionsWeakItem2.series[0].data = [
                    {name: '学习后掌握度', value: afterAccuracy},
                    {name: '', value: 100 - afterAccuracy}
                ]
                let dataArrweek = [] as any[]
                res.data.weakItemVo.pointMasteryVos?.map((item: any) => {
                    dataArrweek.push({name: getName(item.type), value: item.num})
                })
                optionsWeakItem.series[0].data = dataArrweek
            }
            if(res.data.stageItemVo) {
                //训练分析
                const stageItemVo2 = res.data.stageItemVo
                stageItemVo.value = mergeObject(stageItemVo.value, stageItemVo)
                optionsStageItemVo.series[0].data = []
                optionsStageItemVo.series[0].data.push(stageItemVo2?.myAccuracy?.simpleAccuracy)
                optionsStageItemVo.series[0].data.push(stageItemVo2?.myAccuracy?.easyAccuracy)
                optionsStageItemVo.series[0].data.push(stageItemVo2?.myAccuracy?.middleAccuracy)
                optionsStageItemVo.series[0].data.push(stageItemVo2?.myAccuracy?.difficultAccuracy)
                optionsStageItemVo.series[0].data.push(stageItemVo2?.myAccuracy?.hardAccuracy)
                optionsStageItemVo.series[1].data = []
                optionsStageItemVo.series[1].data.push(stageItemVo2?.averageAccuracy?.simpleAccuracy)
                optionsStageItemVo.series[1].data.push(stageItemVo2?.averageAccuracy?.easyAccuracy)
                optionsStageItemVo.series[1].data.push(stageItemVo2?.averageAccuracy?.middleAccuracy)
                optionsStageItemVo.series[1].data.push(stageItemVo2?.averageAccuracy?.difficultAccuracy)
                optionsStageItemVo.series[1].data.push(stageItemVo2?.averageAccuracy?.hardAccuracy)
            }
            if(res.data.doQuesVos) {
                let seqArr = [] as any[]
                let doTimeArr = [] as any[]
                res.data.doQuesVos?.map((item: any) => {
                    seqArr.push('第' + (item.seq + 1) + '题')
                    doTimeArr.push(item.doTime/1000)
                })
                optionsDoQuesVos.xAxis.data = seqArr
                optionsDoQuesVos.series[0].data = doTimeArr
            }
            if(res.data.errorVo) {
                errorVo.value = mergeObject(errorVo.value, res.data.errorVo)
                errorVoDataAll.value = res.data.errorVo.errorItemVo || []
                errorVoData.value = setPageData(1, errorVoDataAll.value)
                errorVo.value.pointName = findMaxByKey(errorVoDataAll.value, 'errorCount').pointName
            }

        } else {
            resetdata()
        }
        analyseState.loading = false

}
const resetdata = () => {
    return new Promise((resolve, reject) => {
        overviewVo.value = new IOver()
        weakItemVo.value = new IWeak()
        // 针对学习数据
        videoVo.value = new IVideo()
        // 阶段检测数据
        stageItemVo.value = new IStage()
        // 错题消化数据
        errorVo.value = new IError()
        masterPointVosAll.value = []
        masterPointVos.value = []
        pointTrainVosAll.value = []
        pointTrainVos.value = []
        optionsOverView.series[0].data = []
        optionsWeakItem.series[0].data = []
        optionsVideoVo.xAxis[0].data = []
        optionsVideoVo.series[0].data = []
        optionsVideoVo.series[1].data = []
        optionsWeakItem1.series[0].data = []
        optionsWeakItem2.series[0].data = []
        optionsStageItemVo.series[0].data = []
        optionsStageItemVo.series[1].data = []
        optionsDoQuesVos.xAxis.data = []
        optionsDoQuesVos.series[0].data = []
        errorVoDataAll.value = []
        errorVoData.value = []
        resolve(true)
    })
}
const findMaxByKey = (arr, key) => {
  return arr.reduce((max, current) => {
    return current[key] > max[key] ? current : max;
  });
}
const getDegreeType = (degree: number) => {
    if (Number(degree) > 0 && Number(degree) <= 0.2){
        return '难'
    } else if (Number(degree) > 0.2 && Number(degree) <= 0.4){
        return '较难'
    } else if (Number(degree) > 0.4 && Number(degree) <= 0.6){
        return '中档'
    } else if (Number(degree) > 0.6 && Number(degree) <= 0.8){
        return '较易'
    } else if (Number(degree)> 0.8 && Number(degree) <= 1){
        return '容易'
    }
}
const toCorrect = () => {
    router.push({
        name: 'NoteList'
    })
}
const getName = (type: any) => {
    switch(type) {
        case 1:
            return '已掌握'
            break;
        case 2:
            return '一般'
            break;
        case 3:
            return '未掌握'
            break;
        case 4:
            return '未测'
            break;
        default:
            return ''

    }
}
function secondsToHMS(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    // 补零操作，确保两位数显示
    const pad = (num) => num.toString().padStart(2, '0');
    overviewVo.value.minutes = pad(minutes)
    overviewVo.value.hours = pad(hours)
    overviewVo.value.secs = pad(secs)
}
function secondsToHMS2(seconds) {
    // console.log(seconds,"打印响应seconds")
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    // 补零操作，确保两位数显示
    const pad = (num) => num.toString().padStart(2, '0');
    videoVo.value.minutes = pad(minutes)
    videoVo.value.secs = pad(secs)
    videoVo.value.hours = pad(hours)
    // console.log(videoVo.value.minutes ,"打印一下  啊啊啊是的客户")
}
const setPageData = (currentPage: number, data: any) => {
    if (data.length > 0) {
        pageData.current = currentPage
        pageData.total = data.length
        const index = (currentPage - 1)*10
        return data.slice(index, index + 10)
    } else {
        return []
    }
}
const currentSizeChange1 = (currentPage: number, pageSize: number) => {
  pageData1.current = currentPage
  pageData1.size = pageSize
  pointTrainVos.value = setPageData(currentPage,pointTrainVosAll.value)
}
const currentSizeChange = (currentPage: number, pageSize: number) => {
  pageData.current = currentPage
  pageData.size = pageSize
  masterPointVos.value = setPageData(currentPage,masterPointVosAll.value)
}
const currentSizeChange2 = (currentPage: number, pageSize: number) => {
  pageData2.current = currentPage
  pageData2.size = pageSize
  errorVoData.value = setPageData(currentPage,errorVoDataAll.value)
}
defineExpose({ init })

</script>
<style lang="scss" scoped>
.base-data {
    min-height: 12.5625rem;
    background-color: #ffffff;
    padding-top: .625rem;
    box-sizing: border-box;
    margin-bottom: 1.25rem;
    &-title {
        width: 24rem;
        height: 2.5625rem;
        line-height: 2.5625rem;
        // margin: .625rem 0;
        border-left: .3125rem solid #5A85EC;
        background: linear-gradient(270deg, #ffffff 0%, #eef3fd 100%);
        color: #5a85ec;
        font-size: 1.125rem;
        font-weight: 700;
        padding-left: .9375rem;
        box-sizing: border-box;
        margin-bottom: .625rem;
        .title-tips {
            color: #666666;
            font-size: .875rem;
            font-weight: 400;
        }
    }
    &-content {
        width: 100%;
        display: flex;
        justify-content: space-around;
        &-item {
            width: 16.375rem;
            height: 7.5rem;
            border-radius: .25rem;
            background-size: contain;
            background-repeat: no-repeat;
            padding: .625rem 1.25rem;
            box-sizing: border-box;
            &-title {
                color: #7079a8;
                font-size: .875rem;
                font-weight: 400;
            }
            &-data {
                color: #37226d;
                font-size: .875rem;
                font-weight: 400;
                margin: .5rem 0;
                span {
                    color: #222e71;
                    font-size: 1.875rem;
                    font-weight: 700;
                    margin-right: .3125rem;
                }
            }
            &-bottom {
                color: #222e71;
                font-size: .875rem;
                font-weight: 400;
            }
        }
    }
}
.bg1 {
    background-image: url(@/assets/img/academic/term-bg1.png);
}
.bg2 {
    background-image: url(@/assets/img/academic/term-bg2.png);
}
.bg3 {
    background-image: url(@/assets/img/academic/term-bg3.png);
}
.bg4 {
    background-image: url(@/assets/img/academic/term-bg4.png);
}
.pagination-box{
    margin-top: 1.875rem;
    margin-bottom: 1.25rem;
}
.progress-box {
    width: 13.75rem;
    height: 1rem;
    border-radius: .5rem;
    background: #0a3a701a;
    display: flex;
    &-item {
        width: 2.8125rem;
        height: 100%;
        position: relative;
        padding: .0625rem 0;
        box-sizing: border-box;
        &:first-child {
            border-top-left-radius: .5rem;
            border-bottom-left-radius: .5rem;
        }
        &:last-child {
            border-top-right-radius: .5rem;
            border-bottom-right-radius: .5rem;
        }
        span {
            position: absolute;
            right: 0rem;
            display: inline-block;
            height: .875rem;
            width: .0625rem;
            background: #ffffff;
        }
    }
}
.blue {
    background-color: #0A3A70;
}
.empty {
  text-align: center;
  margin-top: 3.125rem;
  img {
    width: 7.4375rem;
    height: 8rem;
  }
  p {
    text-align: center;
    color: #999999;
    font-size: .875rem;
    font-weight: 400;
  }
}
.knowledge-data-content {
    display: flex;
    justify-content: space-around;
    padding: .625rem 1.25rem;
    box-sizing: border-box;
    &-item {
        width: 12.875rem;
        height: 5.6875rem;
        border-radius: .25rem;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        &-title {
            font-size: .875rem;
            font-weight: 400;
            display: flex;
            align-items: center;
            span {
                width: 1rem;
                height: 1rem;
                border-radius: 50%;
                display: inline-block;
                margin-right: .375rem;
            }
        }
        &-data {
            font-size: .875rem;
            font-weight: 400;
            margin-top: .75rem;
            span {
                font-size: 1.75rem;
                font-weight: 700;
                margin-right: .25rem;
                margin-left: .25rem;
            }
        }
    }
    .green {
        background: #e5f9f6;
        .knowledge-data-content-item-title {
            color: #009c7f;
            span {
                background: #00c9a3;
            }

        }
    }
    .yellow {
        background: #FDF5E8;
        .knowledge-data-content-item-title {
            color: #EF9D19;
            span {
                background: #EF9D19;
            }

        }
    }
    .red {
        background: #FCE9E9;
        .knowledge-data-content-item-title {
            color: #DD2A2A;
            span {
                background: #DD2A2A;
            }

        }
    }
    .blue {
        background: #EEF3FD;
        .knowledge-data-content-item-title {
            color: #5A85EC;
            span {
                background: #5A85EC;
            }

        }
    }
}
:deep(.el-progress-bar__outer) {
  height: .625rem!important;
}
.knowledge-point {
    padding: 0rem 1.25rem 1.25rem 1.25rem;
    &-item {
        display: flex;
        padding-top: 1.25rem;
        margin-bottom: .625rem;
        &:first-child {
            border-top: .0625rem dashed #EAEAEA;
        }
        &-left {
            width: 53.25rem;
            margin-right: 1.875rem;
            &-top {
                display: flex;
                justify-content: space-between;
                color: #2a2b2a;
                font-size: .875rem;
                font-weight: 400;
                margin-bottom: .625rem;
            }
        }
        &-right {
            display: flex;
            padding-top: .5rem;
            .btn {
                width: 6.125rem;
                height: 1.9375rem;
                border-radius: .25rem;
                color: #009c7f;
                background: #e5f9f6;
                display: flex;
                cursor: pointer;
                align-items: center;
                justify-content: center;
                &:first-child {
                    margin-right: 1.25rem;
                }
                img {
                    width: 1rem;
                    height: 1rem;
                    margin-right: .375rem;
                }
            }
        }
    }
}
.yellow-text {
    color: #ef9d19;
}
.green-text {
    color: #009C7F
}
.red-text {
    color: #DD2A2A
}
.radar-box {
    width: 100%;
    display: flex;
    justify-content: center;
}
.width262 {
    width: 16.375rem;
}
.record-box {
    padding: 1.25rem;
}
.echarts-tip {
    width: 16.75rem;
    height: 2.4375rem;
    line-height: 2.4375rem;
    text-align: center;
    border-radius: 1.2188rem;
    border: .0625rem solid #eaeaea;
    background: #f5f5f5;
    color: #5a85ec;
    margin-top: -4.375rem;
    margin-bottom: 3.125rem;
    font-size: .875rem;
    span {
        font-weight: 700;
    }
}
.arrow-img {
    width: 2.9375rem;
    height: 2.25rem;
}
.advice-text {
    width: 71.375rem;
    height: 10.5rem;
    padding: .75rem .625rem;
    box-sizing: border-box;
    margin-bottom: 1.25rem;
    background: linear-gradient(179.2deg, #f3fff0 0%, #d8fedd 56.99999999999999%, #f0faeb 100%);
    &-top {
        img:first-child {
            width: 1.6875rem;
            height: 1.6875rem;
            margin-right: .5rem;
        }
        img:last-child {
            width: 6.5rem;
            height: 1.6875rem;
        }
    }
    &-middle {
        display: flex;
        align-items: center;
        justify-content: center;
        color: #2a2b2a;
        font-size: .875rem;
        font-weight: 400;
        span {
            max-width: 62.5rem;
        }
        img {
            width: 2.25rem;
            height: 2.25rem;
        }
    }
    &-bottom {
        display: flex;
        align-items: center;
        flex-direction: column;
        margin-top: 1.875rem;
        img:first-child {
            width: 58.625rem;
            height: 1.3125rem;
        }
        img:last-child {
            width: 2.75rem;
            margin-top: .625rem;
            height: 1.0625rem;
        }
    }
}
.height84 {
    height: 5.25rem!important;
}
.half-echarts {
    position: relative;
    .ab-position {
        position: absolute;
        left: calc(50% - 3.375rem);
        bottom: 2.875rem;
        color: #2a2b2a;
        font-size: 1.125rem;
        font-weight: 400;
        div {
            text-align: center;
            span {
                font-size: 3.75rem;
                font-weight: 700;
            }
        }
    }
}
.margtop20 {
    margin-top: 1.875rem;
}
.wrong-data {
    width: 69.25rem;
    height: 5.6875rem;
    line-height: 5.6875rem;
    border-radius: .25rem;
    background: #eef3fd;
    color: #2a2b2a;
    font-size: .875rem;
    font-weight: 400;
    text-align: center;
    margin: 0 auto;
    span {
        font-size: 1.875rem;
        font-weight: 700;
        margin: 0 .3125rem;
    }
}
.width700 {
    width: 70.75rem;
    display: flex;
}

.scroll-container {
width: 43.75rem;           /* 设置容器宽度 */
white-space: nowrap;     /* 防止文字换行 */
overflow: hidden;        /* 隐藏溢出的部分 */
box-sizing: border-box;  /* 确保宽度计算正确 */
}

/* 定义滚动动画 */
.scroll-container span {
display: inline-block;
animation: scroll-text 10s linear infinite; /* 滚动动画 */
}

/* 滚动动画定义 */
@keyframes scroll-text {
0% {
    transform: translateX(100%); /* 初始位置在容器的右边 */
}
100% {
    transform: translateX(-100%); /* 结束位置在容器的左边 */
}
}

// .scroll-container{
//     width: 300px;        /* 设置容器宽度 */
//     white-space: nowrap; /* 防止文本换行 */
//     overflow-x: auto;    /* 水平滚动条 */
//     overflow-y: hidden;  /* 隐藏垂直滚动条 */
// }

.start-learn-btn {
    width: 14.0625rem;
    height: 3.125rem;
    line-height: 3.125rem;
    color: #ffffff;
    background: url(@/assets/img/percision/learn-btn-bg.png);
    background-size: 100%;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 1.125rem;
    margin: 0 auto;
    margin-top: 3.125rem;
}
</style>
