<template>
  <div class="container">
    <div class="title-coustom">
      我的试卷
      <span></span>
    </div>
    <div class="filter-box">
      <div class="filter-box-top">
        <div class="filter-box-top-text">
          共<span>{{pageData.total}}</span>个结果
        </div>
        <el-select v-model="sort" popper-class="filter-select" @change="getList">
          <el-option label="倒序" :value="0"></el-option>
          <el-option label="正序" :value="1"></el-option>
        </el-select>
      </div>
      <subjectSelect :selected="subject" :is-row="true" @setSubject="setSubject" />
    </div>
    <div class="test-box" v-loading="loading">
      <template v-if="testList.length > 0">
        <div class="test-wrap" v-for="item in testList">
          <div class="test-box-item">
            <div class="test-box-item-img">
              <span class="red-text" v-if="item.score">{{item.score}}分</span>
            </div>
            <div class="test-box-item-info">
              <div class="test-box-item-info-title">
                {{item.title}}
              </div>
              <div class="test-box-item-info-data">
                <div>更新时间：{{item.updateDate}}</div>
                <div>浏览：{{ item.viewCount }}</div>
                <div>题量：{{ item.quesCount }}</div>
              </div>
            </div>
            <div class="test-box-item-btn">
              <div class="test-box-item-btn-it red-btn" @click="testDel(item)">
                <img src="@/assets/img/percision/delete_red.png" alt=""> 删除
              </div>
              <div class="test-box-item-btn-it blue-text" @click="testDetail(item)">
                查看详情>
              </div>
            </div>
          </div>
          <div class="hui-line"></div>
        </div>
        <div class="pagination-box">
          <Pagination
            :total="pageData.total"
            :current="pageData.current"
            @currentSizeChange="currentSizeChange"
            @pageClick="pageClick"/>
        </div>
      </template>
      <div class="empty" v-else>
        <img src="@/assets/img/percision/empty.png" alt="del" />
        <p>空空如也</p>
      </div>
    </div>
  </div>
  <el-dialog v-model="state.visible" class="del-dialog" title="删除" width="500" center>
    <div class="dialog-content">
      <p>确定删除该试卷吗？</p>
      <img src="@/assets/img/percision/del-img.png" alt="del" />
    </div>
    <template #footer>
      <div class="dialog-footer">
        <div class="btn-box grey-btn" @click="state.visible = false">取消</div>
        <div class="btn-box red-btn" type="primary" @click="delConfirm">
          确认
        </div>
      </div>
    </template>
  </el-dialog>
  <!-- 购买会员弹窗 -->
  <buyVip :show="showVip"  @close="quitHide"></buyVip>
</template>

<script lang="ts" setup>
import { computed, onMounted, reactive, ref } from 'vue';
import { useRouter, useRoute } from 'vue-router'
import { getLearnReportListApi, cancelCollectApi } from "@/api/report"
import { useUserStore } from "@/store/modules/user"
const userStore = useUserStore()
import { storeToRefs } from 'pinia'
const { subjectObj } = storeToRefs(userStore)
import subjectSelect from "@/views/components/subjectSelect/index.vue"
import { ElMessage } from 'element-plus';
import buyVip from "@/views/components/buyVip/index.vue"
import { dataEncrypt, dataDecrypt } from "@/utils/secret"
import { createTrainToAtlasApi, getDetailsApi, reportTrainApi } from '@/api/training';
const route = useRoute()
const router = useRouter()
const queryData = route.query.data ? dataDecrypt(route.query.data) : {}

const state = reactive({
  visible: false,
  id: ""
})
const pageData = reactive({
  total: 0,
  current: 1,
  size: 10
})
const subject = computed(() => {
  return subjectObj.value.subject
})
let handleData = {} as any
const sort = ref(0)
const testList = ref([] as any[])
const showVip = ref(false)
const loading = ref(false)
onMounted(() => {
  getList()
})
const quitHide = async () => {
    showVip.value = false
    if (useUserStore().memberInfo) {
      getList()
    }
  }
const currentSizeChange = (currentPage: number, pageSize: number) => {
  pageData.current = currentPage
  pageData.size = pageSize
  getList()
}
const pageClick = (val: number) => {
  pageData.current = val
  getList()
}
const getList = () => {
  const params = {
    subject: subjectObj.value.id,
    current: pageData.current,
    size: pageData.size,
    sort: sort.value,
    status: 0
  }
  loading.value = true
  getLearnReportListApi(params)
    .then((res: any) => {
      let list = res.data ? res.data.records : []
      for(let i of list){
        i.score = Number(i.score)
        i.correctRate = parseFloat((Number(i.correctRate)).toFixed(2))
      }
      testList.value = list
      pageData.total = Number(res.data.total)
      loading.value = false
    })
    .catch(() => {
      loading.value = false
    })
}
const setSubject = (data: any) => {
  pageData.current = 1
  getList()
}
// 判断会员状态
const getIsMember = () => {
    const isVip = useUserStore().memberInfo
    if (isVip) {
      return true
    } else {
      showVip.value = true
      return false
    }
  }
const getTrainingTd = (data: any) => {
  return new Promise((resolve, reject) => {
    let formdata = new FormData()
    formdata.append("reportId", data.reportId)
    formdata.append("noteSource", '3')
    createTrainToAtlasApi(formdata).then((res: any) => {
        if (res.data) {
          resolve(res.data)
            //试卷分析
          }
        }).catch((error) => {
          reject()
        })
  })
}
const getDetails = (trainingId: any) => {
  return new Promise((resolve, reject) => {
    getDetailsApi({trainingId: trainingId}).then((res: any) => {
        if (res.data) {
          resolve(res.data.status)
            //试卷分析
          }
        }).catch((error) => {
          reject()
        })
  })
}
const testDetail = async(data: any) => {
  let trainingId = await getTrainingTd(data)
  let status = await getDetails(trainingId)
  console.log(trainingId)
  console.log(status)
  if (getIsMember()) {
    let url = '/ai_percision/knowledge_graph_detail/paper_detail'
    if (queryData.pageSource == '1') {
        if (status == 2) {
          url = '/true_paper/my_paperT/paper_analysisTM'
        } else {
          //查看试卷
          url = '/true_paper/my_paperT/true_paper_detailM'
        }
    } else {
        if (status == 2) {
          //试卷分析
          url = '/ai_percision/knowledge_graph_detail/paper_analysis'
        } else if (queryData.pageSource == '3') {
          //查看试卷
          url = '/ai_percision/knowledge_graph_detail_unit/paper_detailU'
        }
    }
    router.push({
      path: url,
      query: {
        data: dataEncrypt({
          reportId: data.reportId,
          trainingId: trainingId,
          pageSource: queryData.pageSource
        }),
      }
    })
  }
}

const testDel = (item) => {
  state.visible = true
  handleData = item
}
const delConfirm = () => {
  const params = {
    id: handleData.id,
    reportId: handleData.reportId,
  }
  cancelCollectApi(params)
    .then((res: any) => {
      ElMessage.success("删除成功")
      state.visible = false
      getList()
    })
    .catch(() => {
      state.visible = false
    })
}
</script>
<style lang="scss" scoped>
.container {
  height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height));
  background: #ffffff;
  position: relative;
  .title-coustom {
    width: 11.1875rem;
    height: 2.875rem;
    line-height: 2.875rem;
    text-align: center;
    border-bottom-right-radius: .625rem;
    background: #00c9a3;
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 700;
    position: absolute;
    left: -0.875rem;
    top: .625rem;
    span{
      display: inline-block;
      border-top: .4375rem solid #00886E;
      border-left: .4375rem solid #F5F5F5;
      border-bottom: .4375rem solid #F5F5F5;
      border-right: .4375rem solid #00886E;
      position: absolute;
      bottom: -0.875rem;
      left: 0;

    }
  }
  .filter-box {
    border-bottom: .0625rem solid #EAEAEA;
    padding: 1.875rem 1.25rem 1rem .875rem;
    &-top {
      font-size: 1rem;
      display: flex;
      flex-flow: row-reverse;
      align-items: center;
      margin-bottom: 1.875rem;
      :deep(.el-select) {
        width: 5rem;
        .el-select__wrapper {
          box-shadow: none;
          .el-select__selected-item{
            color: #009c7f;
            font-size: 1rem;
            font-weight: 400;
          }
          .el-icon {
            color: #009c7f;

          }
        }
      }
      .filter-box-top-text {
        margin-left: 2.75rem;
        margin-right: 1.875rem;
        span {
          color: #009c7f;
          font-size: 1rem;
          font-weight: 400;
          margin: 0 .375rem
        }
      }
    }
  }
  .test-box {
    height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height) - 10.3125rem);
    width: 81.25rem;
    overflow-y: auto;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: .625rem 0;
    position: relative;
    box-sizing: border-box;
    .test-wrap{
      width: 100%;
      .hui-line{
        width: calc(100% - 1.75rem);
        border-bottom: .0625rem dashed #EAEAEA;
        margin: 0 0 0 .875rem;
        float: left;
      }
    }
    .pagination-box{
      padding-top: 1.25rem;
    }
    &-item {
      width: 100%;
      height: 6.875rem;
      display: flex;
      padding: 1.25rem 1.25rem 1.25rem 1.25rem;
      box-sizing: border-box;
      &:hover {
        background: #effdfb;
      }
      &-img {
        width: 3.1875rem;
        height: 100%;
        font-size: .75rem;
        background-image: url(@/assets/img/percision/test-img.png);
        background-size: 100%;
        background-repeat: no-repeat;
        text-align: center;
        span {
          display: inline-block;
          margin-top: 1.85rem;
        }
      }
      &-info {
        margin-left: 1rem;
        width: 67.5rem;
        margin-right: 1rem;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        &-title {
          color: #2a2b2a;
          font-size: 1rem;
          font-weight: 400;
        }
        &-data {
          div {
            height: 1.75rem;
            border-radius: .875rem;
            background: #fef8e9;
            color: #ef9d19;
            display: inline-block;
            box-sizing: border-box;
            padding: .375rem .75rem;
            font-size: .75rem;
            margin-right: .625rem;
          }
        }
      }
      &-btn {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        &-it {
          width: 5.25rem;
          height: 1.875rem;
          line-height: 1.875rem;
          border-radius: .25rem;
          font-size: .875rem;
          text-align: center;
          cursor: pointer;
          img {
            width: .875rem;
            height: .875rem;
          }
        }
      }
    }
    .learn-img {
      position: fixed;
      bottom: 1.875rem;
      left: 55%;
      width: 14.0625rem;
      height: 3.125rem;
    }
  }
}
.btn {
  color: #ffffff;
  background: #00c9a3;
}
.red-btn {
  background: #fee9e9;
  color: #dd2a2a;
}
.grey-btn {
  background: #F5F5F5;
  color: #666666;
  margin-right: 2.125rem;
}
.red-text {
  color: #dd2a2a;
}
.blue-text {
  color: #009c7f;
}
.btn-box {
  width: 7.625rem;
  height: 2.375rem;
  line-height: 2.375rem;
  border-radius: 1.1875rem;
}
.dialog-content {
  padding-top: 1.875rem;
  padding-bottom: 2.625rem;
  text-align: center;
  p {
    text-align: center;
    margin-bottom: 1rem;
  }
  img {
    width: 5.5rem;
    height: 6.5rem;
  }
}
.dialog-footer {
  display: flex;
  justify-content: center;
  padding-bottom: 1.875rem;
}
.empty {
  text-align: center;
  padding-top: 13.9375rem;
  img {
    width: 7.4375rem;
    height: 8rem;
  }
  p {
    text-align: center;
    color: #999999;
    font-size: .875rem;
    font-weight: 400;
  }
}
</style>
<style lang="scss">
.filter-select {
  .el-select-dropdown__list {
    padding: 0;
    .el-select-dropdown__item {
      width: 5.25rem;
      height: 3.0625rem;
      line-height: 3.0625rem;
      text-align: center;
      padding: 0;
    }
    .el-select-dropdown__item.is-selected {
      font-weight: 700;
      color: #009c7f;
      background-color: #E5F9F6;
    }
  }
}
</style>
