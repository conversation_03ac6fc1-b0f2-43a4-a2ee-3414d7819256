import { request } from "../../utils/axios"
const api = "/api"
const sdkapi = "/api/xiaoyeoo/sdkapi"

// 拍搜识题
export const picSearchApi = (data: any) => {
  return request({
    url: `${sdkapi}/note/pat/search`,
    method: "POST",
    data,
    headers: { "Content-Type": "multipart/form-data" }
  })
}

// 获取学科错题数
export const noteTotalApi = () => {
  return request({
    url: `${sdkapi}/wrong/statistics`,
    method: "GET",
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

//错题本取待清理错题数与历史清理错题数,已订正总数，未订正总数
export const noteNumsApi = (data: any) => {
  return request({
    url: `${sdkapi}/note/errorNum`,
    method: "POST",
    data
  })
}

// 错题本列表
export const noteListApi = (data: any, current: any, size: any) => {
  return request({
    url: `${sdkapi}/note/list?current=${current}&size=${size}`,
    method: "POST",
    data
  })
}

//导出错题本，下载word
export const exportNoteApi = (data: any) => {
  return request({
    url: `${sdkapi}/note/exportNote`,
    method: "GET",
    params: data,
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

// 删除错题
export const noteDeleteApi = (data: any) => {
  return request({
    url: `${sdkapi}/note/delete`,
    method: "POST",
    data,
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

// 加入错题本
export const addNoteApi = (data: any) => {
  return request({
    url: `${sdkapi}/note/addNote`,
    method: "POST",
    data
  })
}

// 更新错题
export const updateNoteApi = (data: any) => {
  return request({
    url: `${sdkapi}/note/update`,
    method: "POST",
    data
  })
}

// 错题本 相似题训练【举一反三】-逻辑同5步
export const trainingCreateApi = (data: any) => {
  return request({
    url: `${sdkapi}/training/create`,
    method: "POST",
    data,
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

//错题本 错题清理、错题重做创建训练-去订正-逻辑同5步
export const createNoteRedoTrainApi = (data: any) => {
  return request({
    url: `${sdkapi}/training/createNoteRedoTrain`,
    method: "POST",
    data
  })
}

//错题本 错题清理、错题重做保存训练-去订正
export const saveNoteRedoTrainApi = (data: any) => {
  return request({
    url: `${sdkapi}/training/saveNoteRedoTrain`,
    method: "POST",
    data
  })
}
// 错题本列表 To 错题消化(apk_1.0.4)
export const listToDigestionApi = (data: any, current: any, size: any) => {
  return request({
    url: `${sdkapi}/note/listToDigestion?current=${current}&size=${size}`,
    method: "POST",
    data
  })
}
