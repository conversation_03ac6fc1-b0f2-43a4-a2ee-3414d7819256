<template>
    <div class="container">
        <div class="top-box">
            <p>针对您的知识点扫雷练习，为您推荐一下薄弱知识点闯关练习。</p>
            <div class="history" @click="goHistory">
                <img src="@/assets/img/percision/history1.png" alt="history1" >
                历史关卡
            </div>
        </div>
        <div class="middle-box">
            <div class="box" v-if="breakData.length > 0">
                <div v-for="(item, index) in breakData" :key="index" style="display: flex;align-items: center;">
                    <div style="display: flex;align-items: center;flex-direction: column;">
                        <div class="middle-box-item">
                            <div class="middle-box-item-top" :class="item.status == 1?'fail-bg':((item.status == 0 || item.status == 3)?'ready-bg':'')"></div>
                            <div class="middle-box-item-middle" :class="item.status == 1?'star1':((item.status == 0 || item.status == 3)?'star0':(item.correctRate > 80?'star3':'star2'))"></div>
                            <div class="middle-box-item-bottom">
                                个性化闯关 攻克薄弱知识点
                            </div>
                        </div>
                        <div class="img-bg-btn" :class="item.status == 0?'bg-btn':'nobg-btn'" @click="toTraining(item.id)">{{ item.status == 0?'开始闯关':'再闯一次' }}</div>
                    </div>
                    <div class="arrow-box" v-if="index < 2">
                        <img src="@/assets/img/percision/arrow-right.png" alt="arrow" />
                    </div>
                </div>
                
            </div>
            <div v-else class="empty-box">
                <img src="@/assets/img/percision/empty.png" alt="no-data" />
                <p>先去做知识点扫雷再来做薄弱知识点闯关吧</p>
            </div>
        </div>
    </div>
</template>
  
<script lang="ts" setup>
// import { useAuthStore } from '../store'
import { useRouter } from 'vue-router'
import { getStudyChapterApi } from '@/api/analyse'
import { onMounted, ref } from 'vue'
// const authStore = useAuthStore()
const router = useRouter()
import { useUserStore } from "@/store/modules/user"
import { storeToRefs } from 'pinia'
import { dataEncrypt } from '@/utils/secret'
const userStore = useUserStore()
const { subjectObj, learnNow } = storeToRefs(userStore)
const breakData = ref([] as any[])
onMounted(() => {
    getData()
})
const getData = () => {
    getStudyChapterApi({bookId: subjectObj.value.bookId, subject: subjectObj.value.id, type: 2}).then((res: any) => {
        if (res.data) {
            breakData.value = res.data
        } 
    }).catch((err: any) => {

    })
}
const goHistory = () => {
    router.push({
        path: '/ai_percision/to_practice/practice_history'
    })
}
const toTraining = (id: string) => {
    router.push({
        path: '/ai_percision/to_practice/paper_write_switchP',
        query: { 
        data: dataEncrypt({
            individuationId: id,
            chapterTrainType: 7,
            pageSource: '5'
        })
    }
    })
}
</script>
<style lang="scss" scoped>
.container {
    padding: 0 1.25rem;
    background: #ffffff;
    box-sizing: border-box;
    height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height));
    .top-box {
        padding: 1.25rem 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        p {
            font-size: 1rem;
            color: #666;
        }
        .history {
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            width: 6.875rem;
            height: 2.25rem;
            border-radius: .25rem;
            border: .0625rem solid #009c7f;
            background: #ffffff;
            color: #009c7f;
            img {
                width: 1rem;
                height: 1rem;
                margin-right: .5rem
            }
        }
    }
    .middle-box {
        padding: 0 1.875rem;
        margin-top: 5.125rem;
        .box {
            display: flex;
            .middle-box-item {
                margin-left: 3.625rem;
                width: 15.3125rem;
                height: 15.3125rem;
                border-radius: 1.25rem;
                border: .75rem solid #FDF5E8;
                box-shadow: .3125rem .3125rem 0rem 0rem #F8DDB3;
                box-sizing: border-box;
                display: flex;
                align-items: center;
                flex-direction: column;
                &-top {
                    width: 14.8125rem;
                    height: 6.375rem;
                    margin-top: -3.875rem;
                    margin-left: 1.4375rem;
                    background-image: url(@/assets/img/percision/break-success.png);
                    background-size: cover;
                    background-repeat: no-repeat;
                }
                .ready-bg {
                    background-image: url(@/assets/img/percision/ready-break.png);
                }
                .fail-bg {
                    background-image: url(@/assets/img/percision/break-fail.png);
                }
                &-middle {
                    width: 10.1875rem;
                    height: 4.375rem;
                    margin-top: 1.875rem;
                    background-image: url(@/assets/img/percision/star3.png);
                    background-size: cover;
                    background-repeat: no-repeat;
                }
                .star1 {
                    background-image: url(@/assets/img/percision/star1.png);
                }
                .star2 {
                    background-image: url(@/assets/img/percision/star2.png);
                }
                .star3 {
                    background-image: url(@/assets/img/percision/star3.png);
                }
                .star0 {
                    background-image: url(@/assets/img/percision/star0.png);
                }
                &-bottom {
                    color: #ef9d19;
                    font-size: .75rem;
                    font-weight: 400;
                    margin-top: 3.125rem;
                }
            }
            .arrow-box {
                padding-bottom: 3.125rem;
                margin-left: 3.625rem;
                img {
                    width: 3.625rem;
                    height: 1.4375rem;
                }
            }
        }
    }
}
.img-bg-btn {
    width: 7rem;
    height: 2.25rem;
    line-height: 2.1875rem;
    color: #ffffff;
    font-size: .875rem;
    font-weight: 400;
    text-align: center;
    margin-left: 3.625rem;
    cursor: pointer;
    margin-top: 1.25rem;
}
.bg-btn {
    background: url(@/assets/img/percision/learn-btn-bg.png);
    background-size: 7rem 2.1875rem;
    background-repeat: no-repeat;
}
.nobg-btn {
    background: #e5f9f6;
    color: #009c7f;
    border-radius: 1.125rem;
}
.empty-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 6.25rem;
    img {
        width: 7.4375rem;
        height: 8rem;
    }
    p {
        color: #999999;
        font-size: .875rem;
        font-weight: 400;
    }
}
</style>