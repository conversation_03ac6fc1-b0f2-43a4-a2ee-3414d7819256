<template>
    <el-pagination class="pagination" :layout="layout" :total="total" :current="current" :page-sizes="[10, 20, 30, 50]"
    @change="currentSizeChange" @prev-click="prevClick" @next-click="nextClick" />
</template>
<script lang="ts" setup>
import { computed } from 'vue'

// 定义组件接收的prop属性
const props = defineProps({
    total: {
        type: Number,
        default: 0
    },
    layout: {
        type: String,
        default: "total, sizes, prev, pager, next, jumper"
    },
    current: {
        type: Number,
        default: 1
    }
})
const emit = defineEmits(['currentSizeChange', 'pageClick'])
const currentSizeChange = (currentPage: number, pageSize: number) => {
    emit('currentSizeChange', currentPage, pageSize)
}
const prevClick = (val: number) => {
    emit('pageClick', val)
}
const nextClick = (val: number) => {
    emit('pageClick', val)
}
</script>