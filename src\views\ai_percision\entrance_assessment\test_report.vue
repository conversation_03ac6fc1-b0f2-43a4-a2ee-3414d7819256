<!--
使用说明：
1. 页面加载时会自动进行截图并生成二维码
2. 如果需要重新生成，可以点击"分享报告"按钮
3. 需要安装html2canvas依赖：npm install html2canvas
4. 生成的二维码包含上传到服务器的截图URL，可以扫码分享
-->
<template>
  <div class="test-report-main" >
    <div class="test-report-container" >
      <!-- 顶部横幅 -->
      <div class="top-banner">
        <img class="not-img" width="22px" height="22px" src="@/assets/img/entranceAssessment/smllbg.png" alt="">
        <span class="banner-text">{{reportData?.title}}</span>
      </div>

      <!-- 主要信息卡片 -->
      <div class="main-card">
        <div class="card-content">
          <!-- 分数圆环和用户信息 -->
          <div class="score-user-section">
            <div class="score-circle">
              <div class="circle-progress" :style="{ '--score-percentage': reportData.score }">
                <div class="circle-inner">
                  <div class="score-number">{{ formatScore(reportData.correctRate*1) }}</div>
                  <div class="score-unit">分</div>
                </div>
              </div>
            </div>
            
        <!-- <iframe
          src="https://tiku-test.xiaoyeoo.com/loginIs?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJvcmdOYW1lIjoiY2VzaGkxIiwic2V4IjoiMCIsImVkdWNhdGlvblVzZXJJZCI6MTkzNzcwMDU3MzE0MzQ4NjQ2Niwib3JnSWQiOjIyNjgyMCwiaXNBZGRPcmciOmZhbHNlLCJvcmdUeXBlIjo1LCJuYmYiOjE3NTEzNTcxMDEsInBob25lIjoiMTg5ODc4OTg3ODkiLCJyb2xlS2V5Ijoib3JnX2FkbWluX2luaXQiLCJuYW1lIjoiY2VzaGkiLCJpc0F1dGhvcml6ZVZpcCI6MSwiaWQiOjEyNDA4OSwiZXhwIjoxNzUxNDQzNTAxLCJhbmNlc3RvcnMiOiIwLDIyNTkwMCIsImlhdCI6MTc1MTM1NzEwMX0.sywl1W6tXBjqenpTfMi51A6r20o7J9UNcym35DFxVM8"
          width="800"
          height="1400"
        /> -->
            <div class="user-info-wrapper">
              <!-- 测评日期 -->
              <div class="test-date-tag">
                测评日期: {{reportData?.createTime}}
              </div>
              
              <!-- 用户信息行 -->
              <div class="user-main-info">
                <div class="user-avatar">
                  <img :src="learnNow.avatar || '/api/placeholder/48/48'" alt="头像"  class="avatar-img" />
                </div>
                <div class="user-details">
                  <div class="user-name-row">
                    <span class="user-name">{{ learnNow.nickName || '用户' }}</span>
                    <span class="grade-info">{{learnUsers[0].gradeName}}{{ learnUsers[0].termId == 1 ? '上学期' : '下学期' }}</span> 
                  </div>
                </div>
              </div>
              
              <!-- 测评内容 -->
              <div class="test-content-desc">
               <span style="color:#5A85EC"> 测评内容:</span> {{reportData?.title}}
              </div>
            </div>
            
            <div class="qr-wrapper">
              <div class="qr-card">
                <div class="qr-code-container" ref="qrCodeContainer">
                  <!-- 加载状态 -->
                  <div v-if="screenshotLoading" class="qr-loading">
                    <div class="loading-spinner"></div>
                    <div class="loading-text">生成中...</div>
                  </div>
                  <!-- 二维码canvas -->
                  <canvas v-else ref="qrCanvas" width="80" height="80"></canvas>
                </div>
                <div class="qr-text">扫码分享报告</div>
              </div>
            </div>
          </div>
        </div>
      </div>

        <!-- 评分详情 -->
        <div class="grade-details">
          <div class="grade-content">
            <div class="left-content">
              <div class="grade-description">
                根据测评得分、测评用时、试题难度等综合评估
              </div>
              <div class="current-grade">
                <div class="grade-label">当前测评等级为：</div>
                <div class="grade-display">
                  <!-- <img class="not-img" width="160px" height="73px" src="@/assets/img/entranceAssessment/score.png" alt=""> -->
                  <img class="link-img"  width="160px" height="73px" :src="getScoreImages(reportData.correctRate)" alt="">
                </div>
              </div>
            </div>
            
            <div class="grade-pyramid">
              <div class="pyramid-container" style="display: flex;">
                <!-- <img class="link-img" :src="getScoreImage(scope.row.score)" alt=""> -->
                <!-- <img class="link-img"  width="160px" height="73px" :src="getScoreImage(reportData.correctRate)" alt=""> -->
                <img class="not-img" style="margin-top: 30px;" width="360px" height="160px" :src="getScoreImage(reportData.correctRate)" alt="">
                <!-- <div class="pyramid-main">
                  <div class="pyramid-level level-a-plus triangle">
                    <div class="pyramid-shape">
                      <span class="level-text">A+</span>
                    </div>
                  </div>
                  <div class="pyramid-level level-a">
                    <div class="pyramid-shape">
                      <span class="level-text">A</span>
                    </div>
                  </div>
                  <div class="pyramid-level level-b-plus current-level">
                    <div class="pyramid-shape">
                      <span class="level-text">B+</span>
                    </div>
                  </div>
                  <div class="pyramid-level level-b">
                    <div class="pyramid-shape">
                      <span class="level-text">B</span>
                    </div>
                  </div>
                  <div class="pyramid-level level-c">
                    <div class="pyramid-shape">
                      <span class="level-text">C</span>
                    </div>
                  </div>
                </div> -->
              </div>
            </div>
          </div>
        </div>

      <!-- 统计卡片区域 -->
      <div class="stats-section">
        <div class="section-title-bar">
          <span class="title-number">1.</span>
          <span class="title-text">总体情况</span>
        </div>
        
        <div class="stats-grid">
          <!-- 左上：答题数简单版 -->   
          <div class="stat-card stat-answer back">
            <div class="answer-card-header">
              <h3 class="answer-card-title">答题数</h3>
              <!-- <span class="answer-correct-info">正确题数：4题</span> -->
              <div class="answer-left-section">
    
                <div class="answer-info">
                  <img width="26px" height="26px" src="@/assets/img/entranceAssessment/dts.png" alt="">
                  <span class="answer-label">答题数</span>
                  <span class="number">{{ reportData.quesCount }}</span>
                  <span class="unit">题</span>
                </div>
              </div>
              
            </div>
            
            <div class="answer-card-content">
       
              <div class="answer-chart-container">
                <div ref="chartContainer1" class="answer-ring-chart"></div>
              </div>
            </div>
          </div>
          
          <!-- 右上：得分简单版 -->
          <div class="stat-card stat-answer back">
            <div class="answer-card-header">
              <h3 class="answer-card-title">得分</h3>
              <!-- <span class="answer-correct-info">正确题数：4题</span> -->
              <div class="answer-left-section">
    
                <div class="answer-info">
                  <img width="26px" height="26px" src="@/assets/img/entranceAssessment/df.png" alt="">
                  <span class="answer-label">得分</span>
                  <span class="number">{{ formatScore(reportData.correctRate*1) }}</span>
                  <span class="unit">分</span>
                </div>
              </div>
              
            </div>
            
            <div class="answer-card-content">
       
              <div class="answer-chart-container">
                <div ref="chartContainer2" class="answer-ring-chart"></div>
              </div>
            </div>
          </div>
          <!-- 左下：答题数详细版 -->
          <div class="stat-card answers-box"  style="padding: 0 20px;height: 215px;">
            <div class="card-header" style="padding: 0 20px;">
              <h3 class="card-title left-num">答题数</h3>
              <div class="accuracy-section">
                <img width="26px" height="26px" src="@/assets/img/entranceAssessment/zql.png" alt="">
                <div class="accuracy-text">正确率</div>
                <div class="accuracy-number">{{ formatPercentage(reportData.correctRate) }}<span class="percent">%</span></div>
              </div>
            </div>
            <div class="card-content answers-content ishei">
   
              <div class="circles-section">
                <div class="circle-item">
                  <div class="circle-label">答题总数</div>
                  <div class="circle-value total">{{ formatScore(reportData?.quesCount*1) }}  </div>
                </div>
                <div class="circle-item">
                  <div class="circle-label">答对题数</div>
                  <div class="circle-value correct">{{ formatScore(reportData?.correct*1) }}  </div>
                </div>
                <div class="circle-item">
                  <div class="circle-label">答错题数</div>
                  <div class="circle-value wrong">{{ formatScore(reportData?.mistake*1) }} </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 右下：得分详细版 -->
          <div class="stat-card score-time-box"  style="padding: 0 20px;height: 215px;">
            <!-- <div class="warning-text">您超出规定答题时间12分17秒，评级在得分基础上下调一级哦</div> -->

            <div class="card-header" style="padding-top: 30px;">
              <h3 class="card-title">得分</h3>
            </div>
            <div class="card-content time-content">
              <div class="time-columns">
                <div class="time-column">
                  <div class="time-flex">
                    <img width="26px" height="26px" src="@/assets/img/entranceAssessment/dtsc.png" alt="">
                    <div class="time-label">答题时长</div>
                  </div>

                  <!-- {{ reportData?.trainTime }} -->
                  <div class="time-value overtime">{{ formattedTrainTime.value}}<span class="time-unit">{{formattedTrainTime.unit}}</span></div>
                </div>
                <div class="time-column">
                  <div class="time-flex">
                    <img width="26px" height="26px" src="@/assets/img/entranceAssessment/dtsc.png" alt="">
                    <div class="time-label">规定时长</div>
                  </div>
                  <div class="time-value normal">{{ reportData.times ? reportData.times : 25}}<span class="time-unit">分钟</span></div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>

      <!-- 知识点分析 -->
      <div class="knowledge-section">
        <div class="section-title-bar">
          <span class="title-number">2.</span>
          <span class="title-text">知识点掌握情况分析</span>
        </div>
        
        <div class="knowledge-list">
          <div 
            v-for="(item, index) in reportData?.reportJson?.pointJson" 
            :key="index"
            class="knowledge-row"
          >
            <div class="knowledge-title">{{ item.pointName }}</div>
            <div class="knowledge-bar">
              <div 
                class="bar-fill" 
                :style="{ width: formatPercentage(item.accuracy*100) + '%', backgroundColor: getKnowledgeColor(formatPercentage(item.accuracy*100)) }"
              ></div>
            </div>
            <div class="knowledge-percentage">掌握度 {{ formatPercentage(item.accuracy*100) }}%</div>
          </div>
        </div>
      </div>

      <!-- 薄弱知识点分析 -->
      <div class="weakness-section">
        <div class="section-title-bar">
          <span class="title-number">3.</span>
          <span class="title-text">薄弱知识点分析</span>
        </div>
        
        <div class="weakness-table">
          <el-table :data="reportData?.reportJson?.pointJson" style="width: 100%" :header-cell-style="{ background: '#f8f9fa', color: '#333' }">
            <el-table-column prop="status" label="掌握情况" width="120" align="center">
              <template #default="scope">
                <div class="status-tag" :class="getStatusClass(scope.row.status)">
                  {{ getStatus(scope.row.status) }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="pointName" label="知识点"/>
            <el-table-column prop="promote" label="知识点类型"  align="center" >
              <template #default="scope">
                <div>
                  {{scope.row.promote?scope.row.promote:'-'}}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="score" label="分值"  align="center" >
              <template #default="scope">
                <div>
                  {{scope.row.score?scope.row.score:'-'}}
                </div>
              </template>
            </el-table-column>
            <!-- <el-table-column prop="ratio" label="占比"  align="center" >
              <template #default="scope">
                {{ formatPercentage(scope.row.ratio*100) }}%
              </template>
            </el-table-column> -->
          </el-table>
        </div>
      </div>

      <div class="section-title-bar" style="margin-top: 36px;">
          <span class="title-number">4.</span>
          <span class="title-text">学习推荐</span>
        </div>
      <!-- 学习推荐 -->
      <div class="suggestion-section" style="margin-top: 0;">
        
        <div class="suggestion-content">
          <div class="suggestion-top">
            <div class="suggestion-header">
              <span class="suggestion-icon">
                <img style="width: 22px ; height: 22px;margin-right: 10px;" src="@/assets/img/entranceAssessment/xx.png" />
              </span>
              <span class="suggestion-title">
                <img style="width: 104px ; height: 27px;" src="@/assets/img/entranceAssessment/xxtj.png" />
              </span>
            </div>
            <div class="suggestion-description">
              学习一下面几个知识点再次测评，你的成绩肯定以提高高不少哦。
            </div>
          </div>
          <div class="suggestion-tags">
            <!-- {{ reportData?.reportJson?.pointJson }} -->
            <template v-if="weakKnowledgePoints.length > 0">
              <!-- {{ weakKnowledgePoints }} -->
            <div 
                v-for="(point, index) in reportData?.reportJson?.pointJson" 
              :key="index"
                class="suggestion-chip weak-point"
                style="display: flex;background: rgb(245, 245, 245);cursor: pointer;"
                @click="onGovideo(point)"
            >
              <img style="width: 16px ; height: 16px;align-items: center;justify-content: center;display: flex;margin-right: 5px;margin-top: 2px;" src="@/assets/img/note/wkplay.svg" />
                {{ point.pointName }}
            </div>
            </template>
            <template v-else>
              <div class="suggestion-chip no-weak-points">
                所有知识点掌握良好
              </div>
            </template>
          </div>
          <div class="suggestion-buttons">
            <el-button class="btn-record" @click="answerRecord()">答题记录</el-button>
            <!-- <el-button class="btn-capture" @click="testCapture" :loading="screenshotLoading">
              {{ screenshotLoading ? '截图中...' : '测试截图' }}
            </el-button> -->
            <el-button type="primary" class="btn-correct" @click="continueStudy">订正错题</el-button>
          </div>
        </div>
      </div>

  
    </div>
  </div>
</template>

<script lang="ts" setup>

import { Close } from '@element-plus/icons-vue'
import { useUserStore } from '@/store/modules/user'
import { storeToRefs } from 'pinia'
import subjectSelect from "@/views/components/subjectSelect/index.vue"
import { getChapterReportListApi } from "@/api/report"

import { ref, reactive, onMounted, onUnmounted, nextTick, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import QRCode from 'qrcode'
import * as echarts from 'echarts'
import { userAnalyseToPointApi } from "@/api/analyse"
import { uploadApi } from "@/api/user"
import html2canvas from 'html2canvas'

import { getDetailssApi } from "@/api/training"

// TypeScript类型声明
interface UserInfo {
  avatar?: string;
  nickName?: string;
  [key: string]: any;
}

const learnUsers = JSON.parse(localStorage.learnUsers || "[]") || []

const router = useRouter()
const route = useRoute()

// 获取用户store
const userStore = useUserStore()
const learnNow = computed(() => userStore.learnNow as UserInfo || {})

// 模板引用
const qrCanvas = ref<HTMLCanvasElement>()
const qrCodeContainer = ref<HTMLDivElement>()
const chartContainer1 = ref<HTMLDivElement>()
const chartContainer2 = ref<HTMLDivElement>()
const testReportMainRef = ref<HTMLDivElement>()

// 预先导入所有评分图片
import asImg from '@/assets/img/entranceAssessment/pyramidas.png'
import aImg from '@/assets/img/entranceAssessment/pyramida.png'
import bsImg from '@/assets/img/entranceAssessment/pyramidbs.png'
import bImg from '@/assets/img/entranceAssessment/pyramidb.png'
import cImg from '@/assets/img/entranceAssessment/pyramidc.png'
import { dataEncrypt, dataDecrypt, mergeObject } from "@/utils/secret"
import smasImg from '@/assets/img/entranceAssessment/as.png'
import smaImg from '@/assets/img/entranceAssessment/a.png'
import smbsImg from '@/assets/img/entranceAssessment/bs.png'
import smbImg from '@/assets/img/entranceAssessment/b.png'
import smcImg from '@/assets/img/entranceAssessment/c.png'

const timeState = reactive({
    hours: 0,
    minutes: 0,
    seconds: 0
})

// 根据分数获取对应的图片
const getScoreImage = (score: number) => {
  if (score >= 90) return asImg    // A+ 图片
  if (score >= 80) return aImg     // A 图片
  if (score >= 70) return bsImg    // B+ 图片
  if (score >= 60) return bImg     // B 图片
  return cImg                      // C 图片（60分以下）
}

const getScoreImages = (score: number) => {
  if (score >= 90) return smasImg    // A+ 图片
  if (score >= 80) return smaImg     // A 图片
  if (score >= 70) return smbsImg    // B+ 图片
  if (score >= 60) return smbImg     // B 图片
  return smcImg                      // C 图片（60分以下）
}

// 加载状态
const loading = ref(false)
const screenshotLoading = ref(false)
const uploadedImageUrl = ref<string>('')
const queryData = route.query.data ? dataDecrypt(route.query.data) : {}
// 清理函数存储
const chartCleanupFunctions = ref<(() => void)[]>([])

// 测试报告数据
const reportData:any = reactive({
    "trainingId" : "",
    "sourceId" : "",
    "bookId" : "",
    "subject" : 0,
    "source" : 0,
    "title" : "",
    "correct" : "",
    "mistake" : "",
    "correctRate" : "",
    "quesCount" : 0,
    "degree" : null,
    "score" : "0.0",
    "status" : 0,
    "spanTime" : "",
    "createTime" : "",
    "times" : null,
    "trainTime" : "0",
    "reportJson" : {
      "pointJson" :[],
      "quesDegree" :[],
      "pointVos" : null
    },
    "items" : [],
    "reviseCount" : 0,
    "chapterTrainType" : 0,
    "content" : null,
    "pointIds" : [],
    "integral" : null,
    "hierarchy" : 0
})

// 知识点分析数据
const knowledgePoints = ref([
  { name: '人工智能基础概念理解', accuracy: 100 },
  { name: '机器学习算法应用', accuracy: 83 },
  { name: '深度学习原理掌握', accuracy: 75 },
  { name: '自然语言处理技术', accuracy: 90 },
  { name: '计算机视觉应用', accuracy: 67 },
  { name: '人工智能伦理问题', accuracy: 95 },
  { name: '智能系统设计', accuracy: 80 },
  { name: '数据分析与处理', accuracy: 88 }
])

// 能力分析表格数据
const abilityData = ref([
  { 
    status: '未掌握',
    statusClass: 'not-mastered',
    knowledge: '5以内的减法', 
    type: '重点知识点', 
    score: '22%',
    ratio: '8.7%'
  },
  { 
    status: '一般',
    statusClass: 'average',
    knowledge: '填符号组算式', 
    type: '难点知识点', 
    score: '17%',
    ratio: '4.3%'
  },
  { 
    status: '已掌握',
    statusClass: 'mastered',
    knowledge: '6减几的减法', 
    type: '重点知识点', 
    score: '61%',
    ratio: '8.7%'
  },
  { 
    status: '已掌握',
    statusClass: 'mastered',
    knowledge: '等于7的加法', 
    type: '难点知识点', 
    score: '0%',
    ratio: '4.3%'
  },
  { 
    status: '一般',
    statusClass: 'average',
    knowledge: '7减几的减法', 
    type: '重点知识点', 
    score: '0%',
    ratio: '4.3%'
  }
])

// 学习建议
const suggestions = ref([
  '知识点名称',
  '知识点名称知识点名称知识点名称知识点名称',
  '知识点名称知识点名称知识点名称知识点名称',
  '知识点名称知识点名称知识点名称知识点名称',
  '知识点名称',
  '知识点名称知识点名称知识点名称',
  '知识点名称知识点名称知识点名称知识点名称'
])

// 格式化后的时间 - 使用computed避免重复计算
const formattedTrainTime = computed(() => formatTime(reportData?.trainTime * 1))
const formattedTime = computed(() => formatTime(reportData?.time * 1))

// 筛选掌握度低于60%的知识点
const weakKnowledgePoints = computed(() => {

  if (!reportData?.reportJson?.pointJson) return []
  
  return reportData.reportJson.pointJson.filter(item => {
    const accuracy = formatPercentage(item.accuracy*100)
    return accuracy < 60//改这里的百分比就可以
  }).map(item => item.pointName || '知识点')
})

const onGovideo = (val:any) =>{
  router.push({
    path: '/note/wkvideo2',
    query: {
      pointId: val.pointId,
      type:'note',
      subject:reportData.subject,
      noteId:queryData.reportId,
      isShow:'hide'
    }
  })
}

//状态
const getStatus = (status) => { 
  if (status == 1) { 
    return '已掌握'
  }else  if (status == 2) { 
    return '不过关'
  }else  if (status == 3) { 
    return '未掌握'
  }
}
//状态clsss
const getStatusClass = (status) => { 
  if (status == 1) { 
    return 'mastered'
  }else  if (status == 2) { 
    return 'not-mastered'
  }else  if (status == 3) { 
    return 'average'
  }
}

// 截图并上传功能
const captureAndUpload = async (): Promise<string> => {
  try {
    screenshotLoading.value = true
    
    // 创建遮罩层，避免用户看到截图过程中的样式变化
    const overlay = document.createElement('div')
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(2px);
      z-index: 9999;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      pointer-events: none;
    `
    
    const loadingContent = document.createElement('div')
    loadingContent.innerHTML = `
      <div style="display: flex; flex-direction: column; align-items: center; color: #5A85EC;">
        <div style="width: 24px; height: 24px; border: 2px solid #f3f3f3; border-top: 2px solid #5A85EC; border-radius: 50%; animation: spin 1s linear infinite; margin-bottom: 12px;"></div>
        <div style="font-size: 14px; font-weight: 500;">正在生成截图...</div>
      </div>
    `
    overlay.appendChild(loadingContent)
    document.body.appendChild(overlay)
    
    // 等待DOM完全渲染和所有图片加载
    await nextTick()
    
    // 等待所有图片加载完成
    await waitForImages()
    
    // 额外等待时间确保所有内容渲染完成
    await new Promise(resolve => setTimeout(resolve, 800))
    
    const testReportMain = document.querySelector('.test-report-main') as HTMLElement
    
    if (!testReportMain) {
      throw new Error('找不到要截图的元素')
    }

    // 保存当前滚动位置
    const originalScrollTop = testReportMain.scrollTop
    const originalScrollLeft = testReportMain.scrollLeft
    
    // 获取容器的完整尺寸（包括滚动内容）
    const containerScrollHeight = 2700
    const containerScrollWidth = testReportMain.scrollWidth
    
    // 临时修改容器样式以确保内容完全可见
    const originalStyles = {
      height: testReportMain.style.height,
      overflow: testReportMain.style.overflow,
      position: testReportMain.style.position,
      visibility: testReportMain.style.visibility
    }
    
    // 在修改样式前先隐藏容器
    testReportMain.style.visibility = 'hidden'
    
    // 使用 requestAnimationFrame 确保样式修改在合适的时机进行
    await new Promise(resolve => requestAnimationFrame(resolve))
    
    // 设置容器样式以显示所有内容
    testReportMain.style.height = containerScrollHeight + 'px'
    testReportMain.style.overflow = 'visible'
    // testReportMain.style.position = 'static'
    
    // 滚动到顶部
    testReportMain.scrollTo(0, 0)
    
    // 隐藏二维码区域，不让它出现在截图中
    const qrWrapper = document.querySelector('.qr-wrapper') as HTMLElement
    const originalQrDisplay = qrWrapper ? qrWrapper.style.opacity : ''
    if (qrWrapper) {
      qrWrapper.style.opacity = '0'
    }
    
    // 恢复容器可见性用于截图
    testReportMain.style.visibility = 'visible'
    
    console.log('截图容器尺寸:', { 
      width: containerScrollWidth, 
      height: containerScrollHeight
    })

    // 使用优化的html2canvas配置
    const canvas = await html2canvas(testReportMain, {
      backgroundColor: '#ffffff',
      scale: 1, // 降低scale避免性能问题
      logging: false, // 关闭日志避免控制台污染
      useCORS: true, // 允许跨域图片
      allowTaint: true, // 允许污染画布，解决跨域问题
      foreignObjectRendering: false, // 禁用外部对象渲染
      imageTimeout: 60000, // 增加图片加载超时时间
      removeContainer: false,
      height: containerScrollHeight,
      width: containerScrollWidth,
      scrollX: 0,
      scrollY: 0,
      windowWidth: window.innerWidth,
      windowHeight: window.innerHeight,
      x: 0,
      y: 0,
      // 处理跨域图片
      proxy: undefined, // 不使用代理
      onclone: async (clonedDoc: Document, element: HTMLElement) => {
        // 在克隆的文档中调整样式
        const clonedElement = clonedDoc.querySelector('.test-report-main') as HTMLElement
        if (clonedElement) {
          // 确保克隆元素显示所有内容
          clonedElement.style.height = containerScrollHeight + 'px'
          clonedElement.style.overflow = 'visible'
          clonedElement.style.position = 'static'
          clonedElement.style.transform = 'none'
          clonedElement.style.webkitTransform = 'none'
          clonedElement.style.maxHeight = 'none'
          clonedElement.style.minHeight = containerScrollHeight + 'px'
          
          // 在克隆文档中也隐藏二维码区域
          const clonedQrWrapper = clonedElement.querySelector('.qr-wrapper') as HTMLElement
          if (clonedQrWrapper) {
            clonedQrWrapper.style.display = 'none'
          }
          
          // 处理容器内部样式
          const container = clonedElement.querySelector('.test-report-container') as HTMLElement
          if (container) {
            container.style.height = 'auto'
            container.style.minHeight = containerScrollHeight + 'px'
            container.style.maxHeight = 'none'
            container.style.overflow = 'visible'
            container.style.paddingBottom = '50px'
          }
          
          // 特别处理头像图片的跨域问题
          const avatarImg = clonedElement.querySelector('.avatar-img') as HTMLImageElement
          if (avatarImg && avatarImg.src) {
            try {
              // 如果是外部头像URL，尝试转换为base64或使用默认头像
              if (!avatarImg.src.startsWith('/') && !avatarImg.src.includes(window.location.origin)) {
                console.log('处理跨域头像:', avatarImg.src)
                
                // 尝试将头像转换为base64
                const base64Avatar = await convertImageToBase64(avatarImg.src)
                if (base64Avatar) {
                  avatarImg.src = base64Avatar
                  console.log('头像已转换为base64')
                } else {
                  // 如果转换失败，使用默认头像
                  avatarImg.src = generateDefaultAvatar(learnNow.value.nickName || '用户')
                  console.log('使用默认头像')
                }
              }
            } catch (error) {
              console.warn('处理头像失败，使用默认头像:', error)
              avatarImg.src = generateDefaultAvatar(learnNow.value.nickName || '用户')
            }
          }
          
          // 处理所有其他图片的跨域问题
          const images = clonedElement.querySelectorAll('img:not(.avatar-img)') as NodeListOf<HTMLImageElement>
          images.forEach((img) => {
            // 设置图片的crossOrigin属性
            img.crossOrigin = 'anonymous'
            
            // 如果是相对路径或本域图片，保持原样
            if (img.src.startsWith('/') || img.src.includes(window.location.origin)) {
              return
            }
            
            // 对于外部图片，尝试通过代理或转换为base64
            try {
              // 这里可以添加图片代理逻辑
              // console.log('处理外部图片:', img.src)
            } catch (error) {
              console.warn('处理图片失败:', img.src, error)
              // 设置默认图片或移除src
              img.style.display = 'none'
            }
          })
          
    
        }
        
        // 等待克隆文档中的图片加载
        return waitForImagesInDocument(clonedDoc)
      }
    })

    console.log('截图完成，canvas尺寸:', { width: canvas.width, height: canvas.height })

    // 使用 requestAnimationFrame 来平滑地恢复样式
    await new Promise(resolve => requestAnimationFrame(() => {
      // 恢复原始样式
      testReportMain.style.height = originalStyles.height
      testReportMain.style.overflow = originalStyles.overflow
      testReportMain.style.position = originalStyles.position
      testReportMain.style.visibility = originalStyles.visibility
      
      // 恢复二维码区域显示
      if (qrWrapper) {
        qrWrapper.style.opacity = '1'
      }
      
      // 恢复滚动位置
      testReportMain.scrollTo(originalScrollLeft, originalScrollTop)
      
      resolve(void 0)
    }))
    
    // 移除遮罩层
    if (overlay && overlay.parentNode) {
      overlay.parentNode.removeChild(overlay)
    }

    // 将canvas转换为blob
    return new Promise((resolve, reject) => {
      canvas.toBlob(async (blob: Blob | null) => {
        if (!blob) {
          reject(new Error('截图生成失败'))
          return
        }

        try {
          // 创建FormData上传图片
          const formData = new FormData()
          const fileName = `test_report_${reportData.trainingId}_${Date.now()}.png`
          formData.append('file', blob, fileName)

          console.log('开始上传图片，文件大小:', blob.size)

          // 调用上传API
          const uploadResponse = await uploadApi(formData) as any
          
          if (uploadResponse.code === 200 && uploadResponse.data?.url) {
            const imageUrl = `${uploadResponse.data.url}${uploadResponse.data.key}`
            uploadedImageUrl.value = imageUrl
            
            // 将图片URL存储到本地缓存，使用trainingId作为key
            saveImageToCache(reportData.trainingId, imageUrl)
            
            console.log('图片上传成功:', imageUrl)
            resolve(imageUrl)
          } else {
            console.error('上传API返回错误:', uploadResponse)
            reject(new Error('上传失败'))
          }
        } catch (error) {
          console.error('上传图片失败:', error)
          reject(error)
        }
      }, 'image/png', 0.8) // 适当降低质量以减小文件大小
    })
  } catch (error) {
    console.error('截图失败:', error)
    ElMessage.error('截图失败: ' + (error as Error).message)
    // 如果截图失败，返回默认分享链接
    const defaultUrl = `${window.location.origin}/report/share?testId=${reportData.testId || 'demo'}&score=${reportData.score}`
    return defaultUrl
  } finally {
    screenshotLoading.value = false
    
    // 确保遮罩层被移除
    const overlay = document.querySelector('div[style*="backdrop-filter: blur(2px)"]')
    if (overlay && overlay.parentNode) {
      overlay.parentNode.removeChild(overlay)
    }
  }
}

// 将图片转换为base64格式
const convertImageToBase64 = (url: string): Promise<string | null> => {
  return new Promise((resolve) => {
    try {
      const img = new Image()
      img.crossOrigin = 'anonymous' // 设置跨域属性
      
      img.onload = () => {
        try {
          const canvas = document.createElement('canvas')
          const ctx = canvas.getContext('2d')
          
          if (!ctx) {
            resolve(null)
            return
          }
          
          canvas.width = img.width
          canvas.height = img.height
          ctx.drawImage(img, 0, 0)
          
          const dataURL = canvas.toDataURL('image/png')
          resolve(dataURL)
        } catch (error) {
          console.warn('转换base64失败:', error)
          resolve(null)
        }
      }
      
      img.onerror = () => {
        console.warn('加载图片失败:', url)
        resolve(null)
      }
      
      // 设置超时
      setTimeout(() => {
        resolve(null)
      }, 5000)
      
      img.src = url
    } catch (error) {
      console.warn('创建图片对象失败:', error)
      resolve(null)
    }
  })
}

// 生成默认头像（简单的字母头像）
const generateDefaultAvatar = (name: string): string => {
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  
  if (!ctx) {
    return '/api/placeholder/48/48'
  }
  
  canvas.width = 48
  canvas.height = 48
  
  // 背景色（使用名字生成颜色）
  const colors = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', 
    '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F'
  ]
  const colorIndex = name.charCodeAt(0) % colors.length
  const selectedColor = colors[colorIndex] || '#4ECDC4' // 确保有默认值
  ctx.fillStyle = selectedColor
  ctx.fillRect(0, 0, 48, 48)
  
  // 绘制文字
  ctx.fillStyle = '#FFFFFF'
  ctx.font = 'bold 18px Arial'
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'
  
  // 取名字的第一个字符
  const firstChar = name.charAt(0).toUpperCase()
  ctx.fillText(firstChar, 24, 24)
  
  return canvas.toDataURL('image/png')
}

// 本地缓存相关函数
const CACHE_KEY_PREFIX = 'test_report_image_'
const CACHE_EXPIRY_DAYS = 7 // 缓存7天

// 保存图片到本地缓存
const saveImageToCache = (trainingId: string, imageUrl: string) => {
  try {
    const cacheData = {
      url: imageUrl,
      timestamp: Date.now(),
      trainingId: trainingId
    }
    localStorage.setItem(`${CACHE_KEY_PREFIX}${trainingId}`, JSON.stringify(cacheData))
    console.log('图片已缓存到本地:', trainingId, imageUrl)
  } catch (error) {
    console.warn('保存图片缓存失败:', error)
  }
}

// 从本地缓存获取图片
const getImageFromCache = (trainingId: string): string | null => {
  try {
    const cacheStr = localStorage.getItem(`${CACHE_KEY_PREFIX}${trainingId}`)
    if (!cacheStr) {
      return null
    }
    
    const cacheData = JSON.parse(cacheStr)
    const now = Date.now()
    const cacheAge = now - cacheData.timestamp
    const maxAge = CACHE_EXPIRY_DAYS * 24 * 60 * 60 * 1000 // 转换为毫秒
    
    // 检查缓存是否过期
    if (cacheAge > maxAge) {
      localStorage.removeItem(`${CACHE_KEY_PREFIX}${trainingId}`)
      console.log('缓存已过期，已清除:', trainingId)
      return null
    }
    
    console.log('从缓存获取图片:', trainingId, cacheData.url)
    return cacheData.url
  } catch (error) {
    console.warn('读取图片缓存失败:', error)
    return null
  }
}

// 清理过期缓存
const cleanExpiredCache = () => {
  try {
    const now = Date.now()
    const maxAge = CACHE_EXPIRY_DAYS * 24 * 60 * 60 * 1000
    
    // 遍历所有localStorage项目
    for (let i = localStorage.length - 1; i >= 0; i--) {
      const key = localStorage.key(i)
      if (key && key.startsWith(CACHE_KEY_PREFIX)) {
        try {
          const cacheStr = localStorage.getItem(key)
          if (cacheStr) {
            const cacheData = JSON.parse(cacheStr)
            const cacheAge = now - cacheData.timestamp
            
            if (cacheAge > maxAge) {
              localStorage.removeItem(key)
              console.log('清理过期缓存:', key)
            }
          }
        } catch (error) {
          // 如果解析失败，直接删除
          localStorage.removeItem(key)
        }
      }
    }
  } catch (error) {
    console.warn('清理缓存失败:', error)
  }
}

// 等待所有图片加载完成
const waitForImages = (): Promise<void> => {
  return new Promise((resolve) => {
    const images = document.querySelectorAll('img') as NodeListOf<HTMLImageElement>
    let loadedCount = 0
    const totalImages = images.length
    
    if (totalImages === 0) {
      resolve()
      return
    }
    
    const checkComplete = () => {
      loadedCount++
      if (loadedCount === totalImages) {
        resolve()
      }
    }
    
    images.forEach((img) => {
      if (img.complete) {
        checkComplete()
      } else {
        img.onload = checkComplete
        img.onerror = checkComplete // 即使加载失败也要继续
        // 设置crossOrigin来处理跨域
        if (!img.crossOrigin) {
          img.crossOrigin = 'anonymous'
        }
      }
    })
    
    // 最多等待10秒
    setTimeout(() => {
      resolve()
    }, 10000)
  })
}

// 等待克隆文档中的图片加载
const waitForImagesInDocument = (doc: Document): Promise<void> => {
  return new Promise((resolve) => {
    const images = doc.querySelectorAll('img') as NodeListOf<HTMLImageElement>
    let loadedCount = 0
    const totalImages = images.length
    
    if (totalImages === 0) {
      resolve()
      return
    }
    
    const checkComplete = () => {
      loadedCount++
      if (loadedCount === totalImages) {
        resolve()
      }
    }
    
    images.forEach((img) => {
      if (img.complete) {
        checkComplete()
      } else {
        img.onload = checkComplete
        img.onerror = checkComplete
      }
    })
    
    // 最多等待5秒
    setTimeout(() => {
      resolve()
    }, 5000)
  })
}

// 生成二维码
const generateQRCode = async () => {
  await nextTick()
  
  if (!qrCanvas.value) return
  
  try {
    let shareUrl = ''
    
    // 先检查本地缓存是否有该报告的图片
    if (reportData.trainingId) {
      const cachedUrl = getImageFromCache(reportData.trainingId)
      if (cachedUrl) {
        shareUrl = cachedUrl
        uploadedImageUrl.value = cachedUrl
        console.log('使用缓存的图片:', cachedUrl)
      }
    }
    
    // 如果没有缓存或已经有上传的图片URL，使用现有逻辑
    if (!shareUrl) {
      if (uploadedImageUrl.value) {
        shareUrl = uploadedImageUrl.value
      } else {
        // 否则进行截图上传
        shareUrl = await captureAndUpload()
      }
    }
    
    // 使用QRCode库生成二维码到canvas
    await QRCode.toCanvas(qrCanvas.value, shareUrl, {
      width: 80,
      height: 80,
      margin: 1,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    })
    
  } catch (error) {
    console.error('生成二维码失败:', error)
    // 如果生成失败，显示默认图标
    if (qrCodeContainer.value) {
      qrCodeContainer.value.innerHTML = '<i class="el-icon-qrcode" style="font-size: 40px; color: #999;"></i>'
    }
  }
}

// 创建答题数圆环图
const createAnswerChart = () => {
  nextTick(() => {
    if (!chartContainer1.value) return
    
    const chart = echarts.init(chartContainer1.value)
    const option = {
      animation: true,
      animationDuration: 1000,
      series: [
        {
          type: 'pie',
          radius: ['65%', '85%'],
          center: ['50%', '50%'],
          startAngle: 90,
          data: [
            {
              value: reportData?.correct,
              name: '答对',
              itemStyle: {
                color: '#00D4AA',
                borderRadius: [0, 0, 0, 0]
              }
            },
            {
              value: reportData?.mistake,
              name: '答错',
              itemStyle: {
                color: '#E5E5E5',
                borderRadius: [0, 0, 0, 0]
              }
            }
          ],
          label: {
            show: false
          },
          labelLine: {
            show: false
          },
          silent: true,
          emphasis: {
            disabled: true
          }
        }
      ],
      graphic: [
        {
          type: 'text',
          left: 'center',
          top: 'center',
          style: {
            text: `${reportData?.correct*1}/${reportData?.quesCount}`,
            textAlign: 'center',
            fill: '#00D4AA',
            fontSize: 18,
            fontWeight: 'bold',
            y: -8
          }
        },
       
      ]
    }
    
    chart.setOption(option)
    
    // 响应式处理
    const resizeChart = () => {
      chart.resize()
    }
    window.addEventListener('resize', resizeChart)
    
    // 组件卸载时清理
    const cleanup = () => {
      window.removeEventListener('resize', resizeChart)
      chart.dispose()
    }
    chartCleanupFunctions.value.push(cleanup)
  })
}

// 创建得分圆环图
const createScoreChart = () => {
  nextTick(() => {
    if (!chartContainer2.value) return
    
    const chart = echarts.init(chartContainer2.value)
    
    const option = {
      animation: true,
      animationDuration: 1000,
      series: [
        {
          type: 'pie',
          radius: ['60%', '80%'],
          center: ['50%', '50%'],
          startAngle: 90,
          data: [
            {
              value: reportData?.correctRate,
              name: '得分',
              itemStyle: {
                color: '#00D4AA',
                borderRadius: [0, 0, 0, 0]
              }
            },
            {
              value: 100 - reportData?.correctRate,
              name: '未得分',
              itemStyle: {
                color: '#E5E5E5',
                borderRadius: [0, 0, 0, 0]
              }
            }
          ],
          label: {
            show: false
          },
          labelLine: {
            show: false
          },
          silent: true,
          emphasis: {
            disabled: true
          }
        }
      ],
      graphic: [
        {
          type: 'text',
          left: 'center',
          top: 'center',
          style: {
            text: `${reportData?.correctRate*1}/100`,
            textAlign: 'center',
            fill: '#00D4AA',
            fontSize: 16,
            fontWeight: 'bold',
            y: -2
          }
        }
      ]
    }
    
    chart.setOption(option)
    
    // 响应式处理
    const resizeChart = () => {
      chart.resize()
    }
    window.addEventListener('resize', resizeChart)
    
    // 组件卸载时清理
    const cleanup = () => {
      window.removeEventListener('resize', resizeChart)
      chart.dispose()
    }
    chartCleanupFunctions.value.push(cleanup)
  })
}

// 格式化时间：将毫秒转换为秒、分钟或小时
const formatTime = (milliseconds: number): { value: string, unit: string } => {
  if (!milliseconds || typeof milliseconds !== 'number') return { value: '0', unit: '秒' }
  
  // 将毫秒转换为秒
  const totalSeconds = milliseconds / 1000
  
  // 如果小于60秒，显示秒
  if (totalSeconds < 60) {
    // 如果是整数秒，不显示小数
    if (totalSeconds % 1 === 0) {
      return { value: totalSeconds.toString(), unit: '秒' }
    }
    // 否则保留一位小数
    return { value: totalSeconds.toFixed(1), unit: '秒' }
  }
  
  // 如果小于1小时（3600秒），显示分钟
  if (totalSeconds < 3600) {
    const minutes = totalSeconds / 60
    
    // 如果是整数分钟，不显示小数
    if (minutes % 1 === 0) {
      return { value: minutes.toString(), unit: '分钟' }
    }
    
    // 否则保留一位小数
    return { value: minutes.toFixed(1), unit: '分钟' }
  }
  
  // 超过1小时，显示小时
  const hours = totalSeconds / 3600
  
  // 如果是整数小时，不显示小数
  if (hours % 1 === 0) {
    return { value: hours.toString(), unit: '小时' }
  }
  
  // 否则保留一位小数
  return { value: hours.toFixed(1), unit: '小时' }
}

// 格式化分数：保留一位小数，如果小数点后第一位是0则不显示小数
const formatScore = (score: number): string => {
  if (typeof score !== 'number') return '0'
  
  // 保留一位小数
  const fixed = Number(score.toFixed(1))
  
  // 如果小数点后第一位是0，显示整数
  if (fixed % 1 === 0) {
    return fixed.toString()
  }
  
  // 否则显示一位小数
  return fixed.toFixed(1)
}

// 格式化百分比：确保不超过100%，保留整数
const formatPercentage = (value: number): number => {
  value = value * 1
  if (typeof value !== 'number' || isNaN(value)) return 0
  // 确保不超过100%，不小于0%
  return Math.min(Math.max(Math.round(value), 0), 100)
}

// 获取知识点颜色
const getKnowledgeColor = (accuracy: number): string => {
  if (accuracy >= 90) return '#00D4AA'
  if (accuracy >= 80) return '#FFA726' 
  if (accuracy >= 70) return '#FF7043'
  return '#F44336'
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

// 继续学习
const continueStudy = () => {
  router.push({
    path: '/note/note_list',
    // query: {
    //   data: dataEncrypt({
    //     reportId: queryData.reportId,
    //     pageSource: '1'
    //   }),
    // }
  })
}

const getData = async () => {
    getDetailssApi({trainingId: queryData.reportId}).then((res: any) => {
        if (res.code == 200) {
            // 将API返回的数据赋值给reportData
            Object.assign(reportData, res.data)
            
            // 等待数据渲染完成后再创建图表和截图
            nextTick(() => {
  // 创建ECharts图表
  createAnswerChart()
  createScoreChart()
                
                // 等待图表渲染完成后再生成二维码
                setTimeout(() => {
                    console.log('开始自动生成二维码...')
                    generateQRCode()
                }, 2000) // 增加等待时间确保图表完全渲染
            })
            
        }
        loading.value = false
    }).catch((error) => {
        console.error('数据获取失败:', error)
        loading.value = false
    })
}
//答题记录
const answerRecord = () => { 
  console.log(queryData.reportId,'queryData.reportId')
  router.push({
    path: '/ai_percision/entrance_assessment/answer_record',
    query: {
      data: dataEncrypt({
        reportId: queryData.reportId,
        pageSource: '1'
      }),
    }
    })
}

// 测试截图功能
const testCapture = async () => {
  try {
    console.log('开始测试截图...')
    const url = await captureAndUpload()
    
    // 重新生成二维码
    await generateQRCode()
    
    ElMessage.success('截图测试完成！请查看二维码')
    console.log('截图URL:', url)
  } catch (error) {
    console.error('截图测试失败:', error)
    ElMessage.error('截图测试失败: ' + (error as Error).message)
  }
}

// 初始化数据
onMounted(() => {
  // 清理过期缓存
  cleanExpiredCache()
  
  getData()
  // 模拟获取测试报告数据
  reportData.testId = route.query.testId as string || 'demo'
  reportData.bookId = route.query.bookId as string || 'demo'

    // 注册自定义返回方法
    window.customGoBack = customGoBack
})

function secondsToHMS(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    // 补零操作，确保两位数显示
    const pad = (num) => num.toString().padStart(2, '0');
    timeState.seconds = pad(secs)
    timeState.minutes = pad(minutes)
    timeState.hours = pad(hours)
}

// 组件卸载时清理资源
onUnmounted(() => {
  chartCleanupFunctions.value.forEach(cleanup => cleanup())
  // 清除自定义返回方法
    if (window.customGoBack) {
      delete window.customGoBack
    }
})


  // 自定义返回方法
  const customGoBack = () => {
      router.go(-1)
  }


</script>

<style lang="scss" scoped>
.test-report-main {
  position: absolute;
  top: 120px;
  left: 0;
  width: 100vw;
  height: auto;
  // background: linear-gradient(to right,  rgba(174, 208, 255,1), rgba(216, 234, 251, 1));
  background-image: url("@/assets/img/entranceAssessment/bigbj.png");
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  background-repeat: no-repeat;
  overflow: hidden;
  
  // 为截图优化的样式
  &.capture-mode {
    position: static;
    height: auto;
    min-height: 100vh;
    overflow: visible;
    background-attachment: scroll;
  }
}

.test-report-container {
  max-width: 1324px;
  margin: 0 auto;
  padding: 0;
  height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  overflow-y: auto;
  overflow-x: hidden;
  
  // 为截图优化的样式
  &.capture-mode {
    height: auto;
    min-height: auto;
    overflow: visible;
    padding-bottom: 50px; // 底部留白
  }
  
  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  
  &::-webkit-scrollbar-button {
    display: none;
  }
  
  &::-webkit-scrollbar-corner {
    background: transparent;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c3c6cb;
    border-radius: 4px;
    transition: background 0.3s ease;
    
    &:hover {
      background: #a8acb3;
    }
  }
  
  &::-webkit-scrollbar-thumb:active {
    background: #9ca0a7;
  }
  
  // Firefox 滚动条样式
  scrollbar-width: thin;
  scrollbar-color: #c3c6cb transparent;
}

.top-banner {
  padding: 16px 20px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  
  .banner-icon {
    font-size: 18px;
  }
  
  .banner-text {
    font-size: 14px;
    font-weight: 500;
  }
}

.main-card {
  margin: 16px 0;
  overflow: hidden;
  
  .card-content {
    padding: 24px 0;
    .score-user-section {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 24px;
      gap: 20px;
      
      .score-circle {
        width: 206px;
        height: 206px;
        // background: url('@/assets/img/entranceAssessment/circle-bg.png')center center no-repeat;
        
        background-size: cover;
        display: flex;
        align-items: center;
        justify-content: center;
        .circle-progress {
          width: 206px;
          height: 206px;
          background-image: url('@/assets/img/entranceAssessment/boerd.png');
          background-position: center;
          background-size: cover;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
          
          
          .circle-inner {
            // background: url('@/assets/img/entranceAssessment/boerd.png')center center no-repeat;
            width: 100px;
            height: 100px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 2;
            
            .score-number {
              font-size: 50px;
              font-weight: 700;
              color: rgb(90, 133, 236);
              line-height: 1;
            }
            
            .score-unit {
              font-size: 16px;
              color: rgb(90, 133, 236);
              margin: 4px 0 0 6px;
              font-weight: 500;
            }
          }
        }
      }
      
      .user-info-wrapper {
        display: flex;
        flex-direction: column;
        gap: 12px;
        flex: 1;
        
        .test-date-tag {
          font-size: 12px;
          color: #fff;
          padding: 6px 12px;
          background: #00000033;
          border-radius: 16px;
          align-self: flex-start;
          // box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .user-main-info {
          display: flex;
          align-items: center;
          gap: 12px;
          
          .user-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            overflow: hidden;
            background: linear-gradient(45deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            
            .avatar-img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
          
          .user-details {
            .user-name-row {
              display: flex;
              align-items: center;
              gap: 16px;
              
              .user-name {
                font-size: 18px;
                font-weight: 600;
                color: #333;
              }
              
              .grade-info {
                font-size: 13px;
                color: #5A85EC;
                background: #E5F2FD;
                padding: 4px 8px;
                border-radius: 12px;
                font-weight: 500;
              }
            }
          }
        }
        
        .test-content-desc {
          font-size: 13px;
          color: #666;
          line-height: 1.4;
          margin-top: 4px;
        }
      }
      
      .qr-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        
        .qr-card {
          width: 110px;
          height: 130px;
          background: white;
          border-radius: 16px;
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: space-between;
          padding: 16px 12px 12px 12px;
          border: 1px solid rgba(0, 0, 0, 0.05);
          
          .qr-code-container {
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex: 1;
            
            canvas {
              width: 80px;
              height: 80px;
              border-radius: 2px;
            }
            
            .qr-loading {
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              width: 100%;
              height: 100%;
              
              .loading-spinner {
                width: 20px;
                height: 20px;
                border: 2px solid #f3f3f3;
                border-top: 2px solid #5A85EC;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin-bottom: 8px;
              }
              
              .loading-text {
                font-size: 10px;
                color: #666;
                text-align: center;
              }
            }
          }
          
          .qr-text {
            font-size: 11px;
            color: #888;
            font-weight: 400;
            text-align: center;
            line-height: 1;
            margin-top: 4px;
          }
        }
      }
    }
  }
}

.grade-details {
      background-color: #fff; /* 设置纯色背景 */
      // background-image: url('@/assets/img/entranceAssessment/grade-bg.png'); /* 设置背景图片 */
      background-size: cover; /* 根据需要调整图片大小 */
      background-position: top; /* 居中图片 */
      border-radius: 10px;
      padding: 20px 90px;
      box-shadow: 0 0 0 3px rgba(202, 222, 255, 0.66);
      .grade-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .left-content {
          flex: 1;
          text-align: left;
          
          .grade-description {
            
            font-size: 20px;
            color: rgb(42, 43, 42);
            margin-bottom: 24px;
            margin-top: 20px;
            line-height: 1.5;
            font-weight: 500;
          }
          
          .current-grade {
            display: flex;
            align-items: center;
            gap: 20px;
            
            .grade-label {
              font-size: 20px;
              color: rgb(42, 43, 42);
              margin-bottom: 0;
              font-weight: 500;
            }
            
            .grade-display {
              display: flex;
              align-items: center;
              gap: 12px;
              
              .grade-emoji {
                font-size: 32px;
              }
              
              .grade-letter {
                font-size: 32px;
                font-weight: bold;
                color: #FF9800;
              }
            }
          }
        }
        
        .grade-pyramid {
          .pyramid-container {
            position: relative;
            height: 240px;
            margin: 0 auto;
            
            // 左右指示器通用样式
            .left-indicators, .right-indicators {
              position: absolute;
              top: 0;
              display: flex;
              flex-direction: column;
              gap: 20px;
              z-index: 10;
              
              .level-indicator {
                display: flex;
                align-items: center;
                gap: 12px;
                
                .indicator-dot {
                  width: 12px;
                  height: 12px;
                  border-radius: 50%;
                  flex-shrink: 0;
                }
                
                .level-name {
                  font-size: 14px;
                  color: #333;
                  font-weight: 500;
                  white-space: nowrap;
                }
                
                &.current {
                  .level-name {
                    font-weight: 700;
                    color: #333;
                  }
                  .indicator-dot {
                    box-shadow: 0 0 0 3px rgba(77, 182, 172, 0.3);
                  }
                }
              }
            }
            
            // 左侧指示器
            .left-indicators {
              left: -120px;
              top: 90px;
              
              .level-a-indicator .indicator-dot {
                background: #F44336; // 红色
              }
              
              .level-b-indicator .indicator-dot {
                background: #9C27B0; // 紫色
              }
            }
            
            // 右侧指示器
            .right-indicators {
              right: -120px;
              top: 20px;
              
              .level-a-plus-indicator .indicator-dot {
                background: #FF9800; // 橙色
              }
              
              .level-b-plus-indicator .indicator-dot {
                background: #4DB6AC; // 青色
              }
              
              .level-c-indicator .indicator-dot {
                background: #42A5F5; // 蓝色
              }
            }
            
            // 金字塔主体
            .pyramid-main {
              position: relative;
              width: 100%;
              height: 100%;
              
              .pyramid-level {
                position: absolute;
                left: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1;
                
                .pyramid-shape {
                  width: 100%;
                  height: 100%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  border-radius: 6px;
                  
                  .level-text {
                    color: white;
                    font-weight: bold;
                    font-size: 16px;
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
                  }
                }
                
                // A+级 - 三角形顶部
                &.level-a-plus.triangle {
                  top: 0;
                  width: 45px;
                  height: 35px;
                  margin-left: -22.5px;
                  
                  .pyramid-shape {
                    background: #A3E4D7; // 更鲜艳的薄荷绿色
                    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
                    border-radius: 0;
                    box-shadow: 0 2px 8px rgba(163, 228, 215, 0.4);
                    
                    .level-text {
                      color: #fff;
                      font-size: 13px;
                      font-weight: 700;
                      margin-top: 12px;
                      text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
                      letter-spacing: 0.5px;
                    }
                  }
                }
                
                // A级 - 第二层梯形
                &.level-a {
                  top: 40px;
                  width: 87px;
                  height: 30px;
                  margin-left: -43.5px;
                  
                  .pyramid-shape {
                    background: #88D8F5; // 天蓝色
                    clip-path: polygon(15% 0%, 85% 0%, 100% 100%, 0% 100%);
                  }
                }
                
                // B+级 - 第三层梯形（当前等级）
                &.level-b-plus {
                  top: 85px;
                  width: 135px;
                  height: 30px;
                  margin-left: -67.5px;
                  
                  .pyramid-shape {
                    background: #FF9800; // 橙色
                    clip-path: polygon(12% 0%, 88% 0%, 100% 100%, 0% 100%);
                  }
                  
                  &.current-level {
                    z-index: 2;
                    
                    .pyramid-shape {
                      box-shadow: 0 0 0 3px rgba(255, 152, 0, 0.3);
                      
                      .level-text {
                        font-size: 16px;
                        font-weight: 800;
                      }
                    }
                  }
                }
                
                // B级 - 第四层梯形
                &.level-b {
                  top: 135px;
                  width: 182px;
                  height: 30px;
                  margin-left: -91px;
                  
                  .pyramid-shape {
                    background: #FFF176; // 淡黄色
                    clip-path: polygon(10% 0%, 90% 0%, 100% 100%, 0% 100%);
                    
                    .level-text {
                      color: #333; // 黄色背景用深色文字
                      text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
                    }
                  }
                }
                
                // C级 - 底层梯形
                &.level-c {
                  top: 185px;
                  width: 230px;
                  height: 30px;
                  margin-left: -115px;
                  
                  .pyramid-shape {
                    background: #F8BBD0; // 粉色
                    clip-path: polygon(8% 0%, 92% 0%, 100% 100%, 0% 100%);
                    
                    .level-text {
                      color: #333; // 粉色背景用深色文字
                      text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

.stats-section, .knowledge-section, .weakness-section, .suggestion-section {
  background: transparent;
  border-radius: 0;
  margin: 32px 16px 16px 16px;
  box-shadow: none;
  overflow: visible;
}

.section-title-bar {
  position: relative;
  color: white;
  margin: 10px  auto;
  width: 386px;
  height: 47px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: url('@/assets/img/entranceAssessment/testtitle.png')center center no-repeat;
  background-size: 100%;

  .title-number {
    font-weight: bold;
    font-size: 18px;
    color: white;
  }
  
  .title-text {
    font-size: 18px;
    font-weight: 600;
    color: white;
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;

  .score-box{
    display: flex;
    justify-content: space-between;
  }
  .stat-card {
    background: white;
    // background-image: url('@/assets/img/entranceAssessment/listbj.png'); /* 设置背景图片 */
    // background-size: cover; /* 根据需要调整图片大小 */
    background-size: 100%; 
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #f0f0f0;
    
    .card-header {
      .card-title {
        font-size: 24px;
        font-weight: 600;
        color: #2A2B2A;
        margin: 0 0 8px 0;
      }
      .left-num{
        padding-top: 30px;
        font-size: 24px;
        font-weight: 700;
        color: #000;
      }
      
      .correct-info, .score-info {
        font-size: 12px;
        color: #666;
      }
      
      .warning-text {
        font-size: 14px;
        color: #E16565;
        line-height: 28px;
        margin-top: 10px;
      }

      .main-number {
        .number {
          font-size: 28px;
          font-weight: bold;
          color: #4A90E2;
          
          &.score-number {
            color: #4A90E2;
          }
        }
        
        .unit {
          font-size: 14px;
          color: #666;
          margin-left: 4px;
        }
      }
    }
    
    .card-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .chart-container {
        width: 138px;
        height:138px;
        
        .ring-chart {
          width: 100%;
          height: 100%;
        }
      }
      
      &.detailed {
        flex-direction: column;
        align-items: flex-start;
        
        .accuracy-section {
          width: 100%;
          margin-bottom: 16px;

          
          .accuracy-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
          }
          
          .accuracy-number {
            font-size: 60px;
            font-weight: bold;
            color: #4A90E2;
            
            .percent {
              font-size: 20px;
            }
          }
        }
        
        .breakdown-section {
          width: 100%;
          display: flex;
          justify-content: space-between;
          
          .breakdown-item {
            text-align: center;
            
            .item-label {
              display: block;
              font-size: 10px;
              color: #666;
              margin-left: 6px;
            }
            
            .item-value {
              display: block;
              font-size: 16px;
              font-weight: bold;
              
              &.total {
                color: rgb(0, 127, 233);
              }
              
              &.correct {
                color: rgb(35, 203, 137);
              }
              
              &.wrong {
                color: rgb(218, 83, 83);
              }
            }
          }
        }
        
        .time-section {
          width: 100%;
          
          .time-item {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            .time-icon {
              font-size: 20px;
            }
            
            .time-info {
              .time-label {
                font-size: 20px;
                color: #323A57;
                margin-bottom: 4px;
                margin-left: 6px;
              }
              
              .time-value {
                font-size: 24px;
                font-weight: bold;
                
                &.overtime {
                  color: #ff6b6b;
                }
                
                &.normal {
                  color: #ff6b6b;
                }
                
                .time-unit {
                  font-size: 14px;
                }
              }
            }
          }
        }
      }
    }
  }
}

.knowledge-list {
  padding: 24px 200px;
  background-color: #fff; /* 设置纯色背景 */
  // background-image: url('@/assets/img/entranceAssessment/grade-bg.png'); /* 设置背景图片 */
  background-size: cover; /* 根据需要调整图片大小 */
  background-position: top; /* 居中图片 */
  border-radius: 10px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  
  .knowledge-row {
    display: flex;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #f5f5f5;
    
    &:last-child {
      border-bottom: none;
    }
    
    .knowledge-title {
      flex: 1;
      font-size: 14px;
      color: #2A2B2A;
      min-width: 200px;
      font-weight: 500;
    }
    
    .knowledge-bar {
      flex: 2;
      height: 10px;
      background: #f0f0f0;
      border-radius: 5px;
      margin: 0 20px;
      overflow: hidden;
      
      .bar-fill {
        height: 100%;
        border-radius: 5px;
        transition: width 0.3s ease;
      }
    }
    
    .knowledge-percentage {
      font-size: 13px;
      color: #666;
      min-width: 100px;
      text-align: right;
      font-weight: 500;
    }
  }
}

.weakness-table {
  padding: 50px 32px 0 32px;
  background-color: #fff; /* 设置纯色背景 */
  // background-image: url('@/assets/img/entranceAssessment/grade-bg.png'); /* 设置背景图片 */
  background-size: cover; /* 根据需要调整图片大小 */
  background-position: top; /* 居中图片 */
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  
  .status-tag {
    width:74px;
    height: 23px;
    line-height: 23px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    color: #fff;
    
    &.not-mastered {
      background: linear-gradient(to right,  #F07F4CFF,#C95656FF);
    }
    
    &.average {
         background: linear-gradient(to right, #F6D22BFF,#F29500FF);
    }
    
    &.mastered {
       background: linear-gradient(to right,  #08D8B8FF,#00B392FF);
    }
  }
  
  :deep(.el-table) {
    .el-table__header-wrapper {
      .el-table__header {
        th {
          background: #f8f9fa !important;
          color: #333 !important;
          font-weight: 600;
          border-bottom: 1px solid #e9ecef;
        }
      }
    }
    
    .el-table__body-wrapper {
      .el-table__body {
        tr {
          &:hover {
            background: #f8f9fa;
          }
          
          td {
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
          }
        }
      }
    }
  }
}

.suggestion-section {
  background-color: #fff; /* 设置纯色背景 */
  // background-image: url('@/assets/img/entranceAssessment/grade-bg.png'); /* 设置背景图片 */
  background-size: cover; /* 根据需要调整图片大小 */
  background-position: top; /* 居中图片 */
  border-radius: 10px;
  padding: 24px;
  // height: 500px;
  padding-bottom: 180px;
  .suggestion-content {
    
    .suggestion-top{
      padding: 20px;
      background: linear-gradient(135deg, #E8F5E8 0%, #F1F8E9 100%);
      margin: 0 0 20px 0;
    }
    .suggestion-header {
      display: flex;
      align-items: center;
      .suggestion-icon {
        font-size: 24px;
      }
      
      .suggestion-title {
        font-size: 18px;
        font-weight: 600;
        color:#00B7D0FF;
      }
    }
    
    .suggestion-description {
      font-size: 14px;
      color: rgb(42, 43, 42);
      padding: 10px 0 0 50px;
    }
    
    .suggestion-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      margin-bottom: 32px;
      
      .suggestion-chip {
        background: #F5F5F5;
        color: #009C7F;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 13px;
        font-weight: 500;

        &.weak-point {
          background: #FFE6E6;
        }
        
        &.no-weak-points {
          background: #E8F5E8;
          color: #00B392;
          border: 1px solid #B3E5D1;
        }
      }
    }
    
    .suggestion-buttons {
      display: flex;
      justify-content: center;
      gap: 20px;
      
      .el-button {
        padding: 12px 32px;
        border-radius: 25px;
        font-size: 15px;
        font-weight: 500;
        min-width: 120px;
        
        &.btn-record {
          background: #F5F5F5;
          color: #666666;
          border: none;
        }
        
        &.btn-capture {
          background: #FF9800;
          border: none;
          color: white;
        }
        
        &.btn-correct {
          background: #00C9A3;
          border: none;
          color: white;
        
        }
      }
    }
  }
}



// 特殊的答题数卡片样式
.stat-answer {
  background-image: url('@/assets/img/entranceAssessment/listbj.png'); /* 设置背景图片 */
  background-size: cover; /* 根据需要调整图片大小 */
  background-size: 100%; 
  padding: 0;
  display: flex;
  justify-content: space-between;
  
  .answer-card-header {
    padding: 16px 20px 0 20px;
    
    .answer-card-title {
      font-size: 24px;
      font-weight: 600;
      color: #2A2B2A;
      margin: 0 0 4px 0;
    }
    
    .answer-correct-info {
      font-size: 12px;
      color: #666;
    }
        .answer-left-section {
          display: flex;
      
          .answer-info {
            position: relative;
            margin-top: 30px;
            display: flex;
            align-items: flex-end;
            img{
              // margin-top: 20px;
            }      
          .answer-icon {
            width: 40px;
            height: 40px;
            background: rgba(74, 144, 226, 0.1);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 5px;
            
            svg {
              width: 20px;
              height: 20px;
            }
          }
        .answer-label {
          font-size: 20px;
          color: #666;
          margin-left: 6px;
          
        }
        
        .number {
            font-size: 60px;
            font-weight: bold;
            color: #5A85EC;
            margin-left: 88px;
            align-self: flex-end;
            line-height: 1;
          }
          
          .unit {
            font-size: 20px;
            color: #5A85EC;
            font-weight: 500;
            margin-left: 5px;
            align-self: flex-end;
            line-height: 1;
          }
      }
    }
    
  }
  
  .answer-card-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding:20px;
    

    .answer-chart-container {
      width: 136px;
      height: 136px;
      
      .answer-ring-chart {
        width: 100%;
        height: 100%;
      }
    }
  }
}

// answers-box 特殊样式
.answers-box {
  background-image: url('@/assets/img/entranceAssessment/listbj.png') no-repeat; /* 设置背景图片 */
  // background-size: cover; /* 根据需要调整图片大小 */
  // background-size: 100%; 
  display: flex;
  // justify-content: space-between;
  // background: #fff;
  .card-header{
    padding: 10px 0 0 30px;
  }
  .accuracy-section {
      display: flex;
      align-items: center;
      padding:30px 0 0 0px;
      .accuracy-icon {
        font-size: 24px;
      }
      
      .accuracy-text {
        font-size: 20px;
        color: rgb(50, 58, 87);
        font-weight: 500;
        margin-left: 6px;
      }
      
      .accuracy-number {
        font-size: 60px;
        font-weight: bold;
        color: #5A85EC;
        margin-left: 40px;
        
        .percent {
          font-size: 32px;
          color: #5A85EC;
          margin-left: 2px;
        }
      }
    }
    .ishei{
      height: 220px;
      margin-left: auto;
    }
  .card-content.answers-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0px 50px;
    
    .circles-section {
      display: flex;
      flex-direction: column;
      gap: 10px;
      align-items: flex-end;
      
      .circle-item {
        display: flex;
        align-items: center;
        gap: 10px;
        
        .circle-label {
          font-size: 12px;
          color: rgb(42, 43, 42);
          min-width: 60px;
          text-align: right;
        }
        
        .circle-value {
          width:38px;
          height: 38px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 16px;
          font-weight: bold;
          color: white;
          background: #fff;
          margin-left: 10px;
          &.total {
            color: #9E9E9E;
            border: 1px solid #9E9E9E;
          }
          
          &.correct {
            color: #00D4AA;
            border: 1px solid #00D4AA;
          }
          
          &.wrong {
            color: #FF6B6B;
            border: 1px solid #FF6B6B;
          }
        }
      }
    }
  }
}

// score-time-box 特殊样式
.score-time-box {
  position: relative;
      .warning-text {
      position: relative;
      margin: 0 auto;
      width: 414px;
      height: 28px;
      line-height: 28px;
      font-size: 13px;
      color: #ff6b6b;
      background: rgba(255, 107, 107, 0.15);
      border-radius: 12px;
      text-align: center;
      font-weight: 500;
      margin-top: 10px;
    }
  
  .card-header {
    padding: 6px 20px 6px 20px;
    margin-bottom: 0;
    
    .card-title {
      font-size: 24px;
      font-weight: 600;
      color: #2A2B2A;
      margin: 0 0 16px 0;
    }
    

  }
  
  .card-content.time-content {
    position: relative;
    // padding: 24px;
    
    .time-columns {
      width: 100%;
      display: flex;
      justify-content: center;
      
      .time-flex{
        display: flex;
        align-items: center;
        margin: 0 0 20px 0;
      }
      .time-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        
        .time-icon {
          font-size: 28px;
          opacity: 0.8;
        }
        
        .time-label {
          font-size: 20px;
          color: #323A57;
          font-weight: 500;
          margin-left: 6px;
        }
        
        .time-value {
          font-size: 50px;
          font-weight: bold;
          line-height: 1;
          
          &.overtime {
            color: #ff6b6b;
          }
          
          &.normal {
            color: #ff6b6b;
          }
          
          .time-unit {
            font-size: 20px;
            margin-left: 6px;
            font-weight: 500;
          }
        }
      }
    }
    
    .chart-overlay {
      position: absolute;
      bottom: 16px;
      right: 20px;
      width: 80px;
      height: 80px;
      
      .score-ring-chart {
        width: 100%;
        height: 100%;
      }
    }
  }
}
.back {
  background-image: url('@/assets/img/entranceAssessment/listbj.png') !important; /* 设置背景图片 */
  background-size: cover!important; /* 根据需要调整图片大小 */
  background-size: 100%!important; 
}

/* 旋转动画 */
@keyframes spin {
  0% { 
    transform: rotate(0deg); 
  }
  100% { 
    transform: rotate(360deg); 
  }
}
</style> 