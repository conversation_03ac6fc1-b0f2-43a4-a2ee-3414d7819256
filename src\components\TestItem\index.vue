<template>
  <div
    class="test-item-box"
    @mouseenter="mouseenter(item)"
    @mouseleave="mouseleave(item)"
    :style="styleData"
    v-for="(item, index) in props.testList"
    :key="item.quesId"
  >
    <div class="test-tittle" @click="goWrongPage(item)">
      <div v-html="resetSty(item, index + 1 + props.lastLength)" />
    </div>
    <div class="test-body" v-html="resetOptions(item)" />
    <div v-show="item.showAnswer && !item.showAnalyse && item.cate != 1" class="test-answer">
      <span>【答案】</span>&nbsp;&nbsp;
      <div v-html="item.displayAnswer" />
    </div>
    <div v-show="item.showAnalyse" class="analyse">
      <div class="flex-sty">
        <span>【知识点】</span>&nbsp;&nbsp;
        <div v-html="item.analyseData ? item.analyseData.pointVos[0].name : ''" />
      </div>
      <div class="flex-sty">
        <span>【答案】</span>&nbsp;&nbsp;
        <div v-html="item.analyseData?.displayAnswer" />
      </div>
      <div class="flex-sty">
        <span>【分析】</span>&nbsp;&nbsp;
        <div v-html="item.analyseData?.analyse" />
      </div>
      <div class="flex-sty">
        <span>【解答】</span>&nbsp;&nbsp;
        <div v-html="item.analyseData?.method" />
      </div>
      <div class="flex-sty">
        <span>【点评】</span>&nbsp;&nbsp;
        <div v-html="item.analyseData?.discuss" />
      </div>
    </div>
    <div
      v-if="[1, 2, 5, 6, 7, 8].includes(props.source)"
      class="test-handle"
    >
      <div class="test-data">
        <div class="test-data1" v-if="props.source !== 5 && props.source !== 7">更新： {{ item.date }}</div>
        <div class="test-data1" v-if="props.source == 7">布置时间： {{ item.updateTime.slice(0, 10) }}</div>
        <div class="test-data1" v-if="props.source == 7">正确率： {{ item.correctRate }}%</div>
        <div class="test-data1" v-if="props.source == 7">错误次数： {{ item.errorCount }}</div>
      </div>
      <div class="handle-box">
        <div @click="handleAnalyse(item)">
          <img src="@/assets/img/mine/analyze.png" class="png-analyze" />
          <span>{{ item.showAnalyse ? "隐藏解析" : "查看解析" }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { onMounted, reactive, ref, watch } from "vue"
// import { addBasketApi, delBasketApi, analysisApi, errorPostApi } from "@/api/ques"
import router from "@/router"
import { ElMessage, ElMessageBox } from "element-plus"
defineOptions({
  name: "TestItem"
})
const props = defineProps({
  //试题数据
  testList: {
    type: Array as any,
    default: function () {
      return []
    }
  },
  //页面来源：1.章节组卷，2.相似题，3.试题篮，4.组卷中心，5.试卷详情，6.换一题，7.错题管理
  source: {
    type: Number,
    default: 1
  },
  // 搜索关键字，用于高亮
  searchValue: {
    type: String,
    default: ""
  },
  styleData: {
    type: String,
    default: ""
  },
  //是否显示添加、删除试题篮
  noAddBtn: {
    type: Boolean,
    default: false
  },
  lastLength: {
    type: Number,
    default: 0
  }
})
watch(
  () => [props.source, props.searchValue],
  (newData) => {
    console.log("props.source", newData[0])
    console.log("props.searchValue", newData[1])
  },
  { immediate: true, deep: true }
)

const userInfo = JSON.parse(localStorage.getItem("userinfo") || "{}")

const emit = defineEmits(["del", "move", "changeState", "getList", "changeList", "addCard"])

const geturl = (item: any) => {
  return new URL(`../../assets/img/mine/${item.isCollect ? "collection" : "uncollection"}.png`, import.meta.url).href //静态资源引入为url，相当于require()
}

//跳转错题详情页面
const goWrongPage = (item: any) => {
  if (props.source == 7 && !props.noAddBtn) {
    router.push({ path: "/online_work/wrong_detail", query: { id: item.quesId } })
  }
}

//过滤修改题干标签内容
const resetSty = function (testItem: any, sort?: number) {
  const tittle =
    (sort ? sort + "." : "") +
    "<span class='green-tittle'>" +
    testItem.label +
    " &nbsp;</span>" +
    (testItem.content ? filterContent(testItem.content) : filterContent(testItem.ques?.content))
  return tittle
}
//过滤掉填空题等div标签的contenteditable属性
const filterContent = (contect: string) => {
  //高亮关键字
  let highlightedResult = ""
  highlightedResult = contect.replaceAll('contenteditable="true"', " ")
  return highlightedResult
}
//过滤修改选项内容
const resetOptions = function (testItem: any, isAnser?: number) {
  let optionHtml = ""
  if (isAnser) {
    testItem.map((item: any, index: number) => {
      optionHtml += `<div">${index != 0 ? "," : ""} ${item}</div>`
    })
  } else {
    if (!testItem.options) return
    testItem.options.map((item: any, index: number) => {
      const anserSign = testItem.showAnswer || props.source == 3
      optionHtml += `<div class="${
        anserSign && testItem.answers.includes(index.toString()) ? "answer-sty answer-item" : "answer-item"
      }"> ${String.fromCharCode(65 + index)}. <div style="display:inline-table;max-width:39.8125rem">${item}</div></div>`
    })
  }
  return optionHtml
}
const mouseenter = function (data: any) {
  if ([4, 5].includes(props.source)) {
    const param = {
      item: data,
      key: "showTexthandle"
    }
    emit("changeState", param)
  }
}
const mouseleave = function (data: any) {
  if ([4, 5].includes(props.source)) {
    const param = {
      item: data,
      key: "showTexthandle"
    }
    emit("changeState", param)
  }
}
//显示隐藏解析
const handleAnalyse = async function (item: any) {
  // const res: any = await analysisApi({
  //   quesId: item.quesId
  // })
  // item.analyseData = res.data
  // const param = {
  //   item: item,
  //   key: "showAnalyse"
  // }
  // emit("changeState", param)
}
</script>
<style lang="scss" scoped>
@import "@/assets/css/test.scss";
.test-item-box {
  background-color: #fff;
  width: 55.625rem;
  margin-bottom: .625rem;
  border: .0625rem solid #fff;
  border-radius: .625rem;
  &:hover {
    border: .0625rem solid var(--percision-tecah-main-color);
    border-radius: .625rem;
    .test-handle {
      border-bottom-left-radius: .625rem;
      border-bottom-right-radius: .625rem;
    }
  }
  .test-tittle {
    display: flex;
    font-size: .875rem;
    line-height: 1.4375rem;
    letter-spacing: .125rem;
    padding: 1.25rem;
    :deep(td) {
      min-width: 3.0625rem;
    }
  }
  .test-answer {
    padding-left: 2rem;
    display: flex;
    font-size: 1rem;
    align-items: center;
  }
  .test-body {
    padding: 0 1.875rem;
    display: flex;
    align-items: center;
    flex-flow: row wrap;
    :deep(.answer-item) {
      margin-right: 5rem;
      margin-bottom: .625rem;
      font-size: 1rem;
      &:hover {
        cursor: pointer;
        color: var(--percision-tecah-select-font-color);
      }
    }
  }
  .analyse {
    padding: 0 1.875rem;
    margin-top: .625rem;
    letter-spacing: .125rem;
    div {
      margin-bottom: .625rem;
    }
  }
  .test-handle {
    width: 100%;
    height: 3rem;
    border-top: .0625rem solid #f2f2f2;
    margin-top: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 1rem;
    padding-right: 1rem;
    .test-data1 {
      margin-right: 1.25rem;
    }
    div {
      display: flex;
      font-size: .75rem;
      align-items: center;
      color: var(--percision-tecah-font-color);
    }
    .handle-box {
      div,
      button {
        border: none;
        margin-left: 1.25rem;
        cursor: pointer;
        display: flex;
        align-items: flex-end;
      }
    }
  }
}
:deep(.green-tittle) {
  color: var(--percision-tecah-select-font-color);
  // cursor: pointer;
}
:deep(.quizPutTag) {
  width: 4.75rem;
  display: inline-block;
  border-bottom: .0625rem solid var(--percision-tecah-select-font-color);
}
:deep(.edittable) {
  border-spacing: 0;
  margin-top: .375rem;
  th,
  td {
    line-height: 1.875rem;
    padding: .3125rem;
    white-space: normal;
    word-break: break-all;
    border: .0625rem solid #000;
    border-right: none;
    vertical-align: middle;
    text-align: center;
  }
  td:last-child {
    border-right: .0625rem solid #000;
  }
}
.flex-sty {
  display: flex;
  font-size: .875rem;
  div {
    max-width: 47.5rem;
    line-height: 1.0625rem;
  }
  span {
    text-wrap: nowrap;
    font-weight: 700;
    letter-spacing: normal;
  }
}
.png-analyze,
.png-similar {
  width: 1rem;
  height: 1rem;
  margin: 0 .25rem 0 0;
}
.png-similar {
  height: 1.0625rem;
}
.el-input__wrapper,
.el-input__wrapper:hover {
  box-shadow: none !important;
}
.icon-text {
  display: flex;
  font-size: 1rem;
  color: var(--percision-tecah-font-color);
  img {
    width: 1.125rem;
    height: 1.125rem;
    margin-right: .625rem;
    &:first-child {
      display: inline-block;
    }
    &:last-child {
      display: none;
    }
  }
  &:hover {
    color: var(--percision-tecah-select-font-color);
    img {
      &:first-child {
        display: none;
      }
      &:last-child {
        display: inline-block;
      }
    }
  }
}
.dialog-content {
  display: flex;
  justify-content: center;
  :deep(.el-form-item__label) {
    color: #4d4d4d;
    font-size: .875rem;
  }
  :deep(.el-checkbox) {
    margin-right: .9375rem;
    .el-checkbox__label {
      padding-left: .25rem;
      font-size: .875rem;
    }
  }
}
</style>
<style lang="scss">
.answer-sty {
  border: .0625rem solid var(--percision-tecah-main-color);
  padding: .3125rem .75rem;
  border-radius: .375rem;
  background: #e3fff7;
}
answer-item img {
  mix-blend-mode: multiply;
}
</style>
