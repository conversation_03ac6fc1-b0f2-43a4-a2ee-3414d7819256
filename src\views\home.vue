<template>
  <div>
    <!-- <h1>Welcome to the Home Page</h1>
    <el-button @click="logout">Logout</el-button> -->
  </div>
</template>

<script lang="ts" setup>
  // import { useAuthStore } from '../store'
  import { useRouter } from 'vue-router'
  import { useUserStore } from "@/store/modules/user"

  // const authStore = useAuthStore()
  const router = useRouter()

  const logout = () => {
    // authStore.clearToken()
    router.push({ name: 'Login' })
  }
  const checkToken = () => {
    const token = useUserStore().token, learnUsers = useUserStore().learnUsers
    if (token) {
      if (learnUsers.length) {
        router.push({ name: 'KnowledgeGraph' })
      } else {
        router.push({ name: "UserAdd", query: { pageType: 'add' } })
      }
    } else {
      router.push({ name: 'Login' })
    }
  }
  checkToken()
</script>