import { reactive } from "vue";

//能力雷达图
export const options = reactive({
  radar: {
    axisLine: {
      show: true,
      lineStyle: {
        color: "#EEF3FD",      // 坐标轴颜色（最外层边框）
        width: 2            // 最外层边框粗细
      }
    },
    axisName: {
      color: '#2A2B2A', // 直接指定颜色（红色示例）
      fontSize: 14,
    },
    splitArea: {
      show: false           // 隐藏分割区域（可选）
    },

    // shape: 'circle',
    indicator: [] as any[]
  },
  series: [
    {
      name: ' ',
      type: 'radar',
      areaStyle: {
        color: '#5A85EC' // 半透明填充色
      },
      data: [
        {
          value: [],
          name: ' '
        }
      ]
    }
  ] as any[]
})

// 章节报告-综合概览-知识点掌握度
export const optionsOverView = reactive({
  title: {
    text: '知识点掌握度',
    left: 'center',
    bottom: 120,
    textStyle: {
      color: '#2A2B2A',
      fontSize: 18
    }
  },
  legend: {
    top: '5%',
    left: 'center',
    selectedMode: false,
  },
  series: [
    {
      name: '',
      type: 'pie',
      silent: true,
      label: {
        formatter: '{b}: {c}个知识点'
      },
      radius: ['50%', '80%'],
      center: ['50%', '70%'],
      startAngle: 180,
      endAngle: 360,
      data: []
    }
  ] as any[]
})
// 章节报告-弱项检测-知识点掌握度
export const optionsWeakItem = reactive({
  title: {
    text: '知识点掌握度',
    left: 'center',
    bottom: 120,
    textStyle: {
      color: '#2A2B2A',
      fontSize: 18
    }
  },
  legend: {
    top: '5%',
    left: 'center',
    selectedMode: false,
  },
  series: [
    {
      name: '',
      type: 'pie',
      silent: true,
      label: {
        formatter: '{b}: {c}个知识点'
      },
      radius: ['50%', '80%'],
      center: ['50%', '70%'],
      startAngle: 180,
      endAngle: 360,
      data: []
    }
  ] as any[]
})
// 章节报告-针对学习
export const optionsVideoVo = reactive({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      label: {
        backgroundColor: '#FFF'
      }
    },
    formatter: function(params) {
      let tooltipContent = params[0].name + '<br/>';
      
      // 遍历所有系列的数据
      params.forEach(param => {
        const marker = param.marker;
        const seriesName = param.seriesName;
        const value = param.value;
        
        // 根据系列名称显示不同单位和处理数值
        if (seriesName === '观看时长') {
          // 将毫秒转换为分钟，四舍五入取整数
          const minutes = Math.round(value / 60000);
          tooltipContent += marker + ' ' + seriesName + ': ' + minutes + '分钟<br/>';
        } else {
          tooltipContent += marker + ' ' + seriesName + ': ' + value + '次<br/>';
        }
      });
      
      return tooltipContent;
    }
  } as any,
  legend: {},
  xAxis: [
    {
      type: 'category',
      data: [] as any[],
      axisLabel: {
        formatter: function (value) {
          // 超过5个字符的标签换行显示
          return value.length > 5 ? value.substring(0, 5) + '\n' + value.substring(5) : value;
        }
      }
    }
  ] as any[],
  yAxis: [
    {
      type: 'value',
      scale: true,
      name: '单位：分钟',
      min: 0,
      interval: 60000, // 设置间隔为1分钟 (60000毫秒)
      minInterval: 60000, // 最小间隔为1分钟，防止重复显示
      axisLabel: {
        formatter: function(value) {
          // 将毫秒转换为分钟并展示为整数
          const minutes = Math.floor(value / 60000);
          // 只显示整数分钟
          return minutes % 1 === 0 ? minutes : '';
        }
      }
    },
    {
      type: 'value',
      scale: true,
      name: '单位：次',
      min: 0,
      show: false, // 隐藏右侧Y轴
      axisLabel: {
        formatter: function(value) {
          // 确保显示为整数
          return Math.round(value);
        }
      }
    }
  ] as any[],
  series: [
    {
      name: '观看时长',
      type: 'bar',
      data: [] as any[],
    },
    {
      name: '观看次数',
      type: 'line',
      yAxisIndex: 1,
      data: [] as any[],
    }
  ] as any[]
})
// 章节报告-学后检测-1
export const optionsWeakItem1 = reactive({
  color: ['#5A85EC', '#C4C4C4'],
  series: [
    {
      name: '',
      type: 'pie',
      silent: true,
      radius: ['120%', '140%'],
      center: ['50%', '80%'],
      startAngle: 180,
      label: {
        show: false
      },
      max: 100,
      min: 0,
      endAngle: 360,
      data: []
    }
  ] as any[]
})
// 章节报告-学后检测-2
export const optionsWeakItem2 = reactive({
  color: ['#5A85EC', '#C4C4C4'],
  series: [
    {
      name: '',
      type: 'pie',
      silent: true,
      radius: ['120%', '140%'],
      center: ['50%', '80%'],
      startAngle: 180,
      endAngle: 360,
      label: {
        show: false
      },
      max: 100,
      min: 0,
      data: []
    }
  ] as any[]
})
// 章节报告-阶段检测
export const optionsStageItemVo = reactive({
  tooltip: {
    trigger: 'axis',
    formatter: (params: any) => {
      var relVal = params[0].name;
      for (var i = 0, l = params.length; i < l; i++) {
        var value = '<br/>' + params[i].marker + params[i].seriesName + '：' + Number(params[i].value) * 100;
        relVal += value + "%"
      }
      return relVal;
    }
  } as any,
  legend: {
    data: ['我的正确率', '全网平均正确率']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  color: ['#5A85EC', '#2A2B2A'],

  xAxis: {
    type: 'category',
    boundaryGap: false,

    data: ['简单题', '较易题', '中等题', '较难题', '难题'] as any[]
  } as any,
  yAxis: {
    axisLabel: {
      formatter: (value: any) => {
        return `${value * 100} %`
      }
    } as any,
    name: '单位：%',
    max: 1,
    min: 0,
    type: 'value'
  } as any,
  series: [
    {
      name: '我的正确率',
      type: 'line',
      data: [] as any[]
    },
    {
      name: '全网平均正确率',
      type: 'line',
      data: [] as any[]
    },
  ] as any[]
})
// 章节报告-做题时长统计
export const optionsDoQuesVos = reactive({
  legend: {
    selectedMode: false,
  },
  xAxis: {
    type: 'category',
    data: [] as any[]
  } as any,
  yAxis: {
    type: 'value'
  } as any,
  series: [
    {
      data: [] as any[],
      name: '观看时长',
      label: {
        show: true, // 显示标签
        position: 'top', // 标签位置，可选'top', 'inside', 'insideTop'等
        formatter: '{c}秒' // 显示数据值，{c}表示数据值
      },
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0, color: '#C8D8FF' // 渐变起始颜色
          }, {
            offset: 1, color: '#5A85EC' // 渐变结束颜色
          }]
        }
      },
      type: 'bar'
    }
  ] as any[]
})
