<template>
    <topbar></topbar>
    <template v-if="!hideTab">
      <div class="middle-tab flex-center" v-show="showTab">
          <div class="middle-tab-box flex-center">
            <template v-for="item in routeData.menuList">
              <!-- 知识点视频 -->
              <template v-if="item.path=='/point_room'">
                <template v-if="gradeId>=10">
                  <div class="middle-tab-box-child flex-center" :class="item.selected?'middle-tab-box-child-selected':''" @click="setRoute(item)">
                      <div v-if="item.meta.icon == 'ai-companion'" class="flex-center">
                          <img class="ai-companion-img" :src="getUrl(item.meta.icon)" :alt="item.meta.icon">
                      </div>
                      <div v-else class="flex-center">
                          <img class="middle-tab-box-child-icon" :src="getUrl(item.meta.icon)" :alt="item.meta.icon">
                          <span class="middle-tab-box-child-text">{{ item.meta.title }}</span>
                      </div>
                  </div>
                </template>
              </template>
              <!-- 名师课堂 -->
              <template v-else-if="item.path=='/teach_room'">
                <template v-if="gradeId<10">
                  <div class="middle-tab-box-child flex-center" :class="item.selected?'middle-tab-box-child-selected':''" @click="setRoute(item)">
                      <div v-if="item.meta.icon == 'ai-companion'" class="flex-center">
                          <img class="ai-companion-img" :src="getUrl(item.meta.icon)" :alt="item.meta.icon">
                      </div>
                      <div v-else class="flex-center">
                          <img class="middle-tab-box-child-icon" :src="getUrl(item.meta.icon)" :alt="item.meta.icon">
                          <span class="middle-tab-box-child-text">{{ item.meta.title }}</span>
                      </div>
                  </div>
                </template>
              </template>

              <!-- 名师同步学 -->
              <template v-else-if="item.path=='/synchronous'">
                <template v-if="gradeId<10">
                  <div class="middle-tab-box-child flex-center" :class="item.selected?'middle-tab-box-child-selected':''" @click="setRoute(item)">
                      <div v-if="item.meta.icon == 'ai-companion'" class="flex-center">
                          <img class="ai-companion-img" :src="getUrl(item.meta.icon)" :alt="item.meta.icon">
                      </div>
                      <div v-else class="flex-center">
                          <img class="middle-tab-box-child-icon" :src="getUrl(item.meta.icon)" :alt="item.meta.icon">
                          <span class="middle-tab-box-child-text">{{ item.meta.title }}</span>
                      </div>
                  </div>
                </template>
              </template>
              <div class="middle-tab-box-child flex-center" :class="item.selected?'middle-tab-box-child-selected':''" @click="setRoute(item)" v-else>
                  <div v-if="item.meta.icon == 'ai-companion'" class="flex-center">
                      <img class="ai-companion-img" :src="getUrl(item.meta.icon)" :alt="item.meta.icon">
                  </div>
                  <div v-else class="flex-center">
                      <img class="middle-tab-box-child-icon" :src="getUrl(item.meta.icon)" :alt="item.meta.icon">
                      <span class="middle-tab-box-child-text">{{ item.meta.title }}</span>
                  </div>
              </div>
            </template>
          </div>
          <!-- <div v-if="!orgName && !isAiDomain" class="link-box" @click="toWeb"><span>老师端</span></div> -->
      </div>
      <div class="crumb-box flex-center" v-show="!showTab && !hideCrumb &&!isHide">
          <div class="crumb-box-box flex-center">
              <div class="back-route" @click="goBack"> < 返回 </div>
              <div v-html="crumbsText"></div>
          </div>
      </div>
    </template>
    <!-- 全屏展示页面 -->
    <div class="main-full" v-if="isFull">
      <template v-if="isThree">
          <router-view v-slot="{ Component }">
              <!-- <component :is="Component" /> -->
              <router-view /> <!-- 自动渲染子路由 -->
          </router-view>
      </template>
      <template v-else>
          <router-view />
      </template>
    </div>
    <div class="content-box" v-else>
        <div class="main-box" :class="showTab?'top10':''">
            <template v-if="isThree">
                <router-view v-slot="{ Component }">
                    <!-- <component :is="Component" /> -->
                    <router-view /> <!-- 自动渲染子路由 -->
                </router-view>
            </template>
            <template v-else>
                <router-view />
            </template>
        </div>
    </div>
</template>
  
<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import { RouterView } from 'vue-router'
import router from '@/router/index'
import { useRouteStoreHook } from '@/store/modules/route'
import topbar from '@/layout/topbar.vue'
import { useRoute } from "vue-router"
import { dataDecrypt } from "@/utils/secret"
const route = useRoute()
let gradeId = ref(0)
if(localStorage.learnNow){
  gradeId.value = JSON.parse(localStorage.learnNow).gradeId
}
let orgName = localStorage.userInfo?.orgName
const learnUsers = JSON.parse(localStorage.learnUsers || "[]") || []
const gradeName =  learnUsers[0]?.gradeName
const knowledgeTitle = computed(() => {
  const highSchoolGrades = ['高一', '高二', '高三'];
  if (highSchoolGrades.includes(gradeName)) {
    return '知识点视频'
  }
  return '名师知识点'
})
console.log("gradeName---",gradeName,orgName,route.fullPath)

// 检查当前域名是否为ai.xyedu.com
const isAiDomain = computed(() => {
  const currentUrl = window.location.href
  return currentUrl.startsWith('https://ai.xyedu.com/')
//   return currentUrl.startsWith('https://ai-test.xiaoyeoo.com/')
})

useRouteStoreHook().initMenuList()
const routeData = useRouteStoreHook()

// 遍历menuList，动态设置TeachRoom页面的title
const updateTeachRoomTitle = () => {
  if (routeData.menuList && routeData.menuList.length > 0) {
    routeData.menuList.forEach(menu => {
      if (menu.name === 'TeachRoom') {
        menu.title = knowledgeTitle.value
      }
      // 如果有子菜单，也需要遍历
      if (menu.children && menu.children.length > 0) {
        menu.children.forEach(child => {
          if (child.name === 'TeachRoom') {
            child.title = knowledgeTitle.value
          }
        })
      }
    })
  }
}

// 初始化菜单标题
updateTeachRoomTitle()
const isHide = computed(() => {
    return router.currentRoute.value.meta.isHide
})
const showTab = computed(() => {
    return router.currentRoute.value.meta.showTab
})
const hideCrumb = computed(() => {
    return router.currentRoute.value.meta.hideCrumb
})
const hideTab = computed(() => {
    return router.currentRoute.value.meta.hideTab
})

const isFull = computed(() => {
    return router.currentRoute.value.meta.isFull
})

const isThree = computed(() => {
    return router.currentRoute.value.meta.requiresNestedView
})
const crumbsText = computed(() => {
    let text = ''
    const currentRoute = router.currentRoute.value
    
    currentRoute.matched.map((item: any, index: number) => {
        let title = item.meta.title
        
        // 如果是知识图谱详情页面，根据type参数动态修改标题
        if (item.name === 'KnowledgeGraphDetail' && currentRoute.query.type) {
            switch (currentRoute.query.type) {
                case 'synchronous':
                    title = '同步模式'
                    break
                case 'improvement':
                    title = '提高模式'
                    break
                case 'examPrep':
                    title = '备考模式'
                    break
                default:
                    title = item.meta.title
                    break
            }
        }
    
        if (index == 0) {
            text += title
        } else {
            text += `<span class='mag'> > </span>${title}`
        }
    })
    return text
})
// const showTab = router.currentRoute.value.meta.showTab

const getUrl = (url: string) => {
  return new URL(`../assets/img/layout/${url}.png`, import.meta.url).href //静态资源引入为url，相当于require()
}
const setRoute = (item: any) => {
    router.push({
        path: item.path
    })
    useRouteStoreHook().setSelectMenu(item.name)
}

const goBack = () => {
    // 检查是否有自定义的返回方法
    if (window?.customGoBack && typeof window?.customGoBack === 'function') {
        console.log("使用自定义返回方法")
        window?.customGoBack()
        return
    }

    // 此处如果有设置backPath,就返回到backPath，如果没有就返回上一页
    const backPath = router.currentRoute.value.meta.backPath
    if (backPath) {
        const matched:any = route.matched
        if(matched.length==3&&matched[1].name=='MyPaperT'&&matched[2].name=='TruePaperDetailM'){
          //处理真题试卷-我的试卷-试卷详情返回
          router.go(-1)
        }else{
          router.push({
              path: backPath as string
          })
        }
    } else {
        const index = router.currentRoute.value.matched.findIndex((item) => item.path == router.currentRoute.value.path)
        const path = router.currentRoute.value.matched[index - 1]?.path as string
        router.push({
            path: path
        })
    }
}

const toWeb = () => {
    window.open('https://tiku.xiaoyeoo.com/login')
}
</script>
<style lang="scss" scoped>
.flex-center {
    display: flex;
    align-items: center;
}

.crumb-box {
    width: 100%;
    justify-content: center;
    height: 3.0625rem;
    background-color: #F5F5F5;
    &-box {
        width: 81.25rem;
        height: 3.0625rem;
        color: #666666;
        font-size: .875rem;
        font-weight: 400;
        .back-route {
            color: #009c7f;
            cursor: pointer;
            margin-right: 1.875rem;
        }
    }
}
.middle-tab {
    width: 100%;
    justify-content: center;
    height: 5.8125rem;
    background-image: url(@/assets/img/layout/middle-bg.png);
    background-size: cover;
    background-repeat: no-repeat;
    position: relative;
    &-box {
        width: 95.25rem;
        height: 2.75rem;
        justify-content: center;
        &-child {
            width: 8.875rem;
            height: 2.75rem;
            border-radius: 1.375rem;
            justify-content: center;
            margin-right: 1.875rem;
            cursor: pointer;
            &:hover {
                background: rgb(236, 249, 234);
                color: #2A2B2A;
            }
            &-selected {
                color: rgb(255, 255, 255);
                background: linear-gradient(147.38deg, rgb(54, 226, 194) 14.694%,rgb(0, 183, 208) 83.07%);
            }
            &-icon {
                width: 1.625rem;
                height: 1.625rem;
                margin-right: .375rem;
            }
            &-text {
                font-size: 1.25rem;
                font-weight: 400;
            }
        }
        .ai-companion-img {
            width: 7rem;
            height: 2.25rem;
        }
    }
}
.link-box {
    position: absolute;
    top: 2rem;
    right: 0;
    cursor: pointer;
    background-image: url(@/assets/img/layout/to-web.png);
    background-size: contain;
    width: 2.1875rem;
    height: 6.4375rem;
    letter-spacing: .375rem;
    writing-mode: vertical-lr;
    background-repeat: no-repeat;
    color: #ffffff;
    font-size: 1rem;
    font-weight: 700;
    span{
        margin-left: .6875rem;
        margin-top: 1.375rem;
        display: inline-block;
    }
}
.top10 {
    margin-top: .625rem;
}
.content-box {
    display: flex;
    justify-content: center;
    background: #f5f5f5;
    // height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-rotute-tab-height));
    .main-box {
        width: 81.25rem;
        height: 100%;
    }
}
.main-full{
  width: 100%;
  height: calc(100vh - var(--v3-navigationbar-height));
  display: flex;
}
</style>
<style lang="scss">
.popper-child {
    padding: 0rem;
    margin: 0rem;
    inset: 4.375rem auto auto 0rem!important;
    display: flex;
    justify-content: center;
    background: #ffffffcc!important;
    backdrop-filter: blur(6.75rem);
    box-shadow: 0 .0625rem .625rem 0 #00000040;
    .child-box {
        display: flex;
        width: 81.25rem;
        justify-content: center;
        .child-item {
            width: 26.25rem;
            height: 9.125rem;
            box-sizing: border-box;
            border-radius: .8125rem;
            margin-right: 1.25rem;
            display: flex;
            cursor: pointer;
            background: #ffffff;
            &:hover {
                border: .125rem solid rgb(0, 201, 163);
            }
            img {
                width: 3.75rem;
                height: 3.75rem;
                margin-top: 1.25rem;
                margin-left: 1.25rem;
                margin-right: 1.125rem;
                border-radius: 50%;
            }
            &-info {
                color: #666666;
                margin-top: 1.25rem;
                width: 14.125rem;
                font-size: .875rem;
                font-weight: 400;
                display: flex;
                flex-direction: column;
                span {
                    margin-bottom: .875rem;
                    &:first-child {
                        color: #2a2b2a;
                        font-size: 1rem;
                        font-weight: 700;
                    }
                }
            }
            &-btn {
                height: 100%;
                display: flex;
                flex-direction: column-reverse;
                justify-content: space-between;
                &-current {
                    width: 6rem;
                    height: 1.9375rem;
                    line-height: 1.9375rem;
                    border-radius: 0 .625rem 0 .625rem;
                    background: linear-gradient(135deg, #36e2c2 0%, #00b7d0 100%);
                    color: #ffffff;
                    font-size: .875rem;
                    text-align: center;
                }
                &-text {
                    color: #009c7f;
                    font-size: .875rem;
                    text-decoration: underline;
                    text-align: right;
                    margin-right: .625rem;
                    margin-bottom: .625rem;
                }
            }
        }
        .child-item-selected {
            background: #e5f9f6;
            .child-item-btn-text {
                color: #999999;
            }
        }
        .info-parents {
            margin-right: -7.5rem;
            display: flex;
            flex-direction: column-reverse;
            color: #009c7f;
            margin-bottom: .625rem;
            &:hover{
              cursor: pointer;
            }
        }
    }
}
.mag {
    margin: 0 .375rem;
    display: inline-block;
}
</style>