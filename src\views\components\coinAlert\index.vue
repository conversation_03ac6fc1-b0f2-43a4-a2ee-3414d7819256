<template>
  <!-- 积分弹窗 -->
  <template v-if="props.show">
    <div v-show="props.hide">
      <div class="jf_bg"></div>
      <div class="jf_box">
        <div class="jf_inner">
          <div class="jf_gif" id="lottie2"></div>
          <div class="jf_tip">{{state.second}}s后自动关闭</div>
          <!-- 上移动画 -->
          <div class="jf_num showup">+{{props.num}}</div>
        </div>
      </div>
    </div>
  </template>
</template>

<script lang="ts" setup>
  import { reactive, watch } from 'vue';
  import { animationData } from '../../../utils/lottie/jifen'
  import { addIntegralApi } from "../../../api/user"

  const emit = defineEmits(['close'])
  const props = defineProps({
    show: {
      type: Boolean,
      default: false
    },
    // 不弹窗只请求接口
    hide: {
      type: Boolean,
      default: true
    },
    num: {
      type: String,
      default: '0'
    },
    source: {
      type: String,
      default: '0'
    },
    //是否请求接口
    isAjax: {
      type: Boolean,
      default: true
    },
  })

  watch(() => props.show, (newVal, oldVal) => {
    if (newVal) {
      setTimeout(()=>{
        setCoin()
      },10)
    }
  }, { immediate: true })

  let interval : any = null
  let learnNow = localStorage.learnNow ? JSON.parse(localStorage.learnNow) : ''
  const state : any = reactive({
    learnId: learnNow?.learnId || '',
    second: 3
  })

  //积分动画
  const setCoin = () => {
    let window1 : any = window
    let lottie : any = window1?.lottie
    let params = {
      container: document.getElementById('lottie2'),
      renderer: 'svg',
      loop: false,
      autoplay: true,
      animationData: animationData
    };
    lottie.loadAnimation(params);
    addCoin()
  }

  // 添加积分
  const addCoin = () => {
    let param = {
      learnUserId: state.learnId,
      type: 0,//0=加积分 1=减积分
      integral: props.num,//积分
      source: props.source,//0=登陆5分 1=做题得分 2= 学习视频课程 3=看视频知识点 4学练时长 5兑换物品
      isData: 0//ture:机构排名 false：全部排名
    }
    if(props.isAjax){
      addIntegralApi(param).then(() => {
        interval = setInterval(remainTime, 1000)
      })
    }else{
      interval = setInterval(remainTime, 1000)
    }
  }

  //关闭弹窗
  const quitHide = () => {
    if (interval) {
      clearInterval(interval)
    }
    emit('close', false)
  }

  //倒计时
  const remainTime = () => {
    if (state.second == 0) {
      clearInterval(interval)
      state.second = 3
      quitHide()
    } else {
      state.second--
    }
  }
</script>
<style lang="scss" scoped>
  /* 积分弹窗 */
  .jf_bg {
    z-index: 2000;
    background: rgba(0, 0, 0, .7);
    position: fixed;
    width: 100%;
    height: 100vh;
    top: 0;
    left: 0;
  }

  .jf_box {
    z-index: 9999;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
  }

  .jf_inner {
    width: 1300px;
    border-radius: 20px;
    box-sizing: border-box;
    padding: 0 20px;
  }

  .jf_gif {
    width: 100%;
    height: 500px;
    overflow: hidden;
  }

  .jf_tip {
    float: left;
    width: 100%;
    line-height: 16px;
    text-align: center;
    color: #c3c3c3;
    font-size: 12px;
    font-weight: 400;
    margin: -85px 0 42px;
  }

  .jf_num {
    float: left;
    width: 100%;
    line-height: 66px;
    text-align: center;
    color: #ffffff;
    font-size: 50px;
    font-weight: 700;
    position: relative;
    top: -150px;
    opacity: 0;
    left: -10px;
  }

  /* 上移动画 */
  @keyframes showup {
    0% {
      top: 0;
      opacity: 0;
    }

    50% {
      top: -150px;
      opacity: 1;
    }

    100% {
      top: -150px;
      opacity: 1;
    }
  }

  .showup {
    -webkit-animation: showup 4s;
  }
</style>