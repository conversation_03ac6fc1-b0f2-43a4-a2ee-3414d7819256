<!-- 知识点-首页 -->
<template>
  <div class="content">
    <!-- 小学 -->
    <div class="sub_box" v-if="state.gradeId<=6">
      <div class="sub_bg" @click="goDetail"></div>
      <div class="inner">
        <div class="sub_ul">
          <div class="sub_li sub11" @click="goList('语文')">
            <img src="@/assets/img/teachroom/yuwen2.png" />
            <div class="sub_txt">语文名师课堂</div>
            <div class="sub_btn">开始学习</div>
          </div>
          <div class="sub_li sub10" @click="goList('数学')">
            <img src="@/assets/img/teachroom/shuxue2.png" />
            <div class="sub_txt">数学名师课堂</div>
            <div class="sub_btn">开始学习</div>
          </div>
          <div class="sub_li sub12" @click="goList('英语')">
            <img src="@/assets/img/teachroom/yingyu2.png" />
            <div class="sub_txt">英语名师课堂</div>
            <div class="sub_btn">开始学习</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 初中，7年级没有化学，初中没科学 -->
    <div class="sub_box" v-else>
      <div class="sub_bg sub_bg2" @click="goDetail"></div>
      <div class="inner">
        <div class="sub_ul">
          <div class="sub_li2 shuxue" @click="goList('数学')">
            <img src="@/assets/img/teachroom/shuxue.png" />
            <div class="sub_txt2">数学</div>
            <div class="sub_btn2">开始学习</div>
          </div>
          <div class="sub_li2 wuli" @click="goList('物理')">
            <img src="@/assets/img/teachroom/wuli.png" />
            <div class="sub_txt2">物理</div>
            <div class="sub_btn2">开始学习</div>
          </div>
          <div class="sub_li2 huaxue" @click="goList('化学')" v-if="state.gradeId!=7">
            <img src="@/assets/img/teachroom/huaxue.png" />
            <div class="sub_txt2">化学</div>
            <div class="sub_btn2">开始学习</div>
          </div>
          <div class="sub_li2 shengwu" @click="goList('生物')">
            <img src="@/assets/img/teachroom/shengwu.png" />
            <div class="sub_txt2">生物</div>
            <div class="sub_btn2">开始学习</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { reactive, onMounted } from 'vue';
  import router from '@/router/index'
  import { useUserStore } from "@/store/modules/user"
  import { useRouteStoreHook } from '@/store/modules/route'

  defineOptions({
    name: "PointRoomIndex"
  })

  const learnNow = useUserStore().learnNow
  const state : any = reactive({
    gradeId: learnNow.gradeId || 0
  })

  onMounted(() => {
    init()
  })

  const init = () => {
    //tabbar选中
    useRouteStoreHook().setSelectMenu('PointRoom')
  }

  //跳介绍
  const goDetail = () => {
    router.push({ name: "PointRoomIntroduce" })
  }

  //跳章节列表
  const goList = (subName : any) => {
    router.push({ name: "PointRoomTeachList", query: { subName } })
  }
</script>

<style lang="scss" scoped>
  @import url('@/assets/styles/reset.css');

  .header_seat {
    width: 100%;
    height: 4.375rem;
    float: left;
  }

  .none {
    display: none !important;
  }

  .nowrap {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .content {
    width: 100%;
    overflow-y: auto;
    background: #F5F5F5;
    display: flex;
    flex: 1;
    padding-bottom: 1.125rem;
  }

  .inner {
    margin: 0 auto;
    width: 81.25rem;
  }

  /* 导航菜单 */
  .navbox {
    float: left;
    width: 100%;
    height: 6.5625rem;
    background: url(@/assets/img/teachroom/barbg.png) no-repeat;
    background-size: 120rem 6.5625rem;
    position: relative;
    z-index: 3;
    overflow: hidden;
  }

  .nav_lt {
    float: left;
    width: 100%;
    box-sizing: border-box;
    padding: 1.6875rem 1.25rem 1.375rem;
  }

  .nav_lt>div {
    float: left;
    width: 8.875rem;
    height: 2.75rem;
    border-radius: 1.375rem;
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 1.625rem;
    color: #2a2b2a;
    font-size: 1.25rem;
    margin: 0 1.25rem 0 0;
  }

  .nav_lt>div:hover {
    cursor: pointer;
    background: #ecf9ea;
  }

  .nav_lt>div.active {
    color: #fff;
    background: linear-gradient(147.5deg, #36e2c2 0%, #00b7d0 100%);
  }

  .nav_lt>div img {
    width: 1.625rem;
    height: 1.625rem;
    margin: 0 .375rem 0 0;
  }

  .nav_ai img {
    width: 7.0625rem !important;
    height: 2.3125rem !important;
    margin: 0 !important;
  }

  .nav_rt {
    float: right;
    width: 10.5rem;
    height: 2.5rem;
    position: relative;
    top: -4.1875rem;
    left: 4.8125rem;
    z-index: 2;
    /* position: absolute;
    top: 6.0625rem;
    right: 2.5rem; */
  }

  .nav_rt:hover {
    cursor: pointer;
  }

  /* 学科 */
  .sub_box {
    float: left;
    width: 100%;
    flex: 1;
  }

  .sub_bg {
    float: left;
    width: 100%;
    height: 33.0625rem;
    background: url(@/assets/img/pointroom/bg1.png) no-repeat;
    background-size: 120rem 33.0625rem;
    background-position: center center;
    margin: -1.375rem 0 0;
  }

  .sub_ul {
    float: left;
    width: 100%;
    margin: 1.25rem 0;
  }

  /* 高中 */
  .sub_li2 {
    float: left;
    width: 12.5rem;
    height: 14rem;
    border-radius: .625rem;
    box-sizing: border-box;
    padding: 1.625rem 0 0 1.625rem;
    overflow: hidden;
    margin: 0 1.25rem 0 0;
  }

  .sub_li2:hover {
    cursor: pointer;
    box-shadow: 0 .125rem 1.25rem 0 #00000026;
  }

  .sub_li2:last-child {
    margin: 0;
  }

  .sub_txt2 {
    float: left;
    width: 5rem;
    line-height: 2.5rem;
    color: #2a2b2a;
    font-size: 1.875rem;
    font-weight: 700;
    margin: 0 0 .875rem;
  }

  .sub_btn2 {
    float: left;
    width: 6.25rem;
    line-height: 2.25rem;
    border-radius: 1.6875rem;
    font-size: 1rem;
  }

  .sub_li2 img {
    float: right;
    width: 8.75rem;
    height: 8.75rem;
    margin: 3.5rem 0 -12.5rem;
  }

  .yuwen {
    background: #ffeae4;
  }

  .yuwen .sub_txt2,
  .yuwen .sub_btn2 {
    color: #8C4735;
  }

  .shuxue {
    background: #E5EAFF;
  }

  .shuxue .sub_txt2,
  .shuxue .sub_btn2 {
    color: #455EAB;
  }

  .yingyu {
    background: #FFF2E6;
  }

  .yingyu .sub_txt2,
  .yingyu .sub_btn2 {
    color: #BC5924;
  }

  .wuli {
    background: #DFF4FF;
  }

  .wuli .sub_txt2,
  .wuli .sub_btn2 {
    color: #2F5A72;
  }

  .shengwu {
    background: #E5F9F0;
  }

  .shengwu .sub_txt2,
  .shengwu .sub_btn2 {
    color: #0E9984;
  }

  .kexue {
    background: #E5EAFF;
  }

  .kexue .sub_txt2,
  .kexue .sub_btn2 {
    color: #455EAB;
  }

  .huaxue {
    background: #E8F2FC;
  }

  .huaxue .sub_txt2,
  .huaxue .sub_btn2 {
    color: #48608E;
  }
</style>
