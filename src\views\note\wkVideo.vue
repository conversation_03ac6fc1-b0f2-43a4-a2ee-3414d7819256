<!-- 错题本-单个知识点视频-学大（文科为主） -->
<template>
  <div class="content" v-show="state.isShow && state.pointId">
    <div class="inner">
      <div class="wrap">
        <div class="menu_lt">
          <div class="wk_box">
            <!-- 播放器 -->
            <div id="dplayer1" class="wk_video" v-show="state.videoUrl|| state.videoList.length"></div>
            <div class="wk_nodata" v-show="!(state.videoUrl|| state.videoList.length)">
              <img src="@/assets/img/teachroom/nopoint.png" />暂无知识点视频
            </div>
            <div class="wk_opt">
              <!-- <div class="wk_thumbs active">
                <img src="@/assets/img/teachroom/thumbs2.svg" />
                <img src="@/assets/img/teachroom/thumbs2sel.svg" />
                <div>已点赞</div>
              </div> -->
              <div class="wk_names" v-if="state.videoUrl">
                <div class="wk_name active" style="margin-right: 0;">
                  <img src="@/assets/img/teachroom/playing1.gif" />
                  <div class="nowrap">{{state.vidInfo.title}}</div>
                </div>
                <!-- <div class="wk_name">
                  <img src="@/assets/img/teachroom/playing1.gif" />
                  <div class="nowrap">例题1</div>
                </div>
                <div class="wk_name">
                  <img src="@/assets/img/teachroom/playing1.gif" />
                  <div class="nowrap">例题2</div>
                </div>
                <div class="wk_name">
                  <img src="@/assets/img/teachroom/playing1.gif" />
                  <div class="nowrap">例题3</div>
                </div> -->
              </div>
            </div>
          </div>

          <div class="vid_more" v-show="state.videoUrl">
            <div class="vid_tit">
              <img src="@/assets/img/teachroom/know.png" />更多课程
            </div>
            <div class="swiper-container vid_ul">
              <div class="swiper-wrapper">
                <template v-if="state.chapterId">
                  <!-- <div class="swiper-slide" >
                    <div class="vid_li active" v-for="(item,index) in state.data.mdata">
                      <div class="vid_img">
                        <img src="@/assets/img/teachroom/novid.png" />
                        <div class="vid_status status0">未学习</div>
                        <div class="vid_status status1">未学完</div>
                        <div class="vid_status status2">已学完</div>
                      </div>
                      <div class="vid_name nowrap">1例题1例题1例题1例题1例题1例题1例题1例题1例题1例题1</div>
                    </div>
                  </div> -->
                </template>
                <template v-else>
                  <div class="swiper-slide" v-for="(item,index) in state.data.mdata">
                    <div class="vid_li" :class="state.videoIndex==index?'active':''" @click="changeTeacher(index)">
                      <div class="vid_img">
                        <img src="@/assets/img/teachroom/novid.png" />
                        <!-- <div class="vid_status status0">未学习</div>
                        <div class="vid_status status1">未学完</div>
                        <div class="vid_status status2">已学完</div> -->
                      </div>
                      <div class="vid_name nowrap">{{item.title}}</div>
                    </div>
                  </div>
                </template>

              </div>
              <!-- <div class="pagination"></div> -->
            </div>
            <!-- 左右箭头 -->
            <div class="turn lt">
              <img src="@/assets/img/teachroom/swiper_lt.svg" />
              <img src="@/assets/img/teachroom/swiper_lt2.svg" />
            </div>
            <div class="turn rt">
              <img src="@/assets/img/teachroom/swiper_rt.svg" />
              <img src="@/assets/img/teachroom/swiper_rt2.svg" />
            </div>
          </div>
          <div class="wk_know">
            <div class="vid_tit">
              <img src="@/assets/img/teachroom/know2.png" />知识点讲解
            </div>
            <!-- 优学派h5 -->
            <!-- <iframe src="https://onlinelibres1.noahedu.com/advertising/289/html/dist/index.html?knowledgeId=22181" class="iframe" id="iframe"></iframe> -->
            <!-- 学大html -->
            <div class="wkinfo" v-html="ReplaceMathString(state.explainStr)" v-if="state.explainStr"></div>
            <div class="wkinfo" v-else>暂无知识点讲解</div>
          </div>
        </div>
        <div class="menu_rt ">
          <img src="@/assets/img/teachroom/pointbg.png" class="pt_top" />
          <div class="pt_ul">
            <!-- 多知识点：章节列表进入 -->
            <template v-if="state.chapterId">
              <div class="pt_li " :class="index==0?'active':''" @tap="wekePlay2" :data-i="index"
                v-for="(item, index) in state.pointList" :key="index">
                <img src="@/assets/img/teachroom/playing2.gif" />
                <div class="nowrap" v-html="ReplaceMathString(item.name)"></div>
              </div>
            </template>
            <!-- 单知识点：错题本、练习报告，带知识点进入 -->
              <div class="pt_li " :class="index==0?'active':''" @tap="wekePlay" :data-i="index"
                v-for="(item, index) in state.pointList" :key="index">
                <img src="@/assets/img/teachroom/playing2.gif" />
                <div class="nowrap" v-html="ReplaceMathString(item.name)"></div>
              </div>
          </div>
          <div class="pt_btn" @click="goTrack" v-if="state.noteId">举一反三</div>
        </div>
      </div>
    </div>
  </div>
  <!-- 购买会员弹窗 -->
  <buyVip :show="state.showVip" @close="quitHide"></buyVip>
  <!-- 积分弹窗 -->
  <coinAlert :show="state.jfShow" :hide="state.jfHide" :num="state.jfNum" :source="state.jfSource" @close="jfHide">
  </coinAlert>
</template>

<script lang="ts" setup>
  import { reactive, onMounted, watch } from 'vue';
  import router from '@/router/index'
  import { ElMessage } from "element-plus"
  import { useUserStore } from "@/store/modules/user"
  import { subjectList } from '@/utils/user/enum'
  import { ReplaceMathString, replaceHttp } from '@/utils/user/util'
  import { setStudyTimeApi, analyseVideoNumApi } from "@/api/user"
  import { pointDetailsApi, queryPointCourseApi, savePointDescApi, videoModifyApi } from "@/api/video"
  import { useRoute } from "vue-router"
  import Swiper from 'swiper';
  import 'swiper/dist/css/swiper.min.css'
  import buyVip from "@/views/components/buyVip/index.vue"
  import coinAlert from "@/views/components/coinAlert/index.vue"
  import { setLearnKey } from '@/utils/user/learntime'
  import { dataEncrypt } from '@/utils/secret';

  defineOptions({
    name: "NoteWkvideo"
  })

  const route = useRoute()
  const query = reactive<any>(route.query)

  const state : any = reactive({
    showVip: false,
    mySwiper: null,
    dp: null,
    tabi: 0,
    isFirst: 1,
    isShow: false,
    data: [],
    poster: '',
    videoUrl: '',
    chapterId: '',
    pointId: '',
    pointIdOld: '',
    currentPointId: '',
    pointList: [],
    noteId: '',
    //知识点
    learnId: '',
    //显示去练习
    action: '',
    videoList: [],
    //视频
    videoIndex: 0,
    explainStr: '',
    autoplay: false,
    subject: '',
    //视频组件
    video: '',
    controls: false,
    videoBtn: true,
    playstate: 0,
    //0暂停，1播放
    speed: '1.0',
    //速度
    speedArr: ['0.5', '0.75', '1.0', '1.25', '1.5', '2.0'],
    rate: 0,
    //显示倍速
    playbtn: true,
    title: '',
    isThumbs: 0,
    isVidMenu: 0,
    //进度条
    showSlider: true,
    showComp: 0,
    slider: 0,
    curtime: 0,
    nowtime: '00:00',
    // 当前时间
    endtime: '00:00',
    // 总时长
    duration: '',
    // 视频长度秒
    isFull: false,
    gradeId: '',
    type: '',
    videoId: '',
    lastTime: '',
    usersNow: {},
    vidInfo: {},
    //积分
    jfShow: false,
    jfHide: true,
    jfNum: '0',
    jfSource: '0'
  })

  onMounted(() => {
    init()
  })

  //监听路由参数
  watch(
    () => route.query,
    (newQ) => {
      const vid = newQ.pointId,name:any=route.name
      if (vid && name.indexOf("NoteWkvideo")>-1) {
        init()
      }
    }
  )

  // 知识点讲解监听
  watch(() => state.vidInfo.summary, (newVal, oldVal) => {
    if (newVal) {
      setJfShow()
    }
  }, { immediate: true })

  // 显示积分-看知识点讲解
  const setJfShow = () => {
    state.jfShow = true
    state.jfHide = false
    state.jfNum = '3'
    state.jfSource = '1'
  }

  // 显示积分-看视频
  const setJfShow2 = () => {
    state.jfShow = true
    state.jfHide = true
    state.jfNum = '5'
    state.jfSource = '2'
  }

  // 隐藏积分
  const jfHide = () => {
    state.jfShow = false
  }

  // 属性赋值
  const setData = (obj : any) => {
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        state[key] = obj[key]
      }
    }
  }

  const quitHide = () => {
    state.showVip = false
    if (useUserStore().memberInfo) {
      init()
    }
  }

  const init = () => {
    let e = route.query
    if (e.pointId) {
      const learnNow : any = useUserStore().learnNow
      setData({
        chapterId: e.chapterId,
        pointIdOld: e.pointId || '',
        pointId: e.pointId || '',
        noteId: e.noteId || '',
        //分析试卷传入
        action: Number(e.action),
        //显示去练习
        learnId: learnNow?.learnId || '',
        gradeId: learnNow?.gradeId || '',
        type: e.type || '',
        subject: e?.subject || '0'
      });
      //初高中/错题本进入显示视频
      if (state.gradeId > 6 || state.type == 'note' || state.type == 'jiucuo') {
        getInfo();
      }
      //记录学习学科
      setLearnKey(state.subject)
    }
  }

  //获取详情
  const getInfo = () => {
    const { pointId, chapterId } = state;
    if (pointId) {
      //知识点、练习报告进入
      const {
        pointIdOld
      } = state;
      const param2 = {
        pointId: pointIdOld
      };
      pointDetailsApi(param2)
        .then((res : any) => {
          const res2 = res.data;
          const videos = res2.mdata;
          res2.id = pointIdOld
          setData({
            isShow: true,
            data: res2,
            videoList: videos,
            pointList: res2 ? [res2] : [],
            explainStr: setWkHtml(res2.desc)
          });
          setVideo(0);
          hasVideo();
          videoScoreSave()
          setTimeout(() => {
            swiperInit()
          }, 0)
          if (state.isFirst) {
            //初始化视频控件
            initPlayers()
          } else {
            switchVideo()
          }
          setData({
            isFirst: 0
          });

        })
        .catch(() => {
          setData({
            isShow: true
          });
        });
    } else {
      //知识点章节列表进入
      const param = {
        chapterId
      };
      queryPointCourseApi(param)
        .then((res : any) => {
          const res2 = res.data;
          setData({
            isShow: true,
            data: res2,
            pointList: res2,
            pointId: res2.length ? res2[0].id : ''
          });
          if (res2.length) {
            wekePlay2({
              currentTarget: {
                dataset: {
                  i: 0
                }
              }
            })
          }
        })
        .catch(() => {
          setData({
            isShow: true
          })
        })
    }
  }

  //修改知识点html
  const setWkHtml = (str : any) => {
    if (str) {
      //替换＜br/＞＜img alt=“菁优网“src=“http：//img.jyeoo.net/quiz/images/201107/35/3c5d8c86.png“style=“vertical-align：middle“/＞
      str = str
        .replace(/＜br\/＞/g, '\n')
        .replace(/＜img /g, '<img ')
        .replace(/“菁优网“/g, '"菁优网"')
        .replace(/src=“http：/g, 'src="http:')
        .replace(/png“/g, 'png"')
        .replace(/style=“/g, 'style="')
        .replace(/“\/＞/g, '"/>');
      return str;
    } else {
      return '暂无讲解';
    }
  }

  //设置视频信息,默认第1个
  const setVideo = (i : any) => {
    const videos = state.videoList;
    if (videos.length) {
      const arr = videos[i || 0];
      setData({
        videoId: arr.id,
        videoUrl: replaceHttp(arr.link),
        autoplay: true,
        vidInfo: arr
      });
    } else {
      setData({
        videoId: '',
        videoUrl: '',
        autoplay: false
      });
      //没视频，自动跳知识点讲解
      // setTimeout(() => {
      //   tabs('1');
      // }, 0);
    }
  }

  //有无视频
  const hasVideo = () => {
    if (!state.videoList.length) {
      return false;
    }
    return true;
  }

  const noVideoMsg = () => {
    ElMessage.error('本知识点暂无微课，\n请学习知识点讲解或学习\n下一个知识点微课！');
  }


  //获取点赞状态
  const getIsThunbs = () => {
    const {
      videoList,
      videoId
    } = state;
    let isThumbs = 0;
    if (videoList.length) {
      for (const i of videoList) {
        if (videoId == i.videoId) {
          isThumbs = i.userLike;
          break;
        }
      }
    }
    setData({
      isThumbs
    });
  }

  //记录知识点讲解-已学习状态
  const savePointDesc = () => {
    const {
      chapterId,
      pointId
    } = state;
    const param : any = {
      pointId
    };
    if (chapterId && chapterId != 'undefined') {
      param.chapterId = chapterId;
    }
    savePointDescApi(param)
  }

  //切换视频url
  const changeTeacher = (n : any) => {
    const { videoId, videoIndex } = state;
    if (!videoId) {
      noVideoMsg();
      return;
    }
    if (n == videoIndex) {
      return
    }
    setData({
      videoIndex: n,
      curtime: 0
    });
    setVideo(n);
    hasVideo();
    videoScoreSave()
    switchVideo()
  }

  // 判断会员状态
  const getIsMember = () => {
    const isVip = useUserStore().memberInfo
    if (isVip) {
      return true
    } else {
      state.showVip = true
      return false
    }
  }

  // 去练习-举一反三
  const goTrack = () => {
    if (getIsMember()) {
      router.push({
          name: 'worningLearningJ3',
          query: {
              data: dataEncrypt({
                  noteId: state.noteId,
                  pageSource: '9'
              })
          }
      })
    }
  }

  // 知识点切换-练习报告
  const wekePlay = (e : any) => {
    const i = e.currentTarget.dataset.i;
    const list = state.pointList;
    const state2 = list[i].studyStatus;
    if (state2 == '学习中') {
      return;
    }
    if (!state.videoId) {
      noVideoMsg();
      return;
    }
    setData({
      videoIndex: i,
      curtime: 0,
      title: list[i].name
    });
    setVideo(i);
  }
  // 知识点切换-章节进入
  const wekePlay2 = (e : any) => {
    const i = e.currentTarget.dataset.i;
    const list = state;
    let pointId = list[i].id
    //同一知识点没缓存
    if (pointId == pointId && state.localPoints[pointId]) {
      return
    }
    const param2 = {
      pointId
    };
    setData({
      pointId,
      explainStr: setWkHtml(list[i].desc),
      title: list[i].name
    })
    if (!state.localPoints[pointId]) {
      //没缓存
      pointDetailsApi(param2)
        .then((res : any) => {
          const res2 = res.data;
          const videos = res2.mdata;
          if (state.isFirst) {
            //初始化视频控件
            initPlayers()
          } else {
            switchVideo()
          }
          setData({
            videoList: videos
          });
          setVideo(0);
          hasVideo();
          videoScoreSave()
          //缓存知识点数据
          let { localPoints } = state
          if (!localPoints[pointId]) {
            localPoints[pointId] = res2
            setData({
              localPoints
            })
          }
        })
        .catch(() => {
          setData({
            isShow: true
          });
        });
    } else {
      //读缓存
      const videos = state.localPoints[pointId].mdata;
      setData({
        videoList: videos
      });
      setVideo(0);
      hasVideo();
    }
  }



  const swiperInit = () => {
    state.mySwiper = new Swiper('.swiper-container', {
      pagination: '.pagination',
      paginationClickable: true,
      spaceBetween: 10,
      slidesPerView: 4,
      slidesPerGroup: 4,
      loop: false,
      // 如果需要前进后退按钮
      nextButton: '.turn.rt',
      prevButton: '.turn.lt',
      freeMode: true//惯性滑动
    })
  }

  //记录视频播放次数（学大报错）
  const setUserVideoViewNumber = () => {
    const { chapterId, pointId, videoId, subject } = state
    const param : any = {
      time: 0,
      videoId,
      type: 2, //1优学派 2菁优网
      subject,
      pointId,
      viewType: true //true:观看次数+1；false:增加观看次数
    }
    if (chapterId && chapterId != 'undefined') {
      param.chapterId = chapterId
    }
    if (!videoId) {
      return
    }
    // setUserVideoViewNumberApi(param)
  }
  // 记录每日查看视频数量
  const analyseVideoNum = () => {
    const { subject } = state
    analyseVideoNumApi({ subject })
    setUserVideoViewNumber()
  }
  //记录视频播放状态（学大报错）
  const videoScoreSave = () => {
    const { learnId, chapterId, pointId, videoId } = state
    const param : any = {
      learnId,
      pointId,
      videoId
    }
    if (chapterId && chapterId != 'undefined') {
      param.chapterId = chapterId
    }
    if (!videoId) {
      savePointDesc()
      return
    }
    // videoScoreSaveApi(param)
    analyseVideoNum()
    getIsThunbs()
  }

  // 设置学习状态（优学派）
  const setStudyState = (status : any) => {
    const { chapterId, pointId, videoId, subject, vidInfo } = state
    const param = {
      chapterId,
      pointId,
      videoId,
      type: 2, //1优学派 2菁优网
      subject,//:KeyValueOfSubject[subject],
      source: 2, //1同步章节 2知识点
      knowledgeName: vidInfo.title,
      status //0未学习 1正在学 2已学完
    }
    videoModifyApi(param)
    //改变学习状态
    vidInfo.studyStatus = status
    setData({
      vidInfo
    })
  }

  // 点赞
  const setThumbs = () => {
    const { chapterId, pointId, videoId, subject, data, dataI, dataI2, dataI3, dataType, title } = state
    const param : any = {
      chapterId,
      pointId,
      videoId,
      type: 1, //1优学派 2菁优网
      subject: subjectList[subject].key,
      source: 2, //1同步章节 2知识点
      knowledgeName: title
    }
    const isThumbs = state.vidInfo.userLike ? 0 : 1
    if (isThumbs) {
      param.like = 1
      videoModifyApi(param)
        .then(() => {
          ElMessage.success('点赞成功')
          if (dataType == 0) {
            //概述
            data[dataI].knowledgeList[dataI2].videoInfos[dataI3].likeNum++
            data[dataI].knowledgeList[dataI2].videoInfos[dataI3].userLike = true
            // state.vidInfo.likeNum++
            // state.vidInfo.userLike = true
          } else {
            //例题
            data[dataI].knowledgeList[dataI2].exampleVideoInfos[dataI3].likeNum++
            data[dataI].knowledgeList[dataI2].exampleVideoInfos[dataI3].userLike = true
            // state.vidInfo.likeNum++
            // state.vidInfo.userLike = true
          }
          setData({
            data
          })
        })
    } else {
      param.like = 0
      videoModifyApi(param)
        .then(() => {
          ElMessage.success('已取消点赞')
          if (dataType == 0) {
            //概述
            data[dataI].knowledgeList[dataI2].videoInfos[dataI3].likeNum--
            data[dataI].knowledgeList[dataI2].videoInfos[dataI3].userLike = false
          } else {
            //例题
            data[dataI].knowledgeList[dataI2].exampleVideoInfos[dataI3].likeNum--
            data[dataI].knowledgeList[dataI2].exampleVideoInfos[dataI3].userLike = false
          }
          setData({
            data
          })
        })
    }
  }
  // 收藏
  const setCollect = () => {
    const { chapterId, pointId, videoId, subject, data, dataI, dataI2, dataI3, dataType } = state
    const param : any = {
      chapterId,
      pointId,
      videoId,
      type: 1, //1优学派 2菁优网
      subject,
      source: 2 //1同步章节 2知识点
    }

    if (dataType == 0) {
      //概述
      param.knowledgeName = data[dataI].knowledgeList[dataI2].videoInfos[dataI3].videoName
    } else {
      //例题
      param.knowledgeName = data[dataI].knowledgeList[dataI2].exampleVideoInfos[dataI3].videoName
    }
    const isCollect = state.vidInfo.userCollect ? 0 : 1
    if (isCollect) {
      param.collect = 1
      videoModifyApi(param)
        .then(() => {
          ElMessage.success('收藏成功')
          if (dataType == 0) {
            //概述
            data[dataI].knowledgeList[dataI2].videoInfos[dataI3].userCollect = true
            state.vidInfo.userCollect = true
          } else {
            //例题
            data[dataI].knowledgeList[dataI2].exampleVideoInfos[dataI3].userCollect = true
            state.vidInfo.userCollect = true
          }
          setData({
            data
          })
        })
    } else {
      param.collect = 0
      videoModifyApi(param)
        .then(() => {
          ElMessage.success('已取消收藏')
          if (dataType == 0) {
            //概述
            data[dataI].knowledgeList[dataI2].videoInfos[dataI3].userCollect = false
            state.vidInfo.userCollect = false
          } else {
            //例题
            data[dataI].knowledgeList[dataI2].exampleVideoInfos[dataI3].userCollect = false
            state.vidInfo.userCollect = false
          }
          setData({
            data
          })
        })
    }
  }

  //组件-播放
  const videoPlay = () => {
    if (state.videoUrl) {
      // state.dp.play()
      //自动播放-延迟且用户点击
      setTimeout(()=>{
        let video:any=document.getElementById('video')
        video.play()
      },100)
      setData({
        videoBtn: false
      })
    } else {
      ElMessage.error('请先选择一个视频')
    }
  }

  //组件-暂停
  const videoPause = () => {
    state.dp.pause()
    setData({
      videoBtn: true
    })
  }

  function initPlayers() {
    let { title, link, type } = state.vidInfo
    let window1 : any = window
    let DPlayer : any = window1?.DPlayer
    state.dp = new DPlayer({
      container: document.getElementById('dplayer1'),
      autoplay: true, //没效果
      theme: '#1DDFAC', //进度条、音量颜色
      preload: 'auto',
      volume: 1,
      contextmenu: [],
      video: {
        title,
        url: link,
        pic: '',
        type: !type ? 'normal' : 'hls'
      }
    });
    state.dp.on('loadstart', function () {
      showPlayIcon()
      // 修改设置为倍速弹窗
      let setbtn : any = document.querySelector('.dplayer-setting-icon')
      setbtn.addEventListener('mouseenter', function () {
        if (document.querySelectorAll('.dplayer-hide-controller').length) {
          // 底部菜单隐藏时不显示
          return
        }
        //显示倍速弹窗
        let setting : any = document.querySelector('.dplayer-setting-box')
        setting.className =
          'dplayer-setting-box dplayer-setting-box-narrow dplayer-setting-box-speed dplayer-setting-box-open'
      })
      //倍速弹窗hover隐藏
      let setting : any = document.querySelector('.dplayer-setting-box')
      setting.addEventListener('mouseleave', function () {
        setting.className = 'dplayer-setting-box'
      })
      setting.addEventListener('click', function () {
        setting.className = 'dplayer-setting-box'
      })

      //隐藏视频标题
      let vidClass : any = document.querySelector('.dplayer-video-current')
      vidClass.addEventListener('mouseleave', function () {
        if (document.querySelectorAll('.dplayer-paused').length) {
          // 暂停时不隐藏
          return
        }
        let title : any = document.getElementById('fulltit')
        title.style.display = 'none'
      })
    })

    //设置倍速文字
    state.dp.on('ratechange', function () {
      speedChange('')
    });

    state.dp.on('ended', function () {
      videoPause()
      // setStudyState(2)
      setJfShow2()
    });
    state.dp.on('pause', function () {
      showPlayIcon()
    });
    state.dp.on('play', function () {
      hidePlayIcon()
    });

    state.dp.on('volumechange', function () {
    })
  }


  fullScreenListeners()
  //监听全屏
  function fullScreenListeners() {
    document.addEventListener('fullscreenchange', function () {
      let fullIcon : any = document.querySelector('#fullIcon')
      if (document.fullscreenElement) {
        console.log('全屏');
        fullIcon.setAttribute('data-title', '退出全屏')
      } else {
        console.log('退出全屏');
        fullIcon.setAttribute('data-title', '全屏')
      }
    })
  }

  //显示播放图标
  function showPlayIcon() {
    let icon : any = document.querySelector('.dplayer-bezel-icon')
    if(icon){
      icon.className = 'dplayer-bezel-icon play'
    }
  }

  //隐藏播放图标
  function hidePlayIcon() {
    setTimeout(() => {
      let icon : any = document.querySelector('.dplayer-bezel-icon')
      if(icon){
        icon.className = 'dplayer-bezel-icon'
      }
    }, 0)
  }

  //切换视频
  function switchVideo() {
    let { title, link, type } = state.vidInfo
    let obj = {
      title,
      url: link,
      pic: '',
      type: !type ? 'normal' : 'hls'
    }
    let src = state.dp.video.src
    if (src == obj.url) {
      return
    }
    state.dp.switchVideo(obj);
    speedChange(1)
    //自动播放-必须延迟且是用户点击
    setTimeout(()=>{
      let video:any=document.getElementById('video')
      video.play()
    },100)
  }
  //倍速切换
  function speedChange(num : any) {
    let video : any = document.getElementById('video')
    let speed = num || video.playbackRate
    let sptxt : any = document.querySelector('.dplayer-icon.dplayer-setting-icon')
    if (speed == 1) {
      sptxt.innerHTML = '倍速'
    } else if (speed == 2) {
      sptxt.innerHTML = '2.0X'
    } else {
      sptxt.innerHTML = speed + 'X'
    }
    //倍速弹窗选中变色
    let list : any = document.querySelectorAll('.dplayer-setting-speed-item')
    for (let i = 0; i < list.length; i++) {
      let num = Number(list[i].attributes['data-speed'].value)
      if (num == speed) {
        list[i].className = 'dplayer-setting-speed-item green'
      } else {
        list[i].className = 'dplayer-setting-speed-item'
      }
    }
  }
  //视频控件e
</script>

<style lang="scss" scoped>
  @import url('@/assets/styles/reset.css');

  .header_seat {
    width: 100%;
    height: 4.375rem;
    float: left;
  }

  .none {
    display: none !important
  }

  .nowrap {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .content {
    width: 100%;
    background: #F5F5F5;
    overflow-y: auto;
  }

  .inner {
    margin: 0 auto;
    width: 81.25rem;
  }

  .wrap {
    float: left;
    width: 100%;
    /* height: calc(100vh - 7.4375rem);
    overflow-y: auto; */
  }

  .wrap>div {
    /* height: calc(100vh - 7.4375rem);
    overflow-y: auto; */
    border: .0625rem solid #eaeaea;
    box-sizing: border-box;
    background: #fff;
  }

  /* 视频 */
  .menu_lt {
    float: left;
    width: calc(100% - 20.3125rem - .625rem);
    padding: .625rem;
  }

  .wk_box {
    width: 100%;
  }

  .wk_video {
    float: left;
    width: 100%;
    height: 33.125rem;
    background: #000;
  }

  .wk_nodata {
    float: left;
    width: 100%;
    height: 33.125rem;
    background: #000;
    color: #999999;
    font-size: .875rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-flow: column;
  }

  .wk_nodata img {
    width: 7.4375rem;
    height: 8rem;
    margin: 0 0 .625rem;
  }

  /* 点赞 */
  .wk_opt {
    float: left;
    width: 100%;
    height: 3.1875rem;
    box-sizing: border-box;
    padding: .9375rem 1.875rem;
    background: #1f1e1d;
  }

  .wk_thumbs {
    float: left;
    line-height: 1.25rem;
    color: #fff;
    font-size: .875rem;
  }

  .wk_thumbs div {
    float: left;
  }

  .wk_thumbs img {
    float: left;
    width: 1.25rem;
    height: 1.25rem;
    margin: 0 .625rem 0 0;
  }

  .wk_thumbs img:hover {
    cursor: pointer;
  }

  .wk_thumbs.active {
    color: #FFC40D;
  }

  .wk_thumbs img:nth-child(1),
  .wk_thumbs.active img:nth-child(2) {
    display: inline-block;
  }

  .wk_thumbs img:nth-child(2),
  .wk_thumbs.active img:nth-child(1) {
    display: none;
  }

  /* 视频名称 */
  .wk_names {
    float: right;
    width: 48.125rem;
    height: 1.1875rem;
    display: flex;
    justify-content: flex-end;
  }

  .wk_name {
    display: flex;
    align-items: center;
    margin: 0 1.875rem 0 0;
  }

  .wk_name:last-child {
    margin: 0;
  }

  .wk_name div {
    max-width: 21.25rem;
    line-height: 1.1875rem;
    color: #ffffff;
    font-size: .875rem;
    text-decoration: underline;
  }

  .wk_name div:hover {
    cursor: pointer;
  }

  .wk_name img {
    width: 1rem;
    height: 1rem;
    margin: 0 .375rem 0 0;
    display: none;
  }

  .wk_name.active img {
    display: inline-block;
  }

  /* 更多课程 */
  .vid_more {
    float: left;
    width: 100%;
  }

  .vid_tit {
    float: left;
    width: 100%;
    color: #2a2b2a;
    font-size: 1rem;
    font-weight: 700;
    line-height: 1.3125rem;
    padding: 1.875rem 0 .625rem;
    display: flex;
    align-items: center;
  }

  .vid_tit img {
    width: 1.125rem;
    height: 1.125rem;
    margin: 0 .5rem 0 0;
  }

  .vid_ul {
    float: left;
    width: 100%;
    height: 10.75rem;
    box-sizing: border-box;
  }

  .vid_li {
    float: left;
    width: 100%;
    /* margin: 0 .625rem 0 0; */
  }

  .vid_li:hover {
    cursor: pointer;
  }

  .vid_img,
  .vid_img img {
    float: left;
    width: 100%;
    height: 8.5625rem;
    border-radius: .25rem;
    box-sizing: border-box;
    overflow: hidden;
  }

  .vid_img img {
    border-radius: 0;
  }

  .vid_li.active .vid_img {
    border: .125rem solid #5a85ec;
  }

  .vid_name {
    float: left;
    width: 100%;
    line-height: 1.1875rem;
    color: #2A2B2A;
    font-size: .875rem;
    margin: .375rem 0 0;
  }

  .vid_li.active .vid_name {
    color: #5A85EC;
  }

  div.vid_status {
    float: right;
    width: 3.75rem;
    line-height: 1.5rem;
    border-radius: 0 0 0 .25rem;
    text-align: center;
    font-size: .75rem;
    color: #fff;
    position: relative;
    z-index: 2;
    margin: -8.5625rem 0 0;
  }

  .vid_status.status0 {
    background: #999;
  }

  .vid_status.status1 {
    background: linear-gradient(150.8deg, #f6d22b 0%, #f29500 100%);
  }

  .vid_status.status2 {
    background: linear-gradient(166.7deg, #08d8b8 0%, #00b392 100%);
  }

  /* 轮播 */
  .swiper-slide {
    width: 14.25rem;
    height: 100%;
  }

  .pagination {
    position: absolute;
    z-index: 20;
    left: .625rem;
    bottom: .625rem;
  }

  .swiper-pagination-switch {
    display: inline-block;
    width: .5rem;
    height: .5rem;
    border-radius: .5rem;
    background: #222;
    margin-right: .5rem;
    opacity: 0.8;
    border: .0625rem solid #fff;
    cursor: pointer;
  }

  .swiper-visible-switch {
    background: #aaa;
  }

  .swiper-active-switch {
    background: #fff;
  }

  .turn {
    float: left;
    width: 2rem;
    height: 6.0625rem;
    border-radius: 1.1875rem;
    background: #ffffff;
    box-shadow: 0 0 .625rem 0 #0000000d;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 2;
    margin: -9.5rem 0 0 -1rem;
  }

  .turn.rt {
    float: right;
    margin: -9.5rem -1rem 0 0;
  }

  .turn.swiper-button-disabled {
    display: none;
    pointer-events: none;
  }

  .turn:hover {
    cursor: pointer;
  }

  .turn img {
    width: .3125rem;
    height: .75rem;
  }

  .turn img:nth-child(1),
  .turn:hover img:nth-child(2) {
    display: inline-block;
  }

  .turn img:nth-child(2),
  .turn:hover img:nth-child(1) {
    display: none;
  }

  /* 知识点 */
  .iframe {
    border: 0;
    width: 100%;
    height: 100vh;
  }

  .wkinfo {
    float: left;
    width: 100%;
    color: #2a2b2a;
    font-size: .875rem;
    font-weight: 400;
    line-height: 1.625rem;
    box-sizing: border-box;
    padding: .625rem 1.25rem;
  }

  /* 列表 */
  .menu_rt {
    float: right;
    width: 20.3125rem;
    border: .0625rem solid #eaeaea;
    background: #ffffff;
    height: calc(100vh - 7.4375rem);
    display: flex;
    flex-flow: column;
  }

  .menu_rt div {
    float: left;
  }

  .pt_top {
    width: 19.6875rem;
    height: 4.6875rem;
    margin: -1.25rem 0 1.25rem .625rem;
    position: relative;
  }

  .pt_ul {
    flex: 1;
    width: 100%;
    overflow-y: auto;
    box-sizing: border-box;
    padding: .625rem 1.25rem 0;
  }

  .pt_li {
    width: 100%;
    height: 3.0625rem;
    border-radius: .25rem;
    border: .0625rem solid #eaeaea;
    background: #ffffff;
    color: #2a2b2a;
    font-size: 1rem;
    margin: 0 0 1.25rem;
    padding: .875rem 1rem;
    box-sizing: border-box;
    display: flex;
    align-items: center;
  }

  .pt_li div {
    flex: 1;
  }

  .pt_li:hover {
    cursor: pointer;
    background: #f5f5f5;
  }

  .pt_li.active {
    color: #5A85EC;
    border-color: #5A85EC;
  }

  .pt_li img {
    width: 1rem;
    height: 1rem;
    margin: 0 .375rem 0 -0.375rem;
    display: none;
  }

  .pt_li.active img {
    display: inline-block;
  }

  .pt_btn {
    width: calc(100% - 2.5rem);
    line-height: 2.75rem;
    border-radius: .25rem;
    background: #00c9a3;
    margin: .625rem 1.25rem 2.5rem;
    color: #ffffff;
    font-size: 1rem;
    font-weight: 700;
    text-align: center;
  }

  .pt_btn:hover {
    cursor: pointer;
  }

</style>
