{
  "compilerOptions": {
    "outDir": "./dist",
    "target": "esnext",
    "useDefineForClassFields": true,
    "module": "esnext",
    "moduleResolution": "node",
    "strict": true,
    "noImplicitAny": false,
    "jsx": "preserve",
    "importHelpers": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "sourceMap": false, // 关闭 TS 的 Sourcemap
    "inlineSourceMap": false,
    "inlineSources": false,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "esModuleInterop": true,
    "lib": ["esnext", "dom"],
    "skipLibCheck": true,
    "types": [
      "@types/node", // 修正为 @types/node
      "vite/client",
      "element-plus/global",
      "unplugin-vue-define-options/macros-global" // 确保已安装
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    },
    "ignoreDeprecations": "5.0",
    "noUncheckedIndexedAccess": true
  },
  "vueCompilerOptions": {
    "target": 3,
    "plugins": ["@vue-macros/volar/define-options"]
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "tests/**/*.ts",
    "types/**/*.d.ts",
    "vite.config.ts",
    "@/router/index.ts",
    "@/utils/**/*.ts",
    "@/utils/**/*.js"
  ],
  "exclude": ["node_modules", "dist"]
}