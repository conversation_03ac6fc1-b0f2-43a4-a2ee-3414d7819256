<template>
    <div class="subject-container" :class="isRow?'subject-container-row':''">
        <div class="subject-item" v-for="(item, index) in subjectList" :class="item.selected?'selected':''" :key=index @click="setSubject(item)">
            <img :src="getUrl(item)" alt="" />
            <span class="subject-item-text">{{item.subject}}</span>
        </div>
    </div>
</template>
  
<script lang="ts" setup>
import { ref, watch } from 'vue'
import { useUserStore } from "@/store/modules/user"
const userStore = useUserStore()
const props = defineProps({
  selected: {
    type: String,
    default: () => "math"
  },
  isRow: {
    type: Boolean,
    default: () => false
  },
})
const subjectList:any = ref([])
const emit = defineEmits(['setSubject'])
watch(
    () => props.selected,
    (newSelected) => {
        if (newSelected.includes('3')) {
            subjectList.value = [
                { id: 10, desc: "math3", subject: "数学", icon: "math", selected: false },
                { id: 11, desc: "chinese3", subject: "语文", icon: "chinese", selected: false },
                { id: 12, desc: "english3", subject: "英语", icon: "english", selected: false },
                { id: 14, desc: "science3", subject: "科学", icon: "science", selected: false }
            ]
        } else if (newSelected.includes('2')) {
            subjectList.value = [
                { id: 30, desc: "math2", subject: "数学", icon: "math", selected: false },
                { id: 31, desc: "physics2", subject: "物理", icon: "physics", selected: false },
                { id: 32, desc: "chemistry2", subject: "化学", icon: "chemistry", selected: false },
                { id: 33, desc: "bio2", subject: "生物", icon: "bio", selected: false },
                { id: 35, desc: "geography2", subject: "地理", icon: "geography", selected: false },
                { id: 36, desc: "chinese2", subject: "语文", icon: "chinese", selected: false },
                { id: 37, desc: "english2", subject: "英语", icon: "english", selected: false },
                { id: 38, desc: "politics2", subject: "政治", icon: "politics", selected: false },
                { id: 39, desc: "history2", subject: "历史", icon: "history", selected: false }
            ]
        } else {
            subjectList.value = [
                { id: 20, desc: "math", subject: "数学", icon: "math", selected: false },
                { id: 21, desc: "physics", subject: "物理", icon: "physics", selected: false },
                { id: 22, desc: "chemistry", subject: "化学", icon: "chemistry", selected: false },
                { id: 23, desc: "bio", subject: "生物", icon: "bio", selected: false },
                { id: 14, desc: "science", subject: "科学", icon: "science", selected: false },
                { id: 25, desc: "geography", subject: "地理", icon: "geography", selected: false },
                { id: 26, desc: "chinese", subject: "语文", icon: "chinese", selected: false },
                { id: 27, desc: "english", subject: "英语", icon: "english", selected: false },
                { id: 28, desc: "politics", subject: "道法", icon: "politics", selected: false },
                { id: 29, desc: "history", subject: "历史", icon: "history", selected: false }
            ]
        }
        subjectList.value.forEach(item => {
            if(item.desc == newSelected) {
                item.selected = true
            } else {
                item.selected = false
            }
        })
        userStore.setSubjectList(subjectList.value)
        
        //匹配学生学科
        let versions=userStore.learnNow.versions,list:any=[]
        if(versions?.length){
          for(let i of versions){
            for(let n of subjectList.value){
              if(i.subject == n.desc){
                list.push(n)
              }
            }
          }
          subjectList.value=list
        }
    },
    { immediate: true }
)

const getUrl = (item: any) => {
    let url = item.icon
    if (item.selected) {
        url = item.icon + '_selected'
    }
    return new URL(`../../../assets/img/percision/${url}.png`, import.meta.url).href //静态资源引入为url，相当于require()
}
const setSubject = (data: any) => {
    userStore.setSubjectObj(data.desc, data.id)
    emit('setSubject', data)
}

</script>
<style scoped lang="scss">
.subject-container-row {
    flex-direction: row!important;
    overflow-x: auto!important;
    overflow-y: hidden!important;
    height: 3.375rem!important;
    width: 100%!important;
    border-radius: 0!important;
    .subject-item {
        margin-bottom: 0!important;
        margin-top: 0!important;
        &:not(:first-child) {
            margin-left: .875rem!important;
        }
    }
}
.subject-container {
    width: 9.875rem;
    height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-rotute-tab-height) - .625rem);
    border-radius: 1.25rem 1.25rem 0 0;
    background: #ffffff;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    .subject-item {
        width: 7.125rem;
        height: 3.375rem;
        line-height: 3.375rem;
        text-align: center;
        border-radius: .25rem;
        cursor: pointer;
        font-size: 1rem;
        color: #2a2b2a;
        display: flex;
        align-items: center;
        justify-content: center;
        &:hover {
            background: #f5f5f5;
        }
        &:not(:last-child) {
            margin-bottom: 1.4375rem;
        }
        &:first-child {
            margin-top: 1.25rem;
        }
        img {
            width: 1.375rem;
            height: 1.375rem;
            margin-right: .5rem;
        }
    }
    .selected {
        background: #f1f7f6;
        color: #009c7f;
    }
}
</style>