import { request } from "@/utils/axios"
const api = "/api"
const sdkapi = "/api/xiaoyeoo/sdkapi"

// 上传图片
export const uploadApi = (data: any) => {
  return request({
    url: `${sdkapi}/learn/user/file/upload`,
    method: "POST",
    data,
    headers: { "Content-Type": "multipart/form-data" }
  })
}

// 获取小优平板用户信息
export const getUserApi = () => {
  return request({
    url: `${api}/xiaoyeoo/user/getUser`,
    method: "GET",
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

// 获取该家长下的所有学生信息
export const userGetAllApi = () => {
  return request({
    // url: `${sdkapi}/learn/user/getAll`,//小优
    url: `${sdkapi}/learn/user/getLearnId`,//自习室
    method: "GET"
  })
}

// 添加学生用户信息
export const userAddApi = (data: any) => {
  return request({
    url: `${sdkapi}/learn/user/add`,
    method: "POST",
    data
  })
}

// 修改学生用户信息
export const userUpdateApi = (data: any) => {
  return request({
    url: `${sdkapi}/learn/user/update`,
    method: "POST",
    data
  })
}

// 修改默认学习用户
export const userDefaultApi = (data: any) => {
  return request({
    url: `${sdkapi}/learn/user/update/default`,
    method: "POST",
    data
  })
}

// 删除学习用户
export const userDeleteApi = (data: any) => {
  return request({
    url: `${sdkapi}/learn/user/batch/delete`,
    method: "POST",
    data
  })
}

// 修改家长昵称 小优平板修改用户名称 和头像接口
export const updateUserPadApi = (data: any) => {
  return request({
    url: `${api}/user/user/updateUserXiaoyeooPad`,
    method: "POST",
    data
  })
}

// 获取微信支付二维码
export const memberOrderByNativeApi = (data: any) => {
  return request({
    url: `${api}/purchase/order/saveMemberOrderByNative`,
    method: "POST",
    data
  })
}

// 获取套餐价格
export const goodsfindByIdApi = (data: any) => {
  return request({
    url: `${api}/purchase/goods/findById/${data}`,
    method: "GET"
  })
}

// 查询支付结果
export const getOrderByNativeApi = (data: any) => {
  return request({
    url: `${api}/purchase/order/getOrderByNative`,
    method: "POST",
    data
  })
}

// 使用兑换码
export const exchangeCodeApi = (data: any) => {
  return request({
    url: `${api}/purchase/vipRedemptionCode/useVipRedemptionCode`,
    method: "POST",
    data
  })
}

// 获取所有年级
export const gradeAllApi = (data: any) => {
  return request({
    url: `${sdkapi}/grade`,
    method: "POST",
    data
  })
}

// 根据学习用户年级查询教材版本
export const bookVersionApi = (data: any) => {
  return request({
    url: `${sdkapi}/book/version`,
    method: "GET",
    params: data,
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

// 获取学段学科教材-高中
export const getEditionListApi = (data: any) => {
  return request({
    url: `${sdkapi}/book/getSectionEditionList`,
    method: "GET",
    params: data,
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

// 获取学段学科教材版本-高中
export const getSectionEditionBookListApi = (data: any) => {
  return request({
    url: `${sdkapi}/book/getSectionEditionBookList`,
    method: "GET",
    params: data,
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

// 根据IP地址定位，返回教材版本
export const getEditionByIpApi = (data: any) => {
  return request({
    url: `${sdkapi}/book/getEditionByIp`,
    method: "GET",
    params: data,
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

// 获取省市地区
export const getRegionsTreeApi = () => {
  return request({
    url: `${sdkapi}/report/getRegionsTree`,
    method: "GET",
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

// 查询我的积分
export const integralInfoApi = (data: any) => {
  return request({
    url: `${sdkapi}/learnUserIntegralInfo`,
    method: "GET",
    params: data,
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

// 查询用户是否签到
export const everydaySignInBoolApi = (data: any) => {
  return request({
    url: `${sdkapi}/everydaySignInBool`,
    method: "GET",
    params: data,
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

// 每日签到
export const everydaySignInApi = (data: any) => {
  return request({
    url: `${sdkapi}/everydaySignIn`,
    method: "GET",
    params: data,
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

// 积分列表
export const integralListApi = (data: any) => {
  return request({
    url: `${sdkapi}/learnUserIntegralList`,
    method: "GET",
    params: data,
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

// 添加积分
export const addIntegralApi = (data: any) => {
  return request({
    url: `${sdkapi}/addLearnUserIntegral`,
    method: "POST",
    data
  })
}

// 判断用户是否为会员
export const userVipInfoApi = (data: any) => {
  return request({
    url: `${sdkapi}/learn/user/getLearnId`,//自习室
    // url: `${api}/user/user/member/center/getUserMemberCenter`,//小优
    method: "GET",
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
    data
  })
}

// 分享教材级学期报告(生成小程序码)
export const createAppletCodeApi = (data: any) => {
  return request({
    url: `${sdkapi}/learn/user/createAppletCode_Share`,
    method: "GET",
    params: data,
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
    responseType: 'blob'
  })
}

// 记录学习时长
export const setStudyTimeApi = (data: any) => {
  return request({
    url: `${sdkapi}/learn/user/setStudyTime`,
    method: "POST",
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
    data
  })
}

// 记录每日查看视频数量
export const analyseVideoNumApi = (data: any) => {
  return request({
    url: `${sdkapi}/learn/user/setStudyVideoNum`,
    method: "POST",
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
    data
  })
}
