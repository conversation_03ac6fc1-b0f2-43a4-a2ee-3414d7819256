<template>
  <el-dialog
    :close-on-click-modal="false"
    v-model="state.dialogVisible"
    title="下载试卷"
    width="550"
    :center="true"
    :destroy-on-close="true"
    @close="handlerReset()"
  >
    <div class="warp">
      <el-form :model="formData" label-width="6.25rem" ref="baseForm">
        <el-form-item label="试卷：" class="paper-label">
          <span class="paper-txt">{{ paperDetail.title }}</span>
        </el-form-item>
        <el-form-item label="试卷类型：" class="paper-type-label" prop="">
          <el-radio-group v-model="formData.type" class="syu">
            <el-radio :value="0">无答案试卷 </el-radio>
            <el-radio :value="2">试卷+答案</el-radio>
            <el-radio :value="3">试卷+答案+解析</el-radio>
          </el-radio-group>
        </el-form-item>
        <div class="mt-2" style="text-align: center">
          <el-button @click="handlerReset" style="width: 5.5rem">取消</el-button>
          <el-button type="primary" @click="onSubmit" v-loading.fullscreen="state.loading">立即下载</el-button>
        </div>
      </el-form>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, defineProps, defineEmits, watch } from "vue"

import { useUserStore } from "@/store/modules/user"
import { downloadReportApi } from "@/api/report"
import { storeToRefs } from 'pinia'

defineOptions({
  name: "downloadTrestDialog"
})

const defaultForm = (): any => {
  return {
    paperId: "",
    type: 1
  }
}

const prop: any = defineProps({
  paperDetail: {
    type: Object,
    default: () => {
      return {
        title: "",
        id: ""
      }
    }
  },
  dialogVisible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(["update:dialogVisible"])
const userStore = useUserStore()
const { subjectObj, learnNow } = storeToRefs(userStore)

const state: any = reactive({
  dialogVisible: false,
  count: 0,
  loading: false
})
const formData = reactive<any>(defaultForm())

const handlerReset = () => {
  state.dialogVisible = false
  emit("update:dialogVisible", state.dialogVisible)
  formDataReset()
}

const formDataReset = () => {
  Object.assign(formData, defaultForm())
}

const baseForm = ref<HTMLElement | null | any>(null)
// 提交操作
const onSubmit = async () => {
  state.loading = true
  try {
    await baseForm.value.formRef?.validate()
    const res = (await downloadReportApi({ id: prop.paperDetail.id, method: formData.type, subject: subjectObj.value.subject })) as any
    state.loading = false
    if (res.data) {
      const link = document.createElement("a")
      const body: any = document.querySelector("body")
      link.href = res.data
      link.style.display = "none"
      body.appendChild(link)
      link.click()
      body.removeChild(link)
      state.dialogVisible = false
    }
  } catch (err) {
    console.log(err)
    state.dialogVisible = false
    state.loading = false
  }
}
// 显示弹窗
const dialogShow = async () => {
  state.dialogVisible = true
}

defineExpose({
  dialogShow
})
</script>

<style lang="scss" scoped>
.paper-txt {
  display: inline;
  color: rgb(0, 0, 0);
  font-size: .875rem;
  font-weight: 600;
}
:deep(.el-radio.is-checked .el-radio__label) {
  color: rgb(0, 0, 0);
  font-size: .875rem;
  font-weight: 600;
}
:deep(.el-radio__label) {
  font-size: .875rem;
  font-weight: 400;
  color: rgb(77, 77, 77);
}
:deep(.el-form-item__label) {
  color: rgb(77, 77, 77);
  font-size: .875rem;
}
.paper-type-label {
  display: flex;
}
.syu {
  display: grid;
}
</style>
