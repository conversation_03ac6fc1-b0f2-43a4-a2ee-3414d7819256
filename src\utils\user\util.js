// import pinyin from 'pinyin.js'

// json对象根据某个属性去重
export function unRepeat(arr, param) {
  const res = new Map()
  return arr.filter((arr) => !res.has(arr[param]) && res.set(arr[param], 1))
}

//获取日期,前后第几天、当前日期带时间
export function getDay(num, istime) {
  const t = new Date()
  t.setDate(t.getDate() + num) //获取num天后的日期
  const y = t.getFullYear()
  const m = ('0' + (t.getMonth() + 1)).slice(-2),
    d = ('0' + t.getDate()).slice(-2),
    h = ('0' + t.getHours()).slice(-2),
    m2 = ('0' + t.getMinutes()).slice(-2),
    s = ('0' + t.getSeconds()).slice(-2)
  if (istime == 1) {
    return y + '-' + m + '-' + d + ' ' + h + ':' + m2 + ':' + s
  } else if (istime == 2) {
    return y + m + d
  } else {
    return y + '-' + m + '-' + d
  }
}

// 时间格式化
export function formatTime(date) {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()
  return `${[year, month, day].map(formatNumber).join('/')} ${[hour, minute, second].map(formatNumber).join(':')}`
}

//时间戳转日期格式 13位时间戳转日期不够补位
export function formatDate2(time) {
  const d = new Date(time)
  const n = new Date(d.getTime())
  let hour = n.getHours()
  let mon = n.getMonth() + 1
  let day = n.getDate()
  let min = n.getMinutes()
  let s = n.getSeconds()
  if (day < 10) {
    day = '0' + day
  }
  if (mon < 10) {
    mon = '0' + mon
  }
  if (hour < 10) {
    hour = '0' + hour
  }
  if (min < 10) {
    min = '0' + min
  }
  if (s < 10) {
    s = '0' + s
  }
  //时间
  const now = n.getFullYear() + '-' + mon + '-' + day + ' ' + hour + ':' + min + ':' + s
  return now
}
//获取当前时间日期格式
export function formatNowDate(date) {
  const padStart = (value) => (value.toString().length === 1 ? '0' + value : value)
  const year = date.getFullYear()
  const month = padStart(date.getMonth() + 1)
  const day = padStart(date.getDate())
  const hours = padStart(date.getHours())
  const minutes = padStart(date.getMinutes())
  const seconds = padStart(date.getSeconds())
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

//数字格式化
export function formatNumber(n) {
  n = n.toString()
  return n[1] ? n : `0${n}`
}

/**字符串是否为空或者全部都是空格 */
export function isNull(str) {
  if (str == '') return true
  var regu = '^[ ]+$'
  var re = new RegExp(regu)
  return re.test(str)
}

// 深拷贝
export function deepClone(obj) {
  if (typeof obj !== 'object' || obj === null) {
    return obj
  }
  const result = Array.isArray(obj) ? [] : {}
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      result[key] = deepClone(obj[key])
    }
  }
  return result
}

// 时分秒显示
export function showTime(nowTime) {
  const remianAllSeconds = Math.floor(nowTime / 1000) //剩余总秒数
  // let remainDays = Math.floor(remianAllSeconds / (60 * 60 * 24));//剩余天数
  const remainHours = Math.floor((remianAllSeconds / (60 * 60)) % 24) //剩余小时数
  const remainMinutes = Math.floor((remianAllSeconds / 60) % 60) //剩余分钟数
  const remainSeconds = Math.floor(remianAllSeconds % 60) //剩余秒数
  if (remainHours) {
    const str = `${remainHours > 9 ? remainHours : '0' + remainHours}:${
      remainMinutes > 9 ? remainMinutes : '0' + remainMinutes
    }:${remainSeconds > 9 ? remainSeconds : '0' + remainSeconds}`
    return str
  } else {
    const str = `${remainMinutes > 9 ? remainMinutes : '0' + remainMinutes}:${
      remainSeconds > 9 ? remainSeconds : '0' + remainSeconds
    }`
    return str
  }
}

// 计算字符长度将中文汉字转2个字符
export function getStrlen(str) {
  let len = 0
  if (str) {
    len = str.replace(/[^u4e00-u9fa5]/g, '22').length
  }
  return len
}

//兼容小学数学一行多道题改1行显示
export function isMath10(subject, content) {
  if (
    subject == 10
    // 统一都换行 &&(content.indexOf('直接写出得数。') > -1 || content.indexOf('直接写得数。') > -1 || content.indexOf('口算。') > -1)
  ) {
    return true
  }
  return false
}

//富文本处理
export function ReplaceMathString(str) {
  var rpRex = ['div', 'table', 'td', 'span', 'br', 'tr', 'td', 'h2', 'fieldset', 'del', 'sup', 'sub'],
    str2 = str
  //添加样式//<[^>]+>
  for (var j = 0; j < rpRex.length; j++) {
    var regex = new RegExp('<' + rpRex[j] + '.*?>', 'ig')
    if (!str2) return
    var arry = str2.match(regex) //match返回数组
    if (arry == null) {
      continue
    }
    var arrydis = []
    // //去重+去有class的
    for (var i = 0; i < arry.length; i++) {
      let arrI = arry[i]
      if (arrydis.indexOf(arrI) == -1 && arrI.indexOf('class=') == -1) {
        //indexOf返回数组中某个指定的元素位置
        arrydis.push(arrI)
      }
    }
    var regex2 = new RegExp('<' + rpRex[j], 'ig')
    for (var x = 0; x < arrydis.length; x++) {
      var replacedstr = arrydis[x].replace(regex2, '<' + rpRex[j] + ' class="MathJye_' + rpRex[j] + '"')
      var regexStr = new RegExp(arrydis[x], 'ig')
      str2 = str2.replace(regexStr, replacedstr)
    }
  }
  //为table外层加div
  var regextableT = new RegExp('<table', 'ig')
  str2 = str2.replace(regextableT, '<div class="mathJye_table_cont"><table ')
  var regextableW = new RegExp('/table>', 'ig')
  str2 = str2.replace(regextableW, '/table></div>')
  //特殊处理为img加class
  var regexImg = new RegExp('<img', 'ig')
  str2 = str2.replace(regexImg, '<img class="MathJye_img"')
  //去除特殊字符
  // str = str.replace(new RegExp('□', 'ig'), '')
  str = str.replace(new RegExp('', 'ig'), '')
  //去除箭头空
  var regexSpace = new RegExp('↵', 'ig')
  str2 = str2.replace(regexSpace, ' ')
  //处理http图片
  str2 = str2.replace(/http:\/\//g, 'https://')
  //处理div可编辑
  str2 = str2.replace(/contenteditable="true"/g, '')
  return str2
}

//提取html标签(英文选项对齐)
export function getStr(html_str) {
  //没表格返回原始值
  if (html_str.indexOf('composition2') == -1) {
    return html_str
  }
  const htmlOld = html_str,
    tdArr = [
      []
    ]
  let istr = 0,
    trx = 0
  // 裁剪选项table
  let str = html_str.split('<table class="composition2"')
  str = str[str.length - 1]
  //换行分组
  str = str.split(/[\n]/g)
  //去除空白
  for (let i = 0; i < str.length; i++) {
    const stri = str[i]
    //判断tr开始
    const reg = /<tr[^>]*>/g
    if (reg.test(stri)) {
      istr = 1
    }
    //添加td标签数组
    if (istr === 1) {
      let tdstr = str[i + 1]
      //判断tr结束
      const reg2 = /<\/tr>/g
      if (reg2.test(tdstr)) {
        trx++
        if ((istr = 1)) {
          tdArr[trx] = []
        }
        istr = 2
      } else {
        //添加td标签去空白,去掉标签
        const reg3 = new RegExp('<[^<>]+>', 'g')
        tdstr = tdstr.replace(/(^\s*)|(\s*$)/g, '').replace(reg3, '')
        tdArr[trx].push(tdstr)
      }
    }
  }
  const maxArr = [],
    optArr = []
  for (const i of tdArr) {
    if (i.length) {
      const arr = JSON.parse(JSON.stringify(i))
      //取最长字符串
      arr.sort((a, b) => {
        return b.length - a.length
      })
      // i.len = arr[0].length
      maxArr.push(arr[0].length)
    }
  }
  //取最长字符串
  maxArr.sort((a, b) => {
    return b - a
  })

  //判断几个选项
  //5个数组，第一个是（1）时处理
  if (tdArr[0].length == 5 && tdArr[0][0].length == 3) {
    const xyopt = 'xyopt5'
    //将第3个选项加上序号
    for (const i of tdArr) {
      if (i.length) {
        optArr.push([`<span class="opac">${i[0]}</span>`, i[3]])
      }
    }
    let newHtml = ReplaceMathString(htmlOld).replace('class="composition2"', `class="${xyopt} composition2"`)
    for (const y of optArr) {
      newHtml = newHtml.replace(y[1], y[0] + y[1])
    }
    return newHtml
  } else if (tdArr[0].length == 4) {
    let xyopt = 'xyopt'
    if (maxArr[0] < 11) {
      xyopt += '1'
      //2行显示
    } else {
      xyopt += '2'
      //将第3个选项加上序号
      for (const i of tdArr) {
        if (i.length) {
          optArr.push([`<span class="opac">${i[0].split('A')[0]}</span>`, i[2]])
        }
      }
    }
    let newHtml = htmlOld.replace('class="composition2"', `class="${xyopt} composition2"`)
    for (const y of optArr) {
      newHtml = newHtml.replace(y[1], y[0] + y[1])
    }
    return newHtml
  }
  return htmlOld
}

//匹配img标签
export function getImgArr(html) {
  const imgReg = /<img.*?(?:>|\/>)/gi //匹配img标签
  const srcReg = /src=[\'\"]?([^\'\"]*)[\'\"]?/i // 匹配src
  let arr = []
  if (html) {
    //筛选所有img
    arr = html.match(imgReg)
  }
  const imgArr = []
  if (!arr) {
    return []
  }
  for (let i = 0; i < arr.length; i++) {
    const src = arr[i].match(srcReg)[1]
    //去掉小图base64
    if (src.indexOf('data:image/') == -1) {
      imgArr.push(src)
    }
  }
  return imgArr
}

//批量获取图片宽高
export function getImageSize(html) {
  return new Promise(function(resolve) {
    //先直接处理html字符串
    resolve(ReplaceMathString(html))
    //先不改
    // const imgs = getImgArr(html)
    //   // bodyWdt = 1110,
    //   // imgInfo = []
    // if (!imgs.length) {
    //   resolve(html)
    // }
    // let i = 0
    // getImgs(imgs[i])
    // function getImgs() {
    //   //待改
    //   // wx.getImageInfo({
    //   //   src: url,
    //   //   success: res => {
    //   //     const width = res.width,
    //   //       height = res.height
    //   //     //判断图片宽度占屏幕75%，要一行显示
    //   //     let full = 0
    //   //     if (width / bodyWdt > 0.75) {
    //   //       full = 1
    //   //     } else if (width / bodyWdt >= 0.5) {
    //   //       full = 2
    //   //     }
    //   //     imgInfo.push({
    //   //       img: url,
    //   //       width,
    //   //       height,
    //   //       full
    //   //     })
    //   //     i++
    //   //     if (i < imgs.length) {
    //   //       getImgs(imgs[i])
    //   //     } else {
    //   //       let richtxt = ReplaceMathString(html)
    //   //       for (const i of imgInfo) {
    //   //         if (i.full) {
    //   //           //追加图片标记
    //   //           richtxt = richtxt.replace(i.img + '"', i.img + `" data-full="${i.full}"`)
    //   //           //预览插件兼容
    //   //           richtxt = richtxt.replace('class="', `class="imgfull${i.full} `)
    //   //         }
    //   //       }
    //   //       resolve(richtxt)
    //   //     }
    //   //   },
    //   //   error: err => {
    //   //     reject(new Error(err))
    //   //   }
    //   // })
    // }
  })
}


//替换https
export function replaceHttp(url) {
  if (url) {
    return url.replace(/http:\/\//g, 'https://')
  } else {
    return url
  }
}