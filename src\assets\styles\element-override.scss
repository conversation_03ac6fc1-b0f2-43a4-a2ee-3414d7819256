/** 自定义 Element Plus 样式 */
$black: #1c1c28;
$green: #009c7f;
$green2: rgb(0, 201, 163);
$gray: #555770;
$maingreen: #00c9a3;
/* 滚动条的样式 */
body::-webkit-scrollbar {
  width: .4375rem;
}
body::-webkit-scrollbar-thumb {
  background: rgba(144, 147, 153, 0.7);
  border-radius: 2rem;
}
body::-webkit-scrollbar-track {
  background: #f0f0f0;
}
/** 菜单导航logo */
.menu-title {
  .menu-title__img {
    // width: 2.0625rem;
    // height: 2.0625rem;
    margin-left: 1.125rem !important;
  }
  .menu-title-text {
    padding: 0 1.25rem 0 1rem !important;
  }
  .menu-title-text__span {
    &.upper {
      font-weight: bold !important;
      font-size: .875rem !important;
      height: 1.375rem !important;
    }
    &.down {
      font-size: .5625rem !important;
      color: $gray !important;
    }
  }
}
/** 搜索表单 */
.el-card {
  border: 0;
  box-shadow: none;
  border-radius: 0;
  .el-select .el-input .el-select__caret {
    color: #666;
  }
}
.precision-search-wrapper {
  .el-card__body {
    padding-left: 0;
  }
}
.precision-search-wrapper,
.precision-product-add {
  .el-card__body {
    border-radius: .25rem;
    background: #fff;
    padding-bottom: 0;
  }
  .el-input__inner,
  .el-range-input {
    color: $black;
  }
  .el-input__inner::placeholder,
  .el-range-input::placeholder {
    color: #888;
  }
  .el-form-item__label {
    height: 2rem;
    line-height: 2rem;
  }
  .el-input__wrapper {
    height: 1.875rem;
    border-radius: .4375rem;
    box-shadow: 0 0 0 .0625rem rgb(153, 153, 153) inset;
  }
  .el-form-item,
  .asterisk-left {
    margin-bottom: 0 !important;
  }
  .el-col {
    float: left;
    height: 1.875rem;
    margin-bottom: 1.25rem;
  }
}

//时间控件
.el-date-range-picker {
  //开始日期选中
  td.end-date .el-date-table-cell__text,
  td.start-date .el-date-table-cell__text {
    background: $green;
    &:hover {
      color: #fff;
    }
  }
  //结束日期选中
  td.today .el-date-table-cell__text {
    color: $green;
  }
  .el-date-table-cell__text:hover {
    color: $green;
  }
  //时间下拉确定/取消
  .el-time-panel__btn {
    &.confirm {
      color: #fff;
      border-radius: .375rem;
      border-color: $green;
      background: $green;
    }
    &.cancel {
      color: $green;
      background: none;
    }
  }
  //底部清空
  .is-text.el-picker-panel__link-btn {
    color: $green;
    background: none;
    border: none;
  }
  //底部确定
  .is-plain.el-picker-panel__link-btn {
    color: #fff;
    border-radius: .375rem;
    border-color: $green;
    background: $green;
  }
  //近1周近3个月
  .el-picker-panel__shortcut {
    &:hover {
      color: $green;
    }
  }
}

.el-time-panel__footer {
  .el-time-panel__btn.confirm {
    color: $green !important;
    border-color: $green !important;
  }
}

.el-time-panel {
  .el-time-panel__footer {
    .el-time-panel__btn.confirm {
      background: none !important;
    }
    .el-time-panel__btn.cancel {
      color: #000 !important;
    }
  }
}

// label
.el-form-item__label {
  color: $black;
  font-size: .75rem;
  font-weight: bold;
}

//选择下拉框
.el-select-dropdown {
  .el-select-dropdown__item.selected {
    color: $green !important;
  }
}

.el-select-dropdown.is-multiple .el-select-dropdown__item.selected::after {
  background-color: $green !important;
}

// 时间插件
.el-year-table td .cell:hover,
.el-year-table td.today .cell,
.el-year-table td.current:not(.disabled) .cell,
.el-month-table td .cell:hover,
.el-month-table td.today .cell,
.el-month-table td.current:not(.disabled) .cell,
.el-date-picker__header-label,
.available:hover {
  color: $green !important;
}
.el-date-table td.today .el-date-table-cell__text {
  color: $green;
}
//时分秒确定
.el-time-panel__btn.confirm {
  line-height: 1.5rem !important;
  color: #fff !important;
  font-weight: normal;
}

//按钮
.el-button {
  &.is-link span {
    color: $green;
  }
}

// 链接
.el-link {
  &.is-underline {
    .el-link__inner {
      color: $green;
    }
  }
}

// 文本域
.el-textarea {
  .el-textarea__inner {
    padding: .5rem .6875rem;
  }
  .el-input__count {
    bottom: .6875rem;
    background-color: transparent;
    z-index: 0;
  }
}

// tabs
.el-tabs {
  .el-tabs__active-bar {
    background: $green2;
  }
  .el-tabs__item.is-active,
  .el-tabs__item:hover {
    color: $green;
  }
  .el-tabs__nav-wrap::after {
    height: 0;
  }
}

//上传组件
.el-upload {
  border: 0;
}
//上传图片
.el-upload--picture-card {
  width: 6.25rem;
  height: 6.25rem;
  //del键删除
  .el-icon--close-tip {
    display: none !important;
  }
}
.el-upload-list--picture-card {
  .el-upload-list__item {
    width: 6.25rem;
    height: 6.25rem;
  }
}

//上传去动画
.el-upload-list__item {
  // transition: none !important;
  // 删除动画,上移动画
  &.el-list-leave-active.el-list-move.el-list-leave-to,
  &.focusing.el-list-leave-active.el-list-leave-to {
    transition-duration: 0s;
  }
}
//上传控件隐藏
.precision-hide-upload {
  .el-upload,
  .el-upload--picture-card {
    display: none !important;
  }
}

//复选框
.el-checkbox {
  .el-checkbox__inner {
    border-color: #666;
  }
  .el-checkbox__inner:hover {
    border-color: $green2;
  }
  &.is-checked {
    .el-checkbox__inner {
      border-color: $green2;
      background: $green2;
    }
    .el-checkbox__label {
      color: $green;
    }
  }
}

//radio单选
.el-radio {
  .el-radio__inner {
    border-color: #666;
    &:hover {
      border-color: $green2;
    }
  }
  &.is-checked {
    .el-radio__inner {
      border-color: $green2;
      background: #fff;
      &::after {
        width: .4375rem;
        height: .4375rem;
        background: $green2;
      }
    }
    .el-radio__label {
      color: $green;
    }
  }
}

// switch开关
.el-switch__core {
  background: #bfbfbf;
}
.el-switch.is-checked .el-switch__core {
  border-color: #00c9a3;
  background: #00c9a3;
}
// 标签
.el-tag {
  &.el-tag--danger {
    border-color: $gray;
    color: $gray;
    background: #f1f1f1;
  }
  &.el-tag--success {
    border-color: #00b12f;
    color: #00b12f;
  }
}

//表格上方按钮
.precision-toolbar-lt {
  .el-button {
    height: 2rem;
    border-radius: .375rem;
    border: .0625rem solid $green2;
    background: #fff;
    color: $green2;
    font-size: .75rem;
    &.is-disabled {
      border: .0625rem solid #c0c4cc;
      background: #fff;
      color: #c0c4cc;
    }
  }
  .el-button--primary {
    background: $green2;
    color: #fff;
  }
}
.precision-toolbar-rt {
  .el-button {
    height: 2rem;
    border-radius: .375rem;
    border: .0625rem solid $gray;
    background: #fff;
    color: $gray;
    font-size: .75rem;
  }
}
// 表格
.el-table {
  width: 100%;
  box-shadow: 0 0 0 .0625rem #e5f8f5;
  overflow-x: hidden;
  box-sizing: border-box;
  border-radius: .25rem;
  .cell {
    height: 40px;
    line-height: 40px;
  }
  // 表头
  th.el-table__cell {
    // background: var(--el-fill-color-light) !important;
    background: #e5f8f5 !important;
    color: #1c1c28ff;
    font-size: .875rem;
    text-align: center;
    height: 2.75rem;
    min-width: 3.4375rem;
    // border: .0625rem solid #88B5FF;
    .cell {
      min-width: 3.4375rem;
    }
  }
  th.is-left {
    text-align: left!important;
  }
  td.is-left .cell{
    text-align: left!important;
  }
  .el-table__header-wrapper .el-table-column--selection > .cell {
    justify-content: center;
  }
  // 单元格
  td.el-table__cell div {
    text-align: center;
    color: $black;
    font-size: .8125rem;
  }
}
.el-table--enable-row-transition .el-table__body td.el-table__cell,
td.el-table__cell {
  border-bottom-color: rgb(229, 248, 245) !important;
}
// 表格勾选-已选中项
.el-row.is-justify-space-between {
  width: 100%;
}

// 分页
.pagination {
  display: flex;
  justify-content: center;
  // 文字
  .el-pagination--small button,
  .el-pagination--small span:not([class*="suffix"]),
  .el-input__inner,
  .number {
    font-size: .875rem !important;
    color: $gray;
  }
  // 左右箭头
  .el-pagination .btn-next .el-icon,
  .el-pagination .btn-prev .el-icon {
    font-size: .875rem !important;
  }
  .el-pagination.is-background {
    .btn-next,
    .btn-next:disabled,
    .btn-prev,
    .btn-prev:disabled {
      background: #f1f6ff;
    }
  }
  .el-input__wrapper {
    background: #f1f6ff;
    box-shadow: none;
  }
  .el-select .el-input .el-select__caret {
    color: $gray;
  }
  .el-pagination {
    @media screen and (max-width: 48rem) {
      .el-pagination__total,
      .el-pagination__sizes,
      .el-pagination__jump,
      .btn-prev,
      .btn-next {
        display: none !important;
      }
    }
  }
}

//分页数字
.el-pager {
  li {
    background: #f1f6ff;
    color: $gray;
    font-size: 1rem;
    &.number:hover {
      color: var(--percision-tecah-main-color) !important;
    }
    &.is-active {
      background: var(--percision-tecah-main-color) !important;
      font-weight: normal;
      color: #fff !important;
      &:hover {
        color: #fff !important;
      }
    }
  }
}
.is-message-box {
  .el-message-box {
    min-width: 34.875rem;
    padding-bottom: 1.25rem !important;
  }
  .el-message-box-tips {
    color: #999999!important;
    font-size: 14px!important;
    font-weight: 400;
  }
  .el-message-box__title {
    color: #1c1c28;
    font-size: 1rem;
    font-weight: bold;
    text-align: center;
  }
  .el-message-box__header {
    border-bottom: 1px solid #eaeaea;
    background: #ffffff;
    position: relative;
    padding-right: 0;
  }
  .el-message-box__headerbtn {
    margin-top: -5px;
    &:hover svg {
      color: #00c9a3;
    }
  }
  .el-message-box__container {
    min-height: 12.5rem;
    align-items: baseline;
    padding-top: 1.875rem;
    box-sizing: border-box;
  }
  .el-message-box__message {
    p {
      font-size: 1rem;
      text-align: center;
      margin: .625rem 0;
    }
  }
  .el-button {
    border-radius: .375rem;
    border: .0625rem solid $green !important;
    color: $green !important;
    font-size: .875rem;
    min-width: 5.125rem;
    height: 2.1875rem;
    border-radius: .375rem;
    &:hover,
    &:focus {
      background: #fff;
    }

    &.el-button--primary {
      color: #fff !important;
      border: .0625rem solid $green2 !important;
      background: $green2 !important;
    }
  }
  .el-message-box__btns {
    display: flex;
    justify-content: center;
    button:nth-child(2) {
      margin-left: 1.25rem;
    }
    .el-button {
      width: 7.625rem;
      height: 2.375rem;
      border-radius: 1.1875rem;
      background: #f5f5f5;
      font-size: 16px;
      font-weight: 400;
      color: #666666!important;
      border: none!important;
    }
    .el-button--primary {
      color: #fff !important;
    }
  }
}

.el-button--primary {
  color: #fff !important;
  border: .0625rem solid $maingreen;
  background: $maingreen;
  &:hover {
    color: #fff !important;
    border: .0625rem solid $maingreen;
    background: $maingreen;
  }
  &:not(.is-link):focus {
    border: .0625rem solid $maingreen !important;
    color: #fff !important;
    background: $maingreen !important;
  }
}
.el-button {
  border: .0625rem solid $maingreen;
  color: $maingreen;
  &:hover {
    border: .0625rem solid $maingreen;
    color: $maingreen;
  }
  &:focus {
    border: .0625rem solid $maingreen;
    background: #fff;
    color: $maingreen;
  }
}

// 弹窗dialog样式
.el-dialog {
  overflow: hidden;
  border-radius: .25rem;
  .el-dialog__header {
    padding: 0;
    height: 1.875rem;
    font-size: 1rem;
    font-weight: 700;
    line-height: 2rem;
    width: 100%;
    background: #ffffff;
    box-sizing: border-box;
    margin-right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-bottom: 20px;
    border-bottom: 1px solid #EAEAEA;
    .el-dialog__headerbtn {
      height: 3rem;
      width: 3rem;
      top: 0;
      .el-dialog__close {
        width: 1.5625rem;
        height: 1.5625rem;
        svg {
          width: 1.5625rem;
          height: 1.5625rem;
        }
      }
      &:hover svg {
        color: $green;
      }
    }
  }
  label {
    color: $black;
    font-size: .875rem;
    font-weight: bold;
  }
  .el-input__wrapper {
    box-shadow: 0 0 0 .0625rem rgb(153, 153, 153) inset;
  }
  .el-input__inner,
  .el-range-input {
    color: $black;
    font-size: .875rem;
  }
  .el-input__inner::placeholder,
  .el-range-input::placeholder {
    color: #888;
  }

  //底部按钮
  .el-col-24 .el-button {
    min-width: 4.9375rem;
    height: 1.875rem;
  }
  .el-button + .el-button {
    margin-left: 1.875rem;
  }

  /* 弹窗浏览图片 */
  &.precision-imgview {
    .el-dialog__header {
      background: none;
    }
    .precision-imgbox {
      width: 100%;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      .precision-imglook {
        max-width: 68vh;
        max-height: 78vh;
        min-height: 25rem;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .el-image {
        background: #fff;
      }
      .el-image__error {
        background: #f5f7fa;
        min-width: 9.125rem;
        min-height: 9.125rem;
      }
    }
  }
}

//上传xls
.el-step__title.is-wait {
  .precision-upxls-h1 {
    color: $black !important;
  }
}
.precision-upxls {
  // height: 37.5rem;
  .precision-upxls-h1 {
    font-size: .875rem;
    font-weight: bold;
    line-height: 1.3125rem;
  }
  .precision-upxls-p {
    font-size: .75rem;
    line-height: 1.125rem;
    color: #8a8a8a;
    font-weight: normal;
  }
  .is-finish {
    .precision-upxls-h1,
    .precision-upxls-p {
      color: $green;
    }
  }
  .el-dialog__header {
    background: #f1f6ff;
  }
  .el-dialog__body {
    padding: 1.5rem 2.5rem 1.6875rem;
    // overflow-y: auto;
  }
  .el-steps {
    height: 4.0625rem;
  }
  .el-steps--simple {
    padding: .5rem 2.25rem;
    background: #fbfbfb;
    border-radius: .375rem;
  }
  .el-step__icon {
    display: none;
  }
  .el-step.is-simple:not(:last-of-type) .el-step__title {
    max-width: 8.4375rem;
  }
  //下载模板
  .precision-xls-down {
    width: 100%;
    height: 8rem;
    background: rgb(251, 251, 251);
    border-radius: .375rem;
    margin: 1rem 0 .75rem;
    box-sizing: border-box;
    padding: .75rem .75rem 1rem;
    &.precision-xls-down2 {
      height: 10.625rem;
    }
    .precision-xls-down-p {
      width: 100%;
      height: 1.5rem;
      line-height: 1.5rem;
      color: #000;
      font-size: .875rem;
      font-weight: bold;
    }
    .precision-xls-down-tip {
      width: 100%;
      height: 1.5rem;
      line-height: 1.5rem;
      color: #8a8a8a;
      font-size: .875rem;
      margin: .75rem 0 1.0625rem;
    }
    .precision-xls-down-btn,
    .precision-xls-error-btn {
      float: left;
      cursor: pointer;
      img {
        width: 1.5rem;
        height: 1.5rem;
        margin: 0 .5625rem 0 0;
        float: left;
      }
      span {
        width: 4rem;
        height: 1.5rem;
        line-height: 1.5rem;
        color: $green;
        font-size: .875rem;
        font-weight: bold;
        text-align: center;
      }
    }
    .precision-xls-error-btn {
      margin: 0rem 0 0 10.625rem;
      position: relative;
      z-index: 8;
    }
    // 上传文件
    .precision-xls-upload {
      width: 100%;
      float: left;
      margin: -1.875rem 0 0;
      .el-button {
        opacity: 0;
        height: 2rem;
      }
      .el-upload-list {
        width: 50%;
        .el-upload-list__item .el-icon--upload-success {
          color: $green;
        }
      }
      // .el-icon.el-icon--document {
      //   background: url(@/assets/img/layout/word.png);
      //   background-size: 100% 100%;
      //   width: 1.5rem;
      //   height: 1.5rem;
      //   svg {
      //     display: none;
      //   }
      // }
      .el-upload-list__item-file-name {
        color: #8a8a8a;
        font-size: .875rem;
      }
      // .el-icon--circle-check {
      //   background: url(@/assets/img/layout/check.png);
      //   background-size: 100% 100%;
      //   width: .75rem;
      //   height: .75rem;
      //   // display: none !important;
      //   svg {
      //     display: none;
      //   }
      // }
      .el-upload-list__item-info {
        position: relative;
        left: -0.5rem;
      }
    }
  }
}

// 添加、编辑产品
.precision-product-add {
  border: 0;
  border-radius: .3125rem;
  overflow: hidden;
  .el-col {
    height: auto;
  }
  .el-tabs {
    border: 0;
    .el-tabs__header {
      border: 0;
      background: none;
      padding-bottom: .75rem;
    }
    .el-tabs__item {
      border: 0;
      color: #999;
      background: #f6f6f6;
      font-size: .875rem;
      width: 8.125rem;
      height: 2.1875rem;
      margin-top: 0;
      font-weight: bold;
      &.is-active {
        border-radius: .25rem;
        background: #dcf1ff;
        color: $green;
      }
    }
    .el-tabs__content {
      padding-left: 0;
      padding-right: 0;
    }
  }
  .el-tabs--border-card > .el-tabs__header {
    .el-tabs__item:first-child {
      margin-left: 0;
    }
  }
  .el-form {
    .el-form-item__content {
      flex: 0.85;
    }
  }
  //禁用
  .is-disabled {
    .el-input__wrapper {
      background: none;
      box-shadow: none !important;
      cursor: default !important;
    }
    .el-input__inner {
      cursor: default !important;
    }
    .el-input__suffix {
      display: none;
    }
    .el-textarea__inner {
      box-shadow: none;
      cursor: default;
      background: #fbfbfb;
    }
  }
  &.precision-disabled {
    .el-form-item__label {
      color: #888;
    }
    .el-input__inner,
    .el-textarea__inner {
      -webkit-text-fill-color: #000;
    }
  }
}
.precision-mgleft {
  .el-form-item {
    .el-form-item__content {
      margin-left: 1.875rem !important;
    }
  }
  .el-input-group--prepend {
    border-radius: .3125rem;
    border: .0625rem solid #e3e3e3ff;
    background: #fff;
    overflow: hidden;
    .el-input__wrapper {
      border-radius: 0;
      box-shadow: none;
      padding-left: 0;
    }
    .el-input-group__prepend {
      padding: 0 .75rem;
      background: none;
      box-shadow: none;
      color: #888;
    }
  }
  .el-form-item__label {
    justify-content: flex-start;
    margin-left: 2.1875rem;
  }
  //禁用
  &.disabled {
    .el-form-item__label {
      color: $black !important;
    }
  }
  .is-disabled {
    &.el-input-group--prepend {
      border: 0;
      background: none;
      cursor: default !important;
    }
    .el-input-group__prepend {
      color: #888;
    }
    .el-input__inner {
      cursor: default !important;
      color: #000;
      -webkit-text-fill-color: #000 !important;
    }
  }
}
// 设备详情-内联tabs
.precision-detail-tabs {
  padding: 0 !important;
  margin: -1.875rem 0 0;
  position: relative;
  z-index: 9999;
  .el-tabs__nav {
    border: 0 !important;
    .el-tabs__item {
      height: 2rem;
      font-size: .8125rem;
      color: #000;
      font-weight: normal;
      background: #fff;
      border-top: .0625rem solid rgba(0, 0, 0, 0.1);
      border-right: .0625rem solid rgba(0, 0, 0, 0.1);
      border-bottom: .0625rem solid rgba(0, 0, 0, 0.1) !important;
      &:last-child {
        border-radius: 0 .3125rem .3125rem 0;
      }
      &:first-child {
        border-left: .0625rem solid rgba(0, 0, 0, 0.1);
        border-radius: .3125rem 0 0 .3125rem;
      }
      &.is-active {
        background: #fff;
      }
    }
  }
  .el-form-item__label {
    font-weight: normal;
  }
}
// 禁用状态样式
.el-radio__input.is-disabled.is-checked + span.el-radio__label,
.el-checkbox__input.is-disabled.is-checked + span.el-checkbox__label {
  color: #000;
}
//批量配置
.precision-dialog label {
  font-weight: normal !important;
}
//退出登录弹窗,置顶
.el-overlay.is-message-box {
  z-index: 99999 !important;
}
//拉长高度，兼容ipad出现弹窗的灰色背景没有全覆盖问题
.el-overlay {
  height: var(--mask-height);
}
.el-overlay-dialog {
  height: var(--mask-height);
}

.precision-search-wrapper .el-input__wrapper,
 .precision-product-add .el-input__wrapper  {
  height: 2rem;
  min-width: 5.375rem;
}
.el-dropdown-menu__item:not(.is-disabled):focus {
  color: var(--percision-tecah-select-font-color);
  background-color: #fff;
}
.el-popper {
  border: none !important;
  box-shadow: 0rem 0rem .75rem rgba(0, 0, 0, 0.12);
  .el-menu {
    background-color: #fff !important;
    min-width: 6.375rem !important;
    .el-menu-item {
      background-color: #fff !important;
      color: #888888 !important;
      &.is-active,
      &:hover {
        color: var(--percision-tecah-select-font-color) !important;
      }
    }
  }
  .is-selected {
    color: #00c9a3;
  }
}
.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background-color: #fff !important;
  color: var(--percision-tecah-select-font-color) !important;
}
// .el-popover.el-popper {
//   padding: 0 !important;
// }
// .el-date-editor:hover,
.el-textarea__inner:focus,
.el-input__wrapper.is-focus,
.el-range-editor.is-active {
  box-shadow: 0 0 0 .0625rem var(--percision-tecah-main-color) inset !important;
}
.el-select {
  --el-select-input-focus-border-color: var(--percision-tecah-main-color);
}

.precision-content-wrapper {
  height: calc(100vh - var(--v3-breadcrumb-height) - var(--v3-navigationbar-height) - .625rem);
  display: flex;
  justify-content: center;
}

.precision-middle-wrapper {
  position: relative;
  display: flex;
  width: 75rem;
  overflow: hidden;
}

.precision-right-wrapper {
  width: calc(100% - 19.375rem);
  .el-card {
    border: none;
    box-shadow: none;
  }
}

//滚动条的宽度
::-webkit-scrollbar {
  width: .375rem;
  height: .375rem;
  // background-color: rgb(29, 223, 172);
  border-radius: .375rem;
}

//滚动条的滑块
::-webkit-scrollbar-thumb {
  background-color: #c3c6cb;
  border-radius: .375rem;
}

//设置图标颜色
.el-loading-spinner .path {
  stroke: #00c9a3;
}

//设置文字颜色
.el-loading-spinner .el-loading-text {
  color: #00c9a3;
}

// .el-select .el-input__wrapper.is-focus {
//   box-shadow: 0 0 0 .0625rem var(--percision-tecah-main-color) !important;
// }

.el-date-table td.current:not(.disabled) .el-date-table-cell__text {
  background-color: #00c9a3;
}

.el-date-table-cell:hover,
.el-date-table-cell__text:hover {
  color: #00c9a3;
}

.el-textarea__inner,
.el-input__wrapper,
.el-range-editor.el-input__wrapper {
  box-shadow: 0 0 0 .0625rem rgb(153, 153, 153) inset;
}

.el-range-editor.is-disabled input {
  background-color: initial;
}

.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: rgb(0, 201, 163);
  border-color: rgb(0, 201, 163);
}
.el-cascader-node.is-active,
.in-checked-path {
  color: var(--percision-tecah-main-color) !important;
}
.el-cascader-node.in-active-path {
  color: var(--percision-tecah-main-color) !important;
}
.el-cascader .el-input.is-focus .el-input__wrapper {
  box-shadow: 0 0 0 .0625rem var(--percision-tecah-main-color, var(--percision-tecah-main-color)) inset;
}
// 去掉el-dropdown hover时的黑色边框
.el-tooltip__trigger:focus-visible {
  outline: unset;
} 
body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

// 全屏loading
.el-loading-mask.is-fullscreen{
  background: rgba(0, 0, 0, 0.5);
}