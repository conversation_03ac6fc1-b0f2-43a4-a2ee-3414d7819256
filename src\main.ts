import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import Pagination from './components/Pagination/index.vue'
import App from './App.vue'
import router from './router'

import 'element-plus/dist/index.css'
import './assets/styles/element-override.scss'
import './assets/styles/test.scss'
import './assets/styles/variables.css'
import './assets/styles/mathJye.css'
import './utils/DPlayer/DPlayer.css'

import * as DPlayer from './utils/DPlayer/DPlayer.min.js'
import * as hls from './utils/DPlayer/hls.min.js'
import * as lottie from './utils/lottie/lottie.js'


//动态设置根字体大小
const setHtmlFontSize = () => {
  const designWidth = 1920
  const baseFontSize = 16
  const screenWidth = window.innerWidth
  const fontsize = (screenWidth/designWidth) * baseFontSize
  document.documentElement.style.fontSize = `${fontsize}px`
}
// 初始化设置
// setHtmlFontSize()
// 监听屏幕变化 暂不打开
window.onresize = () => {
  if (window.innerWidth <= 1300) {
    setHtmlFontSize()
  } else {
    document.documentElement.style.fontSize = ''; // 移除内联样式，恢复默认
  }
}
const app = createApp(App)

app.use(createPinia())
app.use(ElementPlus)
app.component('Pagination', Pagination)
app.use(router)

app.config.globalProperties.$DPlayer = DPlayer
app.config.globalProperties.$hls = hls
app.config.globalProperties.$lottie = lottie
app.mount('#app')

//去除console.log
if(location.href.indexOf('https://stu.xiaoyeoo.com/')>-1){
  console.log = function () {};
}