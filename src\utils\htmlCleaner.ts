/**
 * HTML标签清理工具函数
 */

/**
 * 检查字符串是否在去除HTML标签后为空
 * @param str 要检查的字符串
 * @returns 如果字符串在去除HTML标签后为空，则返回true
 */
export const isEmptyAfterRemovingTags = (str: string): boolean => {
  if (!str) return true;
  
  try {
    // 移除所有HTML标签
    const textContent = str.replace(/<[^>]*>/g, '');
    
    // 移除空白字符
    const trimmedContent = textContent.trim();
    
    // 检查是否为空或只包含&nbsp;
    return trimmedContent === '' || trimmedContent === '&nbsp;' || trimmedContent === '\u00A0';
  } catch (error) {
    console.error('检查HTML内容错误:', error);
    return false;
  }
};

/**
 * 清理HTML标签中的常见问题
 * @param str 要清理的字符串
 * @returns 清理后的字符串
 */
export const cleanHtmlTags = (str: string): string => {
  if (!str) return '';
  
  // 检查去除标签后是否为空
  if (isEmptyAfterRemovingTags(str)) {
    return ''; // 如果内容实际为空，直接返回空字符串
  }
  
  try {
    let result = str;
    
    // 完全移除sup和sub标签及其内容
    result = result.replace(/<sup>.*?<\/sup>/gi, '');
    result = result.replace(/<sub>.*?<\/sub>/gi, '');
    
    // 移除空的标签
    const emptyTagsRegexes = [
      /<i>\s*<\/i>/gi,
      /<b>\s*<\/b>/gi,
      /<em>\s*<\/em>/gi,
      /<strong>\s*<\/strong>/gi,
      /<span[^>]*>\s*<\/span>/gi
    ];
    
    // 应用所有正则表达式
    for (const regex of emptyTagsRegexes) {
      result = result.replace(regex, '');
    }
    
    // 处理可能残留的单个标签
    result = result.replace(/<sup>/gi, '');
    result = result.replace(/<\/sup>/gi, '');
    result = result.replace(/<sub>/gi, '');
    result = result.replace(/<\/sub>/gi, '');
    
    return result;
  } catch (error) {
    console.error('清理HTML标签错误:', error);
    return str; // 如果出错，返回原始字符串
  }
};

/**
 * 增强版的字符串处理函数，处理HTML标签问题
 * @param str 要处理的字符串
 * @param processFn 处理函数，如ReplaceMathString
 * @returns 处理后的字符串
 */
export const enhanceStringProcessor = (str: string, processFn: (s: string) => string): string => {
  if (!str) return '';
  
  try {
    // 先清理HTML标签
    const cleanedStr = cleanHtmlTags(str);
    
    // 如果清理后为空，返回空字符串
    if (!cleanedStr) {
      return '';
    }
    
    // 然后调用处理函数
    return processFn(cleanedStr);
  } catch (error) {
    console.error('字符串处理错误:', error);
    // 发生错误时返回原始字符串，避免显示空白
    return str || '';
  }
}; 

/**
 * 专门用于左侧菜单的HTML标签清理函数
 * 完全移除所有HTML标签，保留纯文本
 * @param str 要清理的字符串
 * @returns 清理后的纯文本字符串
 */
export const cleanMenuText = (str: string): string => {
  if (!str) return '';
  
  try {
    // 移除所有HTML标签，只保留文本内容
    return str.replace(/<[^>]*>/g, '');
  } catch (error) {
    console.error('清理菜单文本错误:', error);
    return str; // 如果出错，返回原始字符串
  }
}; 