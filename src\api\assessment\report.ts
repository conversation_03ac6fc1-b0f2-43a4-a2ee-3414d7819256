import { request } from "@/utils/axios"

const ASSESSMENT_PORT = "/api/xiaoyeoo/sdkapi/assessment"

// 获取测试报告详情
export const getTestReportApi = (params: {
  testId: string
  bookId: string
}) => {
  return request({
    url: `${ASSESSMENT_PORT}/test-report`,
    method: 'get',
    params
  })
}

// 获取知识点分析数据
export const getKnowledgeAnalysisApi = (params: {
  testId: string
  bookId: string
}) => {
  return request({
    url: `${ASSESSMENT_PORT}/knowledge-analysis`,
    method: 'get',
    params
  })
}

// 获取能力分析数据
export const getAbilityAnalysisApi = (params: {
  testId: string
  bookId: string
}) => {
  return request({
    url: `${ASSESSMENT_PORT}/ability-analysis`,
    method: 'get',
    params
  })
}

// 获取学习建议
export const getStudySuggestionsApi = (params: {
  testId: string
  score: number
  weakPoints: string[]
}) => {
  return request({
    url: `${ASSESSMENT_PORT}/study-suggestions`,
    method: 'get',
    params
  })
}

// 下载测试报告
export const downloadTestReportApi = (params: {
  testId: string
  format: 'pdf' | 'doc'
}) => {
  return request({
    url: `${ASSESSMENT_PORT}/download-report`,
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}

// 分享测试报告
export const shareTestReportApi = (params: {
  testId: string
  shareType: 'qr' | 'link'
}) => {
  return request({
    url: `${ASSESSMENT_PORT}/share-report`,
    method: 'post',
    data: params
  })
} 