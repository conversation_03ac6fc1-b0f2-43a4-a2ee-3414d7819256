/** 全局 CSS 变量，这种变量不仅可以在 CSS 和 SCSS 中使用，还可以导入到 JS 中使用 */

:root {
  /** 全局背景色 */
  --v3-body-bg-color: #F5F5F5;

  /** NavigationBar 组件 */
  --v3-navigationbar-height: 4.375rem;


  /** rotute-tab 组件 */
  --v3-rotute-tab-height: 5.8125rem;
  /** Breadcrumb 组件 */
  --v3-breadcrumb-height: 3.0625rem;
  
  /** percision-teach主题色 */
  --percision-tecah-main-color: #00c9a3;
  /** percision-teach选中字体色 */
  --percision-tecah-select-font-color: #009c7f;
  /** percision-teach正常字体色 */
  --percision-tecah-font-color: #4d4d4d;
  /** percision-teach局部浅色字体色 */
  --percision-tecah-font-light-color: #7f7f7f;
}
