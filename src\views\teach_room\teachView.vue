<!-- 名师课堂-知识点概述 -->
<template>
  <div class="content">
    <div class="inner">
      <div class="wrap">
        <!-- 优学派h5 -->
        <iframe :src="state.url" class="iframe" v-if="state.url"></iframe>
        <!-- 学大html -->
        <div class="wkinfo" v-html="state.explainStr" v-else></div>
      </div>
    </div>
  </div>
  <!-- 积分弹窗 -->
  <coinAlert :show="state.jfShow" :hide="false" num="1" source="3" @close="state.jfShow=false">
  </coinAlert>
</template>

<script lang="ts" setup>
  import { reactive, onMounted, watch } from 'vue';
  import { useRoute } from "vue-router"
  import { setLearnKey } from '@/utils/user/learntime'
  import coinAlert from "@/views/components/coinAlert/index.vue"

  defineOptions({
    name: "TeachRoomTeachView"
  })

  const route = useRoute()
  const state : any = reactive({
    url: '',
    explainStr: '',
    subject: 0,
    jfShow: false
  })

  onMounted(() => {
    init()
  })

  //监听路由参数
  watch(
    () => route.query,
    (newQ) => {
      if (newQ && route.name == "TeachRoomTeachView") {
        init()
      }
    }
  )

  // 属性赋值
  const setData = (obj : any) => {
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        state[key] = obj[key]
      }
    }
  }
  const init = () => {
    const { url, subject } = route.query
    setData({
      url,
      explainStr: localStorage.explainStr || '',
      subject,
      jfShow: true
    })
    setLearnKey(subject)
  }
</script>

<style lang="scss" scoped>
  @import url('@/assets/styles/reset.css');

  .none {
    display: none !important
  }

  .nowrap {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .content {
    width: 100%;
    overflow-y: auto;
    background: #F5F5F5;
  }

  .inner {
    margin: 0 auto;
    width: 81.25rem;
  }

  .wrap {
    float: left;
    width: 100%;
    height: calc(100vh - 7.4375rem);
    border: .0625rem solid #eaeaea;
    border-bottom: none;
    box-sizing: border-box;
    background: #fff;
    overflow-y: auto;
  }

  .iframe {
    border: 0;
    width: 100%;
    height: 100%;
  }

  .wkinfo {
    float: left;
    width: 100%;
    color: #2a2b2a;
    font-size: .875rem;
    font-weight: 400;
    line-height: 1.625rem;
    box-sizing: border-box;
    padding: .625rem 1.25rem;
  }
</style>
