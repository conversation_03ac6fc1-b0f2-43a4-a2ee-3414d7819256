import { request } from "@/utils/axios"
const SDKPORT = "/api/xiaoyeoo/sdkapi/training"

/** 创建训练（知识点图谱SDK版本(理科)） */
export function createTrainToAtlasApi(data: Object) {
  return request({
    url: `${SDKPORT}/createTrainToAtlas`,
    method: "POST",
    data
  })
}
/** 获取训练详情 */
export function getDetailsApi(params: Object) {
  return request({
    url: `${SDKPORT}/details`,
    method: "GET",
    params
  })
}

/** 获取训练详情 */
export function getDetailssApi(params: Object) {
  return request({
    url: `${SDKPORT}/details`,
    method: "GET",
    params,
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

/** 精准学练；专项练习；真题试卷；中高考专项 训练报告 */
export function trainingReportApi(data: Object) {
  return request({
    url: `${SDKPORT}/trainingReport`,
    method: "POST",
    data
  })
}
/** 单个知识点 */
export function detailsToPointApi(params: Object) {
  return request({
    url: `${SDKPORT}/detailsToPoint`,
    method: "GET",
    params
  })
}
/** 单题批改 */
export function checkTrainQuesApi(data: Object) {
  return request({
    url: `${SDKPORT}/checkTrainQues`,
    method: "POST",
    data
  })
}
/** 保存用户答案 */
export function saveUserJsonApi(data: Object) {
  return request({
    url: `${SDKPORT}/saveUserJson`,
    method: "POST",
    data
  })
}
/** 保存训练 */
export function saveToAtlasApi(data: Object) {
  return request({
    url: `${SDKPORT}/saveToAtlas`,
    method: "POST",
    data
  })
}
/** 真题保存训练 */
export function saveApi(data: Object) {
  return request({
    url: `${SDKPORT}/save`,
    method: "POST",
    data
  })
}
/** 真题试卷创建训练*/
export function reportTrainApi(data: Object) {
  return request({
    url: `${SDKPORT}/reportTrain`,
    method: "POST",
    data
  })
}
/** 同步练习创建训练*/
export function practiceApi(data: Object) {
  return request({
    url: `${SDKPORT}/practice`,
    method: "POST",
    data
  })
}
/** 训练记录*/
export function listApi(data: Object) {
  return request({
    url: `${SDKPORT}/list`,
    method: "POST",
    data
  })
}
/** /错题本 相似题训练【举一反三】*/
export function createApi(data: Object) {
  return request({
    url: `${SDKPORT}/create`,
    method: "POST",
    data
  })
}
// 入门测（创建训练）
export function createTrainToIntroducedApi(data: Object) {
  return request({
    url: `${SDKPORT}/createTrainToIntroduced`,
    method: "POST",
    data,
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

// 保存入门测训练
export function saveToIntroduceddApi(data: Object) {
  return request({
    url: `${SDKPORT}/saveToIntroduced`,
    method: "POST",
    data
  })
}

// 修改训练的训练总时长
export function updateTrainTotalTimeApi(data: Object) {
  return request({
    url: `${SDKPORT}/updateTrainTotalTime`,
    method: "POST",
    data,
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}

/** 下载入门测 */
export function exportToIntroducedApi(params: Object) {
  return request({
    url: `${SDKPORT}/exportToIntroduced`,
    method: "GET",
    params
  })
}