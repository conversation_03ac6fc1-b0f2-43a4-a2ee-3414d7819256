import { request } from "@/utils/axios"
const SDKPORT = "/api/xiaoyeoo/sdkapi/report"

/** 根据教材id获取章节列表 */
export function getChapterReportListApi(data: Object) {
  return request({
    url: `${SDKPORT}/chapterReportList`,
    method: "POST",
    data
  })
}
/** 按条件搜索试卷 */
export function getReportQueryApi(data: Object) {
  return request({
    url: `${SDKPORT}/reportQuery`,
    method: "POST",
    data
  })
}
/** 试卷详情 */
export function getReportGetApi(params: Object) {
  return request({
    url: `${SDKPORT}/reportGet`,
    method: "GET",
    params
  })
}
/** 下载试卷 */
export function downloadReportApi(params: Object) {
  return request({
    url: `${SDKPORT}/downloadReport`,
    method: "GET",
    params,
    headers: { "Content-Type": "application/x-www-form-urlencoded" }
  })
}
/** 我的试卷列表 */
export function getLearnReportListApi(data: Object) {
  return request({
    url: `${SDKPORT}/getLearnReportList`,
    method: "POST",
    data
  })
}
/** 删除我的试卷 */
export function cancelCollectApi(data: Object) {
  return request({
    url: `${SDKPORT}/cancelCollect`,
    method: "POST",
    data
  })
}
/** 加入我的试卷 */
export function collectReportApi(data: Object) {
  return request({
    url: `${SDKPORT}/collectReport`,
    method: "POST",
    data
  })
}
