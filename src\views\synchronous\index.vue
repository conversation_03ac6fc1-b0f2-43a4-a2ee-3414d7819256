<!-- 名师同步学-首页 -->
<template>
  <div class="content" v-loading="loading">
    <img class="bjtp"  @click="goDetail" v-if="gradeId<=6" src="@/assets/img/synchronous/bg1.png" alt="">
    <img class="bjtp"  @click="goDetail" v-else src="@/assets/img/synchronous/bg3.png" alt="">
    <!-- 小学 -->
    <div  v-if="gradeId<=6">
      <div class="course_list">
        <!-- 语文 -->
        <div class="subject" :class="{ 'no-subject': !hasSubject('chinese') && !loading }">
          <div class="subject-title-bar">
              <span class="subject-name">语文</span>
              <div class="subject-links">
                <a href="#">同步汉字描红</a> |
                <a href="#">同步课堂</a> |
                <a href="#">同步阅读课</a> |
                <a href="#">同步作文课</a>
              </div>
            </div>
            <div class="course-cards-container">
              <div   class="course-card"  v-for="course in chineseCourses"  :key="course.title"  @click="selectCourse(course.title, 'chinese', course.moduleType)">
                <img class="image" :src="course.img"  alt="">
              </div>
            </div>
            
            <!-- 语文科目遮罩层 -->
            <div class="subject-full-mask" v-if="!hasSubject('chinese') && !loading">
              <div class="mask-content">
                <div class="mask-text">当前年级暂无此科目</div>
              </div>
            </div>
        </div>
        <!--英语 -->
        <div class="subject" :class="{ 'no-subject': !hasSubject('english') && !loading }">
          <div class="subject-title-bar">
              <span class="subject-name">英语</span> 
              <div class="subject-links">
                <a href="#">外教教口语</a> |
                <a href="#">同步课堂</a> |
                <a href="#">同步词汇</a> 
              </div>
            </div>
            <div class="course-cards-container">
              <div class="course-card" v-for="course in mathematics" :key="course.title" @click="selectCourse(course.title, 'english', course.moduleType)">
                <img class="image" :src="course.img" alt="">
              </div>
            </div>
            
            <!-- 英语科目遮罩层 -->
            <div class="subject-full-mask" v-if="!hasSubject('english') && !loading">
              <div class="mask-content">
                <div class="mask-text">当前年级暂无此科目</div>
              </div>
            </div>
        </div>
        <!-- 数学 -->
        <div class="subject" style="display: flex;" :class="{ 'no-subject': !hasSubject('math') && !loading }">
          <div class="left-ct">
              <span>数学</span>
              <div class="mode">
                <img class="foundation" src="@/assets/img/synchronous/foundation.png" alt="" @click="selectCourse('数学基础', 'math', 1)">
                <img v-if="showImprovementMode" class="improve" src="@/assets/img/synchronous/improve.png" alt="" @click="selectCourse('数学提高', 'math', 8)">
              </div>
          </div>
          <div class="rig-ct">
            <div class="science-container" :class="{ 'no-subject': !hasSubject('science') && !loading }">
              <img class="science" src="@/assets/img/synchronous/science.png" alt="" @click="selectCourse('科学', 'science', 1)">
              <!-- 科学科目遮罩层 -->
              <div class="subject-mask" v-if="!hasSubject('science') && !loading">
                <div class="mask-content">
                  <div class="mask-text">当前年级暂无此科目</div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 数学科目遮罩层 -->
          <div class="subject-full-mask" v-if="!hasSubject('math') && !loading">
            <div class="mask-content">
              <div class="mask-text">当前年级暂无此科目</div>
            </div>
          </div>
        </div>
      </div>

    </div>
    <!-- 初中 -->
    <div  v-else >
      <div class="course_list">
        <!-- 英语 -->
        <div class="subject" :class="{ 'no-subject': !hasSubject('english') && !loading }">
          <div class="subject-title-bar">
              <span class="subject-name">英语</span>
              <div class="subject-links">
                <a href="#">外教教口语</a> |
                <a href="#">同步词汇</a> |
                <a href="#">课文同步</a> |
                <a href="#">语法精讲</a>
              </div>
            </div>
            <div class="course-cards-container">
              <div   class="course-card"  v-for="course in czMathematics"  :key="course.title"  @click="selectCourse(course.title, 'english', course.moduleType)">
                <img class="image" :src="course.img"  alt="">
              </div>
            </div>
            
            <!-- 英语科目遮罩层 -->
            <div class="subject-full-mask" v-if="!hasSubject('english') && !loading">
              <div class="mask-content">
                <div class="mask-text">当前年级暂无此科目</div>
              </div>
            </div>
      
        </div>
        <!-- 数学 -->
        <div class="subject" style="display: flex;" :class="{ 'no-subject': !hasSubject('math') && !loading }">
          <div class="left-ct">
              <span>数学</span>
              <div class="mode">
                <img class="foundation" src="@/assets/img/synchronous/foundation.png" alt="" @click="selectCourse('数学基础', 'math', 1)">
                <img v-if="showImprovementMode" class="improve" src="@/assets/img/synchronous/improve.png" alt="" @click="selectCourse('数学提高', 'math', 8)">
              </div>
          </div>
          <div class="rig-ct">
            <div class="science-container" :class="{ 'no-subject': !hasSubject('chinese') && !loading }">
              <img class="science" src="@/assets/img/synchronous/czyw.png" alt="" @click="selectCourse('语文', 'chinese', 1)">
              <!-- 语文科目遮罩层 -->
              <div class="subject-mask" v-if="!hasSubject('chinese') && !loading">
                <div class="mask-content">
                  <div class="mask-text">当前年级暂无此科目</div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 数学科目遮罩层 -->
          <div class="subject-full-mask" v-if="!hasSubject('math') && !loading">
            <div class="mask-content">
              <div class="mask-text">当前年级暂无此科目</div>
            </div>
          </div>
        </div>
        <!--初中其他科目 -->
        <div class="subject">
          
            <div class="course-cards-container">
              <div 
                class="course-card subject-qt" 
                v-for="course in allCzSubjects" 
                :key="course.subjectKey" 
                :class="{ 'no-subject': !hasSubject(course.subjectKey) && !loading }"
                @click="selectCourse(course.title, course.subjectKey, course.moduleType || 1)"
              >
              <!-- {{ course.subjectKey }} -->
                <img class="image" :src="course.img"  alt="">
                <!-- 遮罩层 -->
                <div class="subject-mask" v-if="!hasSubject(course.subjectKey) && !loading">
                  <div class="mask-content">
                    <div class="mask-text">当前年级暂无此科目</div>
                  </div>
                </div>
              </div>
            </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>

  import { userGetAllApi } from "@/api/user"
  import { reactive, onMounted, ref, computed } from 'vue';
  import router from '@/router/index'
  import { useUserStore } from "@/store/modules/user"
  import { useRouteStoreHook } from '@/store/modules/route'
  import tb from '@/assets/img/synchronous/tb.png';
  import kt from '@/assets/img/synchronous/kt.png';
  import tbydk from '@/assets/img/synchronous/tbydk.png';
  import tbzwk from '@/assets/img/synchronous/tbzwk.png';
  import wjjky from '@/assets/img/synchronous/wjjky.png';
  import tbkt from '@/assets/img/synchronous/tbkt.png';
  import tbchs from '@/assets/img/synchronous/tbchs.png';
  import czwjky from '@/assets/img/synchronous/czwjky.png';
  import cztbch from '@/assets/img/synchronous/cztbch.png';
  import czkwtb from '@/assets/img/synchronous/czkwtb.png';
  import czyfjj from '@/assets/img/synchronous/czyfjj.png';
  import czwl from '@/assets/img/synchronous/czwl.png';
  import czhx from '@/assets/img/synchronous/czhx.png';
  import czsw from '@/assets/img/synchronous/czsw.png';
  import czls from '@/assets/img/synchronous/czls.png';
  import czdl from '@/assets/img/synchronous/czdl.png';
  import dzdf from '@/assets/img/synchronous/dzdf.png';
  import czyw from '@/assets/img/synchronous/czyw.png';

  // import xiezuoMain from '@/assets/img/synchronous/xiezuo_main.png';

  defineOptions({
    name: "SynchronousIndex"
  })

  const yxpVersions=ref([])
  const learnNow = useUserStore().learnNow
  const loading = ref(true)

  const state : any = reactive({
    gradeId: learnNow.gradeId || 0
  })

  
  const gradeId= computed(() => {
    return useUserStore().learnNow.gradeId 
  });

  const showImprovementMode = computed(() => {
    return gradeId.value < 10; // Only show for non-high school grades
  });

  // 判断是否存在指定科目的计算属性（使用包含匹配）
  const hasSubject = (subject: string) => {
    return yxpVersions.value.some((item: any) => item.subject && item.subject.includes(subject));
  };

  // 科目映射关系
  const subjectMapping: any = {
    'physics': { img: czwl, title: '物理', subjectKey: 'physics', moduleType: 1 },
    'chemistry': { img: czhx, title: '化学', subjectKey: 'chemistry', moduleType: 1 },
    'bio': { img: czsw, title: '生物', subjectKey: 'bio', moduleType: 1 },
    'history': { img: czls, title: '历史', subjectKey: 'history', moduleType: 1 },
    'geography': { img: czdl, title: '地理', subjectKey: 'geography', moduleType: 1 },
    'politics': { img: dzdf, title: '道德与法治', subjectKey: 'politics', moduleType: 1 }
  };

  // 所有初中其他科目（包含科目键用于判断）
  const allCzSubjects = computed(() => {
    const otherSubjects = ['physics', 'chemistry', 'bio', 'history', 'geography', 'politics'];
    return otherSubjects.map(subject => subjectMapping[subject]);
  });

  // 根据存在的科目过滤初中其他科目（保留此计算属性以兼容其他可能的使用）
  const filteredCzSubjects = computed(() => {
    const otherSubjects = ['physics', 'chemistry', 'bio', 'history', 'geography', 'politics'];
    return otherSubjects
      .filter(subject => hasSubject(subject))
      .map(subject => subjectMapping[subject]);
  });

  const mathematics =ref([
    {
      img: wjjky,
      title: '外教教口语',
      moduleType:4
    },
    {
      img: tbkt,
      title: '同步课堂',
      moduleType:1
    },
    {
      img: tbchs,
      title: '同步词汇',
      moduleType:3
    }
  ])
  const chineseCourses = ref([
    {
      img: tb,
      title: '同步汉字描红',
      moduleType:2
    },
    {
      img: kt,
      title: '同步课堂',
      moduleType:1
    },
    {
      img: tbydk,
      title: '同步阅读课',
      moduleType:7
    },
    {
      img: tbzwk,
      title: '同步作文课',
      moduleType:6
    }
  ]);
  const czMathematics = ref([
    {
      img: czwjky,
      title: '外教教口语',
      moduleType:4
    },
    {
      img: cztbch,
      title: '同步词汇',
      moduleType:3
    },
    {
      img: czkwtb,
      title: '课文同步',
      moduleType:1
    },
    {
      img: czyfjj,
      title: '语法精讲',
      moduleType:5
    }
  ]);

  const selectedCourse = ref('同步汉字描红');
  const selectCourse = (title: string, subjectType?: string, moduleType?: number) => {
    selectedCourse.value = title;
    
    // 根据科目类型查找对应的bookId
    let bookId = '';
    let matchedItem :any= {}
    if (subjectType && yxpVersions.value.length > 0) {
       matchedItem = yxpVersions.value.find((item: any) => 
        item.subject && item.subject.includes(subjectType)
      );
      if (matchedItem) {
        bookId = matchedItem.bookId || '';
      }
    }
    
    // 如果没有传入moduleType，默认为1
    const finalModuleType = moduleType || 1;
    
    // console.log('选择课程:', title, '科目类型:', subjectType, '模块类型:', finalModuleType, '找到的bookId:', bookId,matchedItem);
    
    // 跳转时带上所有参数
    const query: any = {};
    if (bookId) {
      query.bookId = bookId;
    }
    if (title) {
      query.courseTitle = title;
    }
    if (subjectType) {
      query.subjectType = subjectType;
    }
    if (matchedItem?.editionId_yxp) { 
      query.subject = matchedItem.subject;
      query.editionName = matchedItem.editionName;
      query.editionId_yxp = matchedItem.editionId_yxp;
      query.gradeName = matchedItem.gradeName;
      query.subjectName = matchedItem.subjectName;
      query.termName = matchedItem.termName;
    }
    query.moduleType = finalModuleType;

    // 清理不相关的缓存
    try {
      // 清理科目相关缓存的函数
      const clearSubjectCache = (subjectType: string, moduleType: number) => {
        const cacheKey = `syncSelectedBook_${subjectType}_${moduleType}`;
        localStorage.removeItem(cacheKey);
      };
      
      // 检查是否有当前科目的缓存
      const cacheKey = `syncSelectedBook_${subjectType}_${finalModuleType}`;
      const savedBookInfoStr = localStorage.getItem(cacheKey);
      
      if (savedBookInfoStr) {
        const savedBookInfo = JSON.parse(savedBookInfoStr);
        
        // 如果年级不匹配，清除该科目所有相关缓存
        if (savedBookInfo.gradeId && savedBookInfo.gradeId !== gradeId.value) {
          console.log('年级不匹配，清除该科目缓存');
          
          // 清除特定科目缓存
          clearSubjectCache(subjectType || '', finalModuleType);
          
          // 检查默认缓存是否也属于这个科目，如果是则也清除
          const defaultCacheStr = localStorage.getItem('syncSelectedBook');
          if (defaultCacheStr) {
            const defaultCache = JSON.parse(defaultCacheStr);
            if (defaultCache.subjectType === subjectType && 
                defaultCache.moduleType === finalModuleType.toString()) {
              localStorage.removeItem('syncSelectedBook');
            }
          }
        }
      }
    } catch (err) {
      console.error('处理缓存时出错:', err);
    }
    
    router.push({ 
      name: "SynchronousList", 
      query: query
    });
  };

  onMounted(() => {
    // console.log(learnNow,"learnNowlearnNow")
    // yxpVersions.value = learnNow.yxpVersions || []
    init()
  })

  const init = async() => {
    loading.value = true; // Start loading

    //tabbar选中
    // const res: any = await userGetAllApi()
    // if (res.code == 200) { 
    yxpVersions.value = learnNow.yxpVersions || []
      // } else {
        // console.error('获取数据失败:', res.msg || '未知错误');
      // }
    // } catch (error) {
      // console.error('获取数据出错:', error);
    // } finally {
    loading.value = false; // Always hide loading when done
    
  }

  // 获取可用科目列表（用于调试）
  const getAvailableSubjects = () => {
    const subjects = [...new Set(yxpVersions.value.map((item: any) => item.subject))];
    return subjects;
  };

  //跳介绍
  const goDetail = () => {
    router.push({ name: "Introduce" })
  }

  //跳章节列表
  const goList = (subName : any) => {
    router.push({ name: "TeachRoomTeachList", query: { subName } })
  }
</script>

<style lang="scss" scoped>
  .content{
    width: 100%;
    // margin: 0 auto;
  }
  .bjtp{
    cursor: pointer;
    width: 100%;
    // height: auto;
    // max-height: 17.5rem;
    // object-fit: cover;
    // object-position: center;
    // display: block;
  }
  .sub_bg {
    cursor: pointer;
    width: 100%;
    // width: 120rem;
    height:22.0625rem;
    background: url(@/assets/img/synchronous/bg1.png) no-repeat;
    background-size: 100% 100%;
    background-position: center;
    margin: -1.375rem 0 0;
  }
    .sub_bg2 {
    background: url(@/assets/img/teachroom/bg2.png) no-repeat;
    background-size: 100% 100%;
    background-position: center;
  }
  .course_list {
    background: #fff;
    padding: 1.25rem;
    box-shadow: 0rem .375rem 1rem 0rem rgba(185, 194, 200, 0.3);
    margin-top: 1.25rem;
  }
  .subject{
    position: relative;
    margin-bottom: 3.125rem;
    
    // 没有科目时的样式
    &.no-subject {
      .subject-title-bar,
      .course-cards-container,
      .left-ct,
      .rig-ct {
        opacity: 1;
        pointer-events: none;
      }
    }
    .left-ct{
      padding:  1.875rem;
      background: #c5d8ff;
      border-radius: 1.25rem;
      span{
        display: block;
        padding-bottom: 1rem;
        font-size: 1.125rem;
        color: #2A2B2A;
        font-weight: 700;
      }
      .mode{  
        cursor: pointer;
        padding: 0 1.4375rem;
        .foundation{
          width: 21.6875rem;
          height: 6.25rem;
          margin-right: 2.375rem;
          transition: all 0.3s ease;
            &:hover {
          transform: translateY(-0.3125rem);
        }
        }
      
         .improve{
            width: 21.6875rem;
            height: 6.25rem;
            transition: all 0.3s ease;
              &:hover {
        transform: translateY(-0.3125rem);
      }
        }
    
      }
      
     
    }
    .rig-ct{
      width: 25rem;
      margin-left: auto;
      cursor: pointer;

      .science-container {
        position: relative;
        width: 25rem;
        height: 12.5rem;

        &.no-subject {
          opacity: 1;
          pointer-events: none;
        }
      }

      .science{
        width: 25rem;
        height: 12.5rem;
        transition: all 0.3s ease;
        &:hover {
        transform: translateY(-0.3125rem);
      }
      }
    }
  }
  .subject-title-bar {
    display: flex;
    align-items: center;
    margin-bottom: 1.25rem;
    border-bottom: .0625rem solid rgb(234, 234, 234);
    padding-bottom: 1.25rem;
    .subject-name {
      font-size: 1.125rem;
      font-weight: bold;
      color: #000;
      margin-right: 1.25rem;
    }
    .subject-links {
      display: flex;
      align-items: center;
      gap: .625rem;
      color: #888;
      a {
        text-decoration: none;
        color: #888;
        font-size: .875rem;
        // &:hover {
        //   color: #333;
        // }
      }
    }
  }
  .course-cards-container {
    display: flex;
    gap: 1.25rem;
  }
  .course-card {
    cursor: pointer;
    background: #fff;
    flex: 1;
    // box-shadow: 0 .25rem .5rem rgba(0,0,0,0.05);
    border: .125rem solid transparent;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-0.3125rem);
    }
    .image{
      width: 100%;
    }

  }
  .subject-qt{
    position: relative;
    .image{
      width: 12.0625rem;
      height: 10.875rem;
    }
    
    // 单个课程卡片没有科目时的样式
    &.no-subject {
      opacity: 0.3;
      pointer-events: none;
    }
  }
  
  // 遮罩层样式
  .subject-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 1.25rem;
    z-index: 10;
    
    .mask-content {
      color: #fff;
      font-size: 1rem;
      font-weight: 600;
      text-align: center;
      padding: .625rem;
    }

    .mask-text {
      background-color: rgba(0, 0, 0, 0.6);
      padding: .5rem .9375rem;
      border-radius: .25rem;
    }
  }
  
  // 整个科目区域的遮罩层
  .subject-full-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 20;
    border-radius: 1.25rem;
    .mask-content {
      color: #fff;
      font-size: 1.125rem;
      font-weight: 600;
      text-align: center;
      padding: .9375rem;
      border-radius: 1.25rem;
    }

    .mask-text {
      background-color: rgba(0, 0, 0, 0.6);
      padding: .625rem 1.25rem;
      border-radius: .25rem;
    }
  }
</style>
