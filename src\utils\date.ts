/**
 * 日期格式化工具函数
 */

/**
 * 格式化日期为 YYYY/MM/DD HH:MM 格式
 * @param date 日期对象，默认为当前时间
 * @returns 格式化后的日期字符串
 */
export function formatDateTime(date = new Date()): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  
  return `${year}/${month}/${day} ${hours}:${minutes}`;
}

/**
 * 获取当前时间的格式化字符串 YYYY/MM/DD HH:MM
 * @returns 当前时间的格式化字符串
 */
export function getCurrentDateTime(): string {
  return formatDateTime(new Date());
}

/**
 * 将时间字符串转换为格式化的日期时间
 * @param timeStr 时间字符串
 * @returns 格式化后的日期字符串，如果输入无效则返回空字符串
 */
export function formatTimeString(timeStr: string): string {
  if (!timeStr) return '';
  
  try {
    const date = new Date(timeStr);
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return '';
    }
    
    return formatDateTime(date);
  } catch (error) {
    console.error('日期格式化错误:', error);
    return '';
  }
} 