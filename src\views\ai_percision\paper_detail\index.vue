<template>
    <div class="container">
      <div class="left">
        <div class="top-box">
            <div class="title-handle">
                <div class="title-box">
                    <img class="test-icon" src="@/assets/img/percision/title.png" />
                    <div class="test-title">北师大版（2024）九年级下学期《第1章 整式的乘除》2025年最热同步卷</div>
                    <div class="icon-btn size84">
                        <img src="@/assets/img/percision/download.png" alt="">
                        下载
                    </div>
                </div>
                <div class="btn"> + 加入我的试卷</div>
            </div>
            <div class="title-data">
                <div class="title-data-item">更新时间：2025/01/07</div>
                <div class="title-data-item">浏览：2241</div>
                <div class="title-data-item">题量：20</div>
                <div class="title-data-item">地区：广东省深圳市</div>
            </div>
        </div>
      </div>
      <div class="right">
        <div class="line-text">
            <el-divider style="width: 2.5625rem" border-style="dashed"></el-divider>密<el-divider style="width: 6.25rem" border-style="dashed"></el-divider>封<el-divider style="width: 6.25rem" border-style="dashed"></el-divider>线<el-divider style="width: 2.5625rem" border-style="dashed"></el-divider>
        </div>
        <div class="test-type-box">
            <div class="test-type-item">
                <p>15</p>
                <div>一、选择题</div>
            </div>
            <div class="test-type-item">
                <p>15</p>
                <div>一、选择题</div>
            </div>
            <div class="test-type-item">
                <p>15</p>
                <div>一、选择题</div>
            </div>
            <div class="test-type-item green">
                <p>44</p>
                <div>总计</div>
            </div>
            <div class="icon-btn size285" @click="toWrite">
                <img src="@/assets/img/percision/write.png" alt="">
                开始作答
            </div>
            <p class="test-type-box-tips">点击开始作答后，该套试卷自动加入到“我的试卷”</p>
        </div>
        <div class="line-text">
            <el-divider style="width: 2.5625rem" border-style="dashed"></el-divider>密<el-divider style="width: 6.25rem" border-style="dashed"></el-divider>封<el-divider style="width: 6.25rem" border-style="dashed"></el-divider>线<el-divider style="width: 2.5625rem" border-style="dashed"></el-divider>
        </div>
      </div>
    </div>
</template>
  
<script lang="ts" setup>
// import { useAuthStore } from '../store'
import { useRouter } from 'vue-router'

// const authStore = useAuthStore()
const router = useRouter()
const toWrite = () => {
    router.push({ 
        path: '/ai_percision/knowledge_graph_detail/paper_write'
    })
}

</script>
<style lang="scss" scoped>
.container {
    display: flex;
    .left {
        padding: 1.25rem;
        width: 60.3125rem;
        background: #ffffff;
        .top-box {
            border-bottom: .0625rem solid #EAEAEA;
            .title-handle {
                display: flex;
                justify-content: space-between;
                .title-box {
                    display: flex;
                    align-items: center;
                    .test-icon {
                        width: 1.125rem;
                        height: 1.125rem;
                    }
                    .test-title {
                        color: #2a2b2a;
                        font-size: 1rem;
                        font-weight: 700;
                        margin-left: .5rem;
                        margin-right: .625rem;
                    }
                    .size84 {
                        width: 5.25rem;
                        height: 1.875rem;
                        font-size: .875rem;
                        img {
                            width: .75rem;
                            height: .75rem;
                        }
                    }
                }
                .btn {
                    width: 7.375rem;
                    height: 1.875rem;
                    line-height: 1.875rem;
                    text-align: center;
                    font-size: .875rem;
                    border-radius: .25rem;
                    border: .0625rem solid #009c7f;
                    background: #ffffff;
                    cursor: pointer;
                    color: #009c7f;
                }
            }
            .title-data {
                margin-top: .625rem;
                margin-bottom: 1rem;
                &-item {
                    display: inline-block;
                    border-radius: .875rem;
                    padding: .375rem .75rem;
                    background: #fef8e9;
                    color: #ef9d19;
                    font-size: .75rem;
                    font-weight: 400;
                    margin-right: .625rem;
                }
            }
        }
    }
    .right {
        padding: 1.125rem 0;
        width: 20.3125rem;
        margin-left: .625rem;
        background: #ffffff;
        box-sizing: border-box;
        height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height));
        .line-text {
            display: flex;
            align-items: center;
        }
        .test-type-box {
            height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height) - 8.125rem);
            box-sizing: border-box;
            padding: .625rem;
            .test-type-item {
                display: inline-block;
                margin-left: .625rem;
                margin-bottom: .625rem;
                width: 8.5625rem;
                height: 7.3125rem;
                border: .0625rem solid #eaeaea;
                background: #ffffff;
                padding: 1rem 0;
                box-sizing: border-box;
                p{
                    color: #2a2b2a;
                    font-size: 2.5rem;
                    font-weight: 700;
                    text-align: center;
                    margin: 0;
                }
                div{
                    color: #666666;
                    font-size: 1rem;
                    font-weight: 400;
                    text-align: center;
                }
            }
            .green {
                border: .0625rem solid #00c9a3;
                background: #e5f9f6;
                p {
                    color: #009c7f;
                }

            }
            .size285 {
                width: 17.8125rem;
                height: 2.75rem;
                font-size: 1rem;
                font-weight: 700;
                margin-left: .625rem;
                margin-top: 1.25rem;
                img {
                    width: 1rem;
                    height: 1rem;
                }
            }
            &-tips {
                text-align: center;
                color: #999999;
                font-size: .75rem;
                font-weight: 400;

            }
        }
    }
}
.icon-btn {
    border-radius: .25rem;
    background: #00c9a3;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    img {
        margin-right: .3125rem;
    }
}
</style>