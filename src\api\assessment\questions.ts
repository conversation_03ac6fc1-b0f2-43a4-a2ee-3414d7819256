import { request } from "@/utils/axios"

const QUESTIONS_PORT = "/api/xiaoyeoo/sdkapi/assessment"

// 获取试题列表
export const getQuestionListApi = (params: {
  unitId?: string | number
  bookId: string | number
  type?: string // unit | midterm | final
}) => {
  return request({
    url: `${QUESTIONS_PORT}/questions`,
    method: 'get',
    params
  })
}

// 获取试题详情
export const getQuestionDetailApi = (params: {
  questionId: string | number
}) => {
  return request({
    url: `${QUESTIONS_PORT}/question/detail`,
    method: 'get',
    params
  })
}

// 获取试题统计信息
export const getQuestionStatsApi = (params: {
  unitId?: string | number
  bookId: string | number
  type?: string
}) => {
  return request({
    url: `${QUESTIONS_PORT}/questions/stats`,
    method: 'get',
    params
  })
}

// 下载试题
export const downloadQuestionsApi = (params: {
  unitId?: string | number
  bookId: string | number
  type?: string
}) => {
  return request({
    url: `${QUESTIONS_PORT}/questions/download`,
    method: 'post',
    data: params,
    responseType: 'blob'
  })
} 