<template>
  <!-- 购买/兑换会员 -->
  <div v-if="false">
    <div class="alert_bg"></div>
    <div class="alert_box">
      <div class="alert_inner">
        <div class="alert_top">
          <div class="alert_h1">购买/兑换会员</div>
          <img src="@/assets/img/user/close.svg" class="alert_x" @click="quitHide" />
        </div>
        <div class="alert_wrap">
          <div class="exbox">
            <div class="extip">您还不是会员，请输入兑换码兑换会员</div>
            <div class="exlab">
              <el-input class="exinput" v-model="state.exCode" autocomplete="off" placeholder="请输入12位兑换码"
                maxlength="12"></el-input>
              <div class="exbtn" @click="exchangeCode">立即兑换</div>
            </div>
            <div class="qrbox">
              <div class="qrtip">
                您还可以扫码进入 <span class="green">家长小程序</span> 购买会员；或 <span
                  class="red">￥{{state.packInfo.discountedPrice}}</span> 购买 <span class="red">1年会员</span>。
              </div>
              <div class="qrcode">
                <div class="qrimg1">
                  <img :src="state.qrcode2" :class="state.qrcode2?'':'opac'" />
                  <div>小程序二维码</div>
                </div>
                <div class="qrline"></div>
                <div class="qrimg2">
                  <img :src="state.qrcode3" class="qricon" :class="state.qrcode3?'':'opac'" />
                  <img src="@/assets/img/user/qrborder.svg" class="qrborder" />
                  <div style="position: relative;top: 5px;">微信扫码付款</div>
                </div>
              </div>
            </div>
            <div class="agree">
              购买表示您同意<router-link class="link" :to="'/user_agent'" target="_blank">《平台用户服务协议》</router-link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 购买/兑换成功 -->
  <div v-if="state.showOk">
    <div class="alert_bg"></div>
    <div class="alert_box">
      <div class="alert_inner">
        <div class="alert_top">
          <div class="alert_h1">{{state.payTxt}}会员</div>
          <img src="@/assets/img/user/close.svg" class="alert_x" @click="quitHide" />
        </div>
        <div class="alert_wrap">
          <div class="buy">
            <img src="@/assets/img/user/ok.svg" />
            <div class="buy_h1">购买成功</div>
            <div class="buy_date">您的会员已购买成功！有效期至<span>{{state.memberInfo.expirationTime}}</span></div>
            <div class="alert_time">{{state.second}}s后自动关闭</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { reactive, onMounted, watch } from 'vue';
  import router from '@/router/index'
  import { useUserStore } from "@/store/modules/user"
  import { userVipInfoApi, createAppletCodeApi, exchangeCodeApi, memberOrderByNativeApi, goodsfindByIdApi, getOrderByNativeApi } from "@/api/user"
  import { ElMessage } from "element-plus"
  import QRCode from 'qrcode';

  onMounted(() => {
    getGoodId()
  })
  const emit = defineEmits(['close'])
  const props = defineProps({
    show: {
      type: Boolean,
      default: false
    },
    showOk: {
      type: Boolean,
      default: false
    }
  })
  
  watch(() => props.show, (newVal, oldVal) => {
    if (newVal) { vipAlertShow() }
  }, { immediate: true })

  let interval : any = null
  let learnNow = localStorage.learnNow ? JSON.parse(localStorage.learnNow) : ''
  const state : any = reactive({
    list: [],
    usersNow: '',
    learnId: learnNow?.learnId || '',
    isQuit: false,
    userId: localStorage.sysUserId || '',
    userInfo: localStorage.userInfo ? JSON.parse(localStorage.userInfo) : '',
    memberInfo: '',
    qrcode1: '',
    qrcode2: '',
    qrcode3: '',
    exCode: '',
    showVip: false,
    showOk: false,
    second: 3,
    goodId: '',
    outTradeNo: '',
    packInfo: {
      discountedPrice: ''
    },
    payTxt: ''
  })

  //会员信息
  const userVipInfo = () => {
    let param = {
      userId: state.userId
    }
    userVipInfoApi(param).then((res : any) => {
      let memberInfo = res.data || '1'//自习室默认有会员
      state.memberInfo = memberInfo
      if (memberInfo) {
        //处理到期时间
        if (memberInfo?.expirationTime) {
          memberInfo.expirationTime = memberInfo.expirationTime.substr(0, 16)
          state.memberInfo = memberInfo
        }
        memberInfo = JSON.stringify(memberInfo)
      }
      localStorage.memberInfo = memberInfo
      useUserStore().memberInfo = memberInfo
    })
  }

  //购买会员弹窗
  const vipAlertShow = () => {
    state.exCode = ''
    state.showVip = true
    createAppletCode()
    createWxPayCode()
    goodsfindById()
    getOrderState()
  }

  //关闭弹窗
  const quitHide = () => {
    state.isQuit = false
    state.showOk = false
    state.showVip = false
    if (interval) {
      clearInterval(interval)
    }
    if (timer) {
      clearInterval(timer)
    }
    emit('close', false)
  }

  //购买成功弹窗
  const buyOkShow = () => {
    quitHide()
    state.showOk = true
  }

  //立即兑换
  const exchangeCode = () => {
    const code = state.exCode
    //兑换码判断有数字和字母
    if (!code) {
      ElMessage.error('请输入兑换码！')
      return
    }
    if (code.length != 12) {
      ElMessage.error('请输入12位的兑换码！')
      return
    }
    const param = {
      userId: state.userId,
      card: code,
      userSource: 'xiaoyeoopad'
    }
    exchangeCodeApi(param).then((res : any) => {
      const msg = res.data
      if (msg == 'vip' || msg == 'xiaoyeoo' || msg == 'tailove') {
        //vip
        ElMessage.success('VIP会员兑换成功!\n您还可以登录精准教学web平台使用。')
      } else if (msg == 'svip') {
        //svip
        ElMessage.success('SVIP会员兑换成功!\n校园相关功能请登录精准教学web平台使用。')
      }
      state.payTxt = '兑换'
      buyOkShow()
      interval = setInterval(remainTime, 1000)
      userVipInfo()
    })
  }

  //倒计时
  const remainTime = () => {
    if (state.second == 0) {
      clearInterval(interval)
      state.second = 3
      quitHide()
    } else {
      state.second--
    }
  }

  //生成小程序-会员权益
  const createAppletCode = () => {
    let param = {
      page: 'pages/vip/benefits/benefits',
      scene: '__4',
      type: 4
    }
    createAppletCodeApi(param).then((res : any) => {
      // blob图片
      state.qrcode2 = window.URL.createObjectURL(res.data)
    })
  }
  //获取1年套餐的goodID
  const getGoodId = () => {
    const href = location.href
    let goodId = ''
    if (href.indexOf('http://localhost') > -1) {
      //本地
      goodId = "1795394121544822786"
    } else if (href.indexOf('-dev.') > -1) {
      //开发
      goodId = "1780148133349642242"
    } else if (href.indexOf('-test.') > -1) {
      //测试
      goodId = "1795394121544822786"
    } else {
      //正式
      goodId = "1788900481299423233"
    }
    state.goodId = goodId
  }
  //生成微信支付码
  const createWxPayCode = () => {
    let param = {
      userId: state.userId,
      goodsId: state.goodId,
      userSource: 'xiaoyeoopad'
    }
    memberOrderByNativeApi(param).then((res : any) => {
      let data = res.data || ''
      if (data) {
        state.outTradeNo = data.outTradeNo
        // 生成二维码
        const text = data.codeUrl;
        const options = {
          errorCorrectionLevel: 'H', // 错误更正级别：L, M, Q, H
          type: 'image/png', // 输出类型
          quality: 0.92, // 图片质量（仅限PNG）
          margin: 1, // 边距
          color: {
            dark: '#000000', // 深色色块
            light: '#FFFFFF' // 浅色背景
          },
          scale: 8 // 点像素大小
        }
        QRCode.toDataURL(text, options, (err : any, url : any) => {
          state.qrcode3 = url
        })
      }
    })
  }
  //获取套餐价格
  const goodsfindById = () => {
    goodsfindByIdApi(state.goodId).then((res : any) => {
      state.packInfo = res.data
    })
  }

  //获取订单状态
  let timer : any = null
  const getOrderState = () => {
    timer = setInterval(() => {
      let param = {
        userId: state.userId,
        goodsId: state.goodId,
        userSource: 'xiaoyeoopad'
      }
      getOrderByNativeApi(param).then((res : any) => {
        if (res.data.paymentStatus == 'SUCCESS') {
          clearInterval(timer)
          state.payTxt = '购买'
          buyOkShow()
          interval = setInterval(remainTime, 1000)
          userVipInfo()
        }
      })
    }, 4000)
  }
</script>
<style lang="scss" scoped>
  .alert_inner {
    width: 798px;
    border-radius: 20px;
    background: #ffffff;
    box-sizing: border-box;
    padding: 0 20px;
  }

  .alert_top {
    float: left;
    width: 100%;
    height: 54px;
    border-bottom: 1px solid #eee;
  }

  .alert_h1 {
    float: left;
    width: 100%;
    line-height: 54px;
    text-align: center;
    color: #2a2b2a;
    font-size: 16px;
    font-weight: bold;
  }

  .alert_x {
    float: right;
    width: 15px;
    height: 15px;
    padding: 19px;
    margin: -50px -18px 0 0;
  }

  .alert_x:hover {
    cursor: pointer;
  }

  .alert_wrap {
    width: 100%;
    float: left;
  }

  .alert_form {
    width: 462px;
    margin: 0 auto;
  }

  .alert_item {
    margin: 59px 0 0;
  }

  .alert_label {
    line-height: 21px;
    color: #2a2b2a;
    font-size: 16px;
    padding: 0 0 8px;
  }

  .alert_input {
    width: 462px;
    height: 53px;
  }

  .alert_input .el-input__inner {
    height: 53px;
    border-radius: 6px;
    background: #f5f5f6;
    border: 1px solid #f5f5f6;
  }

  .alert_foot {
    width: 100%;
    float: left;
    margin: 256px 0 30px;
    display: flex;
    justify-content: center;
  }

  .alert_ok {
    width: 122px;
    height: 38px;
    border-radius: 19px !important;
    background: #00c9a3 !important;
    border-color: #00c9a3 !important;
  }

  /* 兑换会员 */
  .exbox {
    width: 658px;
    margin: 0 auto;
  }

  .extip {
    float: left;
    width: 100%;
    line-height: 19px;
    color: #2a2b2a;
    font-size: 14px;
    margin: 40px 0 21px;
  }

  .exlab {
    float: left;
    width: 100%;
    padding: 0 0 16px;
    border-bottom: 1px dashed #eaeaea;
  }

  .exinput {
    float: left;
    width: 544px !important;
  }

  .exinput :deep(.el-input__wrapper) {
    background: #f5f5f6;
    box-shadow: none;
  }

  .exinput .el-input__inner,
  :deep(.exinput .el-input__inner) {
    height: 48px;
    border-radius: 6px;
    background: #f5f5f6;
    border-color: #f5f5f6;
  }

  .exbtn {
    float: right;
    width: 104px;
    line-height: 50px;
    text-align: center;
    border-radius: 4px;
    background: #e5f9f6;
    color: #009c7f;
    font-size: 14px;
  }

  .exbtn:hover {
    cursor: pointer;
  }

  .qrbox {
    float: left;
    width: 100%;
    margin: 15px 0 16px;
    border-radius: 6px;
    border: 1px solid #eaeaea;
  }

  .qrtip {
    float: left;
    width: 100%;
    color: #666666;
    font-size: 14px;
    box-sizing: border-box;
    padding: 16px 0 0 16px;
  }

  .qrtip .green {
    color: #009C7F;
  }

  .qrtip .red {
    color: #DD2A2A;
  }

  .qrcode {
    float: left;
    width: 100%;
    box-sizing: border-box;
    padding: 21px 100px 21px;
  }

  .qrimg1 {
    float: left;
    width: 150px;
  }

  .qrimg1 img {
    width: 150px;
    height: 150px;
  }

  .qrimg1 div,
  .qrimg2 div {
    float: left;
    width: 100%;
    text-align: center;
    line-height: 16px;
    color: #666666;
    font-size: 12px;
    margin: 10px 0 0;
  }

  .qrline {
    float: left;
    width: 1px;
    height: 177px;
    border-left: 1px dashed #eaeaea;
    margin: 0 78px;
  }

  .qrimg2 {
    float: left;
    width: 150px;
  }

  .qricon {
    float: left;
    width: 130px;
    height: 130px;
    margin: 10px;
  }

  .qrborder {
    float: left;
    width: 150px;
    height: 150px;
    margin: -150px 0 0;
  }

  .agree {
    float: right;
    line-height: 16px;
    color: #2a2b2a;
    font-size: 12px;
    margin: 0 0 20px;
  }

  .link {
    color: #009C7F;
  }

  .link:hover {
    cursor: pointer;
  }

  /* 购买成功 */
  .buy {
    display: flex;
    flex-flow: column;
    align-items: center;
  }

  .buy img {
    width: 88px;
    height: 88px;
    margin: 55px 0 20px;
  }

  .buy_h1 {
    line-height: 26px;
    color: #009c7f;
    font-size: 20px;
    font-weight: 700
  }

  .buy_date {
    height: 19px;
    opacity: 1;
    color: #2a2b2a;
    font-size: 14px;
    font-weight: 400;
    margin: 31px 0 168px;
  }

  .buy_date span {
    color: #009C7F;
  }

  .alert_time {
    line-height: 20px;
    color: #666666;
    font-size: 14px;
    font-weight: 500;
    margin: 0 0 40px;
  }

  /* 退出弹窗 */
  .alert_bg {
    z-index: 88888;
    background: rgba(0, 0, 0, .7);
    position: fixed;
    width: 100%;
    height: 100vh;
    top: 0;
    left: 0;
  }

  .alert_box {
    z-index: 99999;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .quit_box {
    .alert_inner {
      width: 500px;
      border-radius: 20px;
      background: #ffffff;
      box-sizing: border-box;
      padding: 0 20px;
    }

    .alert_top {
      float: left;
      width: 100%;
      height: 54px;
      border-bottom: 1px solid #eee;
    }

    .alert_h1 {
      float: left;
      width: 100%;
      line-height: 54px;
      text-align: center;
      color: #2a2b2a;
      font-size: 16px;
      font-weight: bold;
    }

    .alert_x {
      float: right;
      width: 15px;
      height: 15px;
      padding: 19px;
      margin: -50px -18px 0 0;
    }

    .alert_x:hover {
      cursor: pointer;
    }

    .alert_wrap {
      width: 100%;
      float: left;
      display: flex;
      align-items: center;
      flex-flow: column;
    }

    .alert_tit {
      line-height: 21px;
      color: #2a2b2a;
      font-size: 16px;
      margin: 39px 0 16px;
    }

    .alert_btns {
      display: flex;
      margin: 20px 0 35px;
    }

    .alert_btns div {
      width: 122px;
      line-height: 38px;
      text-align: center;
      border-radius: 19px;
      font-size: 16px;
    }

    .alert_btns div:hover {
      cursor: pointer;
    }

    .alert_quit {
      color: #666666;
      background: #f5f5f5;
    }

    .alert_btns .alert_ok {
      color: #DD2A2A;
      background: #fee9e9 !important;
      margin: 0 0 0 34px;
    }
  }
</style>