<!-- 名师课堂-介绍 -->
<template>
  <div class="content">
    <div class="inner">
      <div class="wrap">
        <!-- 平板 -->
        <img src="@/assets/img/teachroom/xiaoxue.png" class="img" v-if="state.gradeId<=6" />
        <img src="@/assets/img/teachroom/chuzhong.png" class="img" v-else />
        <!-- h5 -->
        <!-- <img src="../../assets/img/teachroom/xiaoxue2.png" class="img" />
        <img src="../../assets/img/teachroom/chuzhong2.png" class="img" /> -->
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { reactive, onMounted } from 'vue';
  import { useUserStore } from "@/store/modules/user"

  defineOptions({
    name: "TeachRoomIntroduce"
  })

  const learnNow = useUserStore().learnNow
  const state : any = reactive({
    gradeId: learnNow.gradeId || 0
  })

  onMounted(() => {
    init()
  })

  // 属性赋值
  const setData = (obj : any) => {
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        state[key] = obj[key]
      }
    }
  }
  const init = () => {

  }
</script>

<style lang="scss" scoped>
  @import url('@/assets/styles/reset.css');

  .header_seat {
    width: 100%;
    height: 4.375rem;
    float: left;
  }

  .none {
    display: none !important
  }

  .nowrap {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .content {
    width: 100%;
    /* height: 100vh; */
    overflow-y: auto;
    background: #F5F5F5;
  }

  .inner {
    margin: 0 auto;
    width: 81.25rem;
  }

  .wrap {
    float: left;
    width: 100%;
  }

  .img {
    width: 100%;
    float: left;
  }
</style>
