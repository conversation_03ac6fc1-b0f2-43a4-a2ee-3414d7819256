<!-- 我的积分 -->
<template>
  <div class="content">
    <div class="header" style="width: 100%;;height: 4.375rem;">
      <topbar></topbar>
    </div>
    <div class="rule">
      <div class="inner">
        <div class="rule_back" @click="router.go(-1)">< 返回</div>
        <div class="rule_box">
          <div class="rule_lt">
            <img src="@/assets/img/user/cointip.png" />奖励规则
          </div>
          <div class="rule_rt">
            1.当日登录<span>+5</span>分；2.每做对1题<span>+3</span>分；3.每做错1题<span>+1</span>分；4.学习1个视频课程<span>+5</span>分；5.看一个知识点讲解<span>+1</span>分；6.学练时长每10分钟<span>+1</span>分
          </div>
        </div>
      </div>
    </div>
    <div class="wrap">
      <div class="inner">
        <div class="coin">
          <div class="learn_name">
            <img :src="state.learnNow.avatar" />
            <div class="learn_nick">
              <span class="nowrap">{{state.learnNow.nickName}}</span>
              <span>（{{state.learnNow.gradeName}}）</span>
            </div>
          </div>
          <div class="mybox">
            <div class="mycoin">
              <img src="@/assets/img/user/coin.png" />我的积分
            </div>
            <div class="mynum">{{state.info.surplusIntegral||0}}</div>
            <div class="mytotal">
              <div>累计积分：{{state.info.totalIntegral||0}}</div>
              <div>已兑换：{{state.info.exchangeIntegral||0}}</div>
            </div>
          </div>
          <div class="coin_h1">
            <div>日期</div>
            <div>积分</div>
            <div>累计积分</div>
          </div>
          <div class="coin_ul" v-if="state.info.integralInfos">
            <div class="coin_li" v-for="item in state.info.integralInfos" :key="item">
              <div>{{item.createTime}}</div>
              <div :class="item.type==1?'red':'green'">{{item.type==1?'-':'+'}}{{item.integral}}</div>
              <div>{{item.surplusIntegral}}</div>
            </div>
          </div>
        </div>
        <div class="rank">
          <div class="tabs">
            <div class="tab_li" :class="tab==1?'active':''" @click="tabs(1)">
              <div class="tab_img">
                <img src="@/assets/img/user/rank.png" />
                <div></div>
              </div>
              <div class="tab_p">全部排名</div>
            </div>
            <div class="tab_li" :class="tab==2?'active':''" @click="tabs(2)">
              <div class="tab_img">
                <img src="@/assets/img/user/rank.png" />
                <div></div>
              </div>
              <div class="tab_p">本机构排名</div>
            </div>
          </div>
          <div class="rank_h1">
            <div class="th1">排名</div>
            <div class="th2">头像</div>
            <div class="th3">昵称</div>
            <div class="th4">年级</div>
            <div class="th5">积分</div>
          </div>
          <!-- 有数据 -->
          <div class="rank_ul" v-if="state.isShow&&state.list.length">
            <div class="rank_li" v-for="(item,i) in state.list" :key="item">
              <div class="tr1">
                <img src="@/assets/img/user/top1.png" v-if="i==0" />
                <img src="@/assets/img/user/top2.png" v-else-if="i==1" />
                <img src="@/assets/img/user/top3.png" v-else-if="i==2" />
                <div v-else>{{item.ranking}}</div>
              </div>
              <div class="tr2">
                <img :src="item.avatar" />
              </div>
              <div class="tr3">{{item.userName}}</div>
              <div class="tr4">{{item.gradeId?gradeNameList[item.gradeId]:''}}</div>
              <div class="tr5">{{item.totalIntegral}}</div>
            </div>
          </div>
          <div class="nodata" v-else-if="state.isShow">
            <img src="@/assets/img/user/nodata.png" />暂无排名记录
          </div>
          <div class="rank_li rank_my" v-if="state.isShow">
            <div class="tr1">{{state.myRank.ranking||'未上榜'}}</div>
            <div class="tr2">
              <img :src="state.learnNow.avatar" />
            </div>
            <div class="tr3">{{state.learnNow.nickName}}</div>
            <div class="tr4">{{state.learnNow.gradeName}}</div>
            <div class="tr5">{{state.myRank.totalIntegral||0}}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { gradeNameList } from '../../utils/user/enum'
  import { reactive, ref, getCurrentInstance, onMounted } from "vue"
  import { useUserStore } from "@/store/modules/user"
  import { type FormInstance, FormRules, ElMessage } from "element-plus"
  import { integralInfoApi, integralListApi } from "@/api/user"
  import router from "@/router"
  import { useRoute } from "vue-router"
  import type { CascaderProps } from "element-plus"
  import topbar from '@/layout/topbar.vue'

  const { proxy } = getCurrentInstance() as any
  const loading = ref(false)
  const route = useRoute()
  const query = reactive<any>(route.query)
  let learnNow = localStorage.learnNow ? JSON.parse(localStorage.learnNow) : ''
  const state = reactive({
    isShow: false,
    gradeNameList,
    info: {
      totalIntegral: 0,
      exchangeIntegral: 0,
      surplusIntegral: 0,
      integralInfos: [
        {
          "integral": 0,
          "surplusIntegral": 0,
          "createTime": "",
          "type": 0
        }
      ]
    },
    learnNow,
    learnId: learnNow?.learnId || '',
    list: [
      {
        "learnUserId": "",
        "id": "",
        "orgId": null,
        "userName": "",
        "gradeId": 0,
        "avatar": "",
        "ranking": 0,
        "totalIntegral": 0
      }
    ],
    myRank: {
      "learnUserId": "",
      "id": "",
      "orgId": null,
      "userName": "",
      "gradeId": 0,
      "avatar": "",
      "ranking": 0,
      "totalIntegral": 0
    }
  })

  //初始化
  onMounted(() => {
    state.list = []
    integralInfo()
    integralList()
  })

  //我的积分
  const integralInfo = () => {
    const data = {
      learnUserId: state.learnId
    }
    integralInfoApi(data)
      .then((res : any) => {
        state.info = res.data || {}
      })
  }

  // tab菜单
  const tab = ref(1)
  const tabs = (num : any) => {
    tab.value = num
    integralList()
  }

  //排名
  const integralList = () => {
    const data = {
      learnUserId: state.learnId,
      isData: tab.value == 1 ? 0 : 1//ture:机构排名 false：全部排名
    }
    integralListApi(data)
      .then((res : any) => {
        state.isShow = true
        const list = res.data.learnUserIntegralVoBuilderList || []
        state.list = list
        // //匹配用户排名
        // if (list.length) {
        //   for (let i of list) {
        //     if (i.learnUserId == state.learnId) {
        //       state.myRank = i
        //     }
        //   }
        // }
        if (res?.data?.learnUserIntegralVoBuilder) {
          state.myRank = res?.data?.learnUserIntegralVoBuilder
        }
      })
      .catch(() => {
        state.isShow = true
        state.list = []
      })
  }
</script>

<style lang="scss" scoped>
  @import url('@/assets/styles/reset.css');

  .none {
    display: none !important
  }

  .nowrap {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .content {
    width: 100%;
    height: 100vh;
    overflow-y: auto;
    background: #F5F5F5;
  }

  .inner {
    margin: 0 auto;
    width: 81.25rem;
  }

  .inner div {
    float: left
  }

  /* 规则 */
  .rule {
    width: 100%;
    height: 5.75rem;
    background: url(@/assets/img/user/coinbg1.png) no-repeat;
    background-size: 120rem 5.75rem;
    box-sizing: border-box;
  }

  .rule_box {
    width: 100%;
    height: 2.875rem;
    border-radius: 1.4375rem;
    border: .0625rem solid #ffffff;
    background: #ffffffb3;
    box-sizing: border-box;
    padding: .625rem 1rem 0;
  }

  .rule_back {
    color: #2a2b2a;
    font-size: .875rem;
    line-height: 1.1875rem;
    margin: .6875rem 0 .375rem;
    cursor: pointer;
  }

  .rule_lt {
    color: #f79834;
    font-size: .875rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    margin: 0 1.875rem 0 0;
  }

  .rule_lt img {
    width: 1.4375rem;
    height: 1.4375rem;
    margin: 0 .375rem 0 0;
  }

  .rule_rt {
    color: #2a2b2a;
    font-weight: 400;
    line-height: 1.625rem;
    font-size: .875rem;
    position: relative;
    top: -0.25rem;
  }

  .rule_rt span {
    color: #F79834;
    font-weight: bold;
    font-size: 1.25rem;
  }

  /* 我的积分 */
  .wrap {
    width: 100%;
    height: calc(100vh - 10.125rem);
    background: url(@/assets/img/user/coinbg2.png) no-repeat;
    background-size: 120rem 38.1875rem;
    box-sizing: border-box;
  }

  .coin {
    width: 24rem;
    height: calc(100vh - 10.125rem);
    background: #fff;
    overflow-y: auto;
    display: flex;
    flex-flow: column;
    overflow-x: hidden;
  }

  .learn_name {
    width: 100%;
    height: 3.75rem;
    background: linear-gradient(90deg, #fdead6 0%, #ffffff 100%);
    border-left: .3125rem solid #f79834;
    margin: 1.25rem 0 1.875rem;
    box-sizing: border-box;
  }

  .learn_name img {
    float: left;
    width: 2.75rem;
    height: 2.75rem;
    border-radius: 50%;
    margin: .5rem .625rem;
  }

  .learn_nick {
    width: 18.75rem;
    line-height: 3.75rem;
    color: #2a2b2a;
    font-size: .875rem;
  }

  .learn_nick span {
    float: left;
    display: inline-block;
  }

  .learn_nick span:nth-child(1) {
    max-width: 13.75rem;
  }

  .mybox {
    width: 100%;
    padding: 0 0 1.25rem;
    border-bottom: .0625rem solid #eaeaea;
  }

  .mycoin {
    width: 100%;
    color: #f79834;
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-items: center;
    box-sizing: border-box;
    padding: 0 0 0 8.9375rem;
  }

  .mycoin img {
    width: 1.5rem;
    height: 1.5rem;
    margin: 0 .625rem 0 0;
  }

  .mynum {
    width: 100%;
    line-height: 4.125rem;
    color: #f79834;
    font-size: 3.125rem;
    font-weight: 700;
    text-align: center;
    margin: .375rem 0 .625rem;
  }

  .mytotal {
    width: 100%;
    box-sizing: border-box;
    padding: 0 2.6875rem;
    display: flex;
    justify-content: center;
  }

  .mytotal div {
    line-height: 1.9375rem;
    border-radius: .9688rem;
    background: #f5f5f5;
    padding: 0 .75rem;
    color: #2a2b2a;
    font-size: 1rem;
    white-space: nowrap;
  }

  .mytotal div:nth-child(1) {
    margin: 0 1.875rem 0 0;
  }

  .coin_h1 {
    width: 22.75rem;
    height: 3.0625rem;
    border-radius: .625rem .625rem 0 0;
    border: .0625rem solid #eaeaea;
    background: #f5f5f5;
    box-sizing: border-box;
    margin: 1.1875rem 0 0 .625rem;
  }

  .coin_h1 div {
    color: #2a2b2a;
    font-size: 1rem;
    font-weight: bold;
    line-height: 1.3125rem;
    margin: .875rem 0;
    box-sizing: border-box;
  }

  .coin_h1 div:nth-child(1) {
    width: 8.0625rem;
    padding: 0 0 0 .625rem;
    border-right: .0625rem solid #ddd;
  }

  .coin_h1 div:nth-child(2) {
    width: 8.6875rem;
    padding: 0 0 0 1.25rem;
    border-right: .0625rem solid #ddd;
  }

  .coin_h1 div:nth-child(3) {
    padding: 0 .625rem 0 0;
    text-align: right;
    float: right !important;
  }

  .coin_ul {
    width: 100%;
    box-sizing: border-box;
    padding: 0 .625rem;
  }

  .coin_li {
    width: 100%;
    border-bottom: .0625rem dashed #eaeaea;
  }

  .coin_li div {
    color: #2a2b2a;
    font-size: .875rem;
    color: #2a2b2a;
    font-size: .875rem;
    line-height: 3.4375rem;
    box-sizing: border-box;
  }

  .coin_li div:nth-child(1) {
    width: 8.0625rem;
    padding: 0 0 0 .625rem;
  }

  .coin_li div:nth-child(2) {
    width: 8.6875rem;
    padding: 0 0 0 1.25rem;
    font-weight: bold;
  }

  .coin_li div:nth-child(3) {
    padding: 0 .625rem 0 0;
    text-align: right;
    float: right !important;
  }

  .coin_li .green {
    color: #009C7F;
  }

  .coin_li .red {
    color: #DD2A2A;
  }

  /* 排名 */
  .rank {
    width: 56.625rem;
    height: calc(100vh - 10.125rem);
    background: #fff;
    margin: 0 0 0 .625rem;
    display: flex;
    flex-flow: column;
  }

  .tabs {
    width: 100%;
    box-sizing: border-box;
    padding: 2.5rem 15.25rem 1.6875rem;
  }

  .tab_li {
    width: 7.4375rem;
    height: 1.8125rem;
  }

  .tab_li:nth-child(2) {
    width: 8.6875rem;
    margin: 0 0 0 6.9375rem;
  }

  .tab_img {
    width: 100%;
    height: 1.8125rem;
    opacity: 0;
  }

  .tab_li.active .tab_img {
    opacity: 1;
  }

  .tab_img img {
    float: left;
    width: 2rem;
    height: 2rem;
    position: relative;
    z-index: 2;
  }

  .tab_img div {
    width: 5.4375rem;
    height: .8125rem;
    background: #f79834;
    margin: .5rem 0 0 -0.1875rem;
  }

  .tab_li:nth-child(2) .tab_img div {
    width: 6.6875rem;
  }

  .tab_p {
    width: 6.25rem;
    color: #2a2b2a;
    font-size: 1.25rem;
    font-weight: 700;
    line-height: 1.8125rem;
    margin: -1.8125rem 0 0 2.0625rem;
  }

  .tab_li:hover {
    cursor: pointer;
  }

  .rank_h1 {
    width: 55.375rem;
    height: 3.0625rem;
    border-radius: .625rem .625rem 0 0;
    border: .0625rem solid #eaeaea;
    background: #f5f5f5;
    margin: 0 0 0 .625rem;
  }

  .rank_h1 div {
    color: #2a2b2a;
    font-size: 1rem;
    font-weight: bold;
    line-height: 1.3125rem;
    margin: .875rem 0;
    padding: 0 0 0 1.25rem;
    box-sizing: border-box;
    border-right: .0625rem solid #ddd;
  }

  .th1 {
    width: 8.875rem;
  }

  .th2 {
    width: 10.75rem;
  }

  .th3 {
    width: 15.6875rem;
  }

  .th4 {
    width: 11.6875rem;
  }

  .th5 {
    width: 8.125rem;
    border: 0 !important;
  }

  .rank_ul {
    flex: 1;
    width: 100%;
    box-sizing: border-box;
    padding: 0 .625rem;
    overflow-y: auto;
  }

  .rank_li {
    width: 100%;
    border-bottom: .0625rem dashed #eaeaea;
  }

  .rank_li>div {
    height: 4rem;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding: 0 0 0 1.25rem;
    color: #2a2b2a;
    font-size: .875rem;
  }

  .tr1 {
    width: 8.875rem;
  }

  .tr1 div {
    width: 1.75rem;
    text-align: center;
  }

  .tr1 img {
    width: 1.75rem;
    height: 1.3125rem;
    margin: 0 0 0 .1875rem;
  }

  .tr2 {
    width: 10.75rem;
  }

  .tr2 img {
    width: 2.75rem;
    height: 2.75rem;
    border-radius: 50%;
  }

  .tr3 {
    width: 15.6875rem;
  }

  .tr4 {
    width: 11.6875rem;
  }

  .tr5 {
    color: #f79834 !important;
    font-weight: 700;
  }

  .nodata {
    flex: 1;
    color: #999999;
    font-size: .875rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-flow: column;
  }

  .nodata img {
    width: 7.4375rem;
    height: 8rem;
    margin: 0 0 .625rem;
  }

  .rank_my {
    width: 100%;
    border-radius: .625rem .625rem 0 0;
    background: url(@/assets/img/user/mybg.png) no-repeat;
    background-size: 100% 5.875rem;
    box-sizing: border-box;
    border: 0;
    padding: 0 .625rem;
  }
</style>
