<template>
  <div class="dashboard-container">
    <!-- Loading 状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-content">
        <div class="spinner"></div>
        <p class="loading-text">正在加载用户信息...</p>
      </div>
    </div>
    
    <!-- 错误状态 -->
    <div v-else-if="error" class="error-overlay">
      <div class="error-content">
        <h3>加载失败</h3>
        <p>{{ error }}</p>
        <el-button type="primary" @click="retryLoad">重试</el-button>
      </div>
    </div>
    
    <!-- 正常内容 -->
    <div v-else>
      <!-- <h1>Welcome to the Home Page</h1>
      <el-button @click="logout">Logout</el-button> -->
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from "@/store/modules/user"
import { type FormInstance, FormRules, ElMessage, ElButton } from "element-plus"
import { pwdLoginApi, sendCodeApi, registerApi } from "@/api/login"
import { userGetAllApi } from "@/api/user"
import { gradeNameList } from '@/utils/user/enum'
import router from "@/router"

const route: any = useRoute()
const userStore = useUserStore()

// 响应式状态
const loading = ref<boolean>(false)
const error = ref<string>('')

const checkToken = (): void => {
  // const token = useUserStore().token, learnUsers = useUserStore().learnUsers
  // if (token) {
  //   if (learnUsers.length) {
  //     router.replace({ name: 'KnowledgeGraph' })
  //   } else {
  //     router.push({ name: "UserAdd", query: { pageType: 'add' } })
  //   }
  // } else {
  //   router.replace({ name: 'Login' })
  // }
  if (route.query?.token) { 
    // getSySToken(route.query?.token).thne(res => { 
    //   if (res.code == 200) { 
    //     const curData=res.data
    //     const loginData = {
    //       "token" :curData?.token,
    //       "familyId" : null,
    //       "sysUserId" : curData?.sysUserId,
    //       "name" : null,
    //       "sex" : null,
    //       "avatar" : null,
    //       "email" : null,
    //       "phone" : null,
    //       "learnId" : curData?.learnId
    //     }
    //       userGetAll(loginData)
    //       }
    //     })
    userGetAll()
    localStorage.token = route.query?.token
    userStore.token = route.query?.token
    // userStore.agencyName = route.query?.agencyName
  } else {
    error.value = '缺少必要的登录凭证'
  }
}

// 获取用户列表
const userGetAll = async (): Promise<void> => {
  try {
    loading.value = true
    error.value = ''
    
    const res: any = await userGetAllApi()
    
    if (res.code === 200) {
      const data = res.data
      const userInfo = JSON.stringify(data)
      const sysUserId = data.sysUserId
      // const orgName = data.orgName
      //  localStorage.userInfo?.orgName

      localStorage.sysUserId = sysUserId
      localStorage.userInfo = userInfo
      // localStorage.orgName = orgName
      userStore.sysUserId = sysUserId
      userStore.userInfo = userInfo
      
      const arr: any[] = [res.data]
      console.log("arr---", arr)
      
      if (arr.length > 0) {
        // 判断学习用户数
        arr.forEach((item: any) => {
          item.gradeName = gradeNameList[item.gradeId]
        })
        
        localStorage.learnUsers = JSON.stringify(arr)
        let usersNow = localStorage.learnNow ? JSON.parse(localStorage.learnNow) : null
        
        if (usersNow) {
          // 匹配当前学生是否在学生列表
          let isFound = false
          for (const i of arr) {
            if (usersNow.learnId == i.learnId) {
              isFound = true
              break
            }
          }
          if (!isFound) {
            // 没匹配到，默认第一个
            arr[0].isDefault = true
            usersNow = arr[0]
          }
        } else {
          // 当前没学生，默认第一个
          let hasDefault = false
          for (const i of arr) {
            if (i.isDefault) {
              // 有默认时
              hasDefault = true
              usersNow = i
              break
            }
          }
          if (!hasDefault) {
            arr[0].isDefault = true
            usersNow = arr[0]
          }
        }
        
        // 缓存选中学科
        // 缓存学生列表
        userStore.learnUsers = localStorage.learnUsers
        userStore.setlearnNow(usersNow)
        
        // 跳首页
        await router.replace({ name: "KnowledgeGraph" })
        ElMessage.success('登录成功')
      } else {
        // 跳新增学习用户
        await router.push({ name: "UserAdd", query: { pageType: 'add' } })
        ElMessage.info('请先添加学习用户')
      }
    } else {
      throw new Error(res.message || '获取用户信息失败')
    }
  } catch (err: any) {
    console.error('获取用户信息失败:', err)
    error.value = err.message || '网络错误，请稍后重试'
    ElMessage.error(error.value)
  } finally {
    loading.value = false
  }
}

// 重试加载
const retryLoad = (): void => {
  checkToken()
}

// 组件挂载时执行
onMounted(() => {
  checkToken()
})
</script>

<style lang="scss" scoped>
.dashboard-container {
  position: relative;
  width: 100%;
  height: 100vh;
}

.loading-overlay, .error-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.25rem;
}

.spinner {
  width: 3.125rem;
  height: 3.125rem;
  border: 0.25rem solid rgba(255, 255, 255, 0.3);
  border-top: 0.25rem solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #ffffff;
  font-size: 1rem;
  font-weight: 500;
  margin: 0;
  text-align: center;
}

.error-content {
  background: #ffffff;
  padding: 2rem;
  border-radius: 0.5rem;
  box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 25rem;
  
  h3 {
    color: #e74c3c;
    margin: 0 0 1rem 0;
    font-size: 1.25rem;
  }
  
  p {
    color: #666666;
    margin: 0 0 1.5rem 0;
    line-height: 1.5;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .error-content {
    margin: 1rem;
    padding: 1.5rem;
    max-width: none;
  }
  
  .loading-text {
    font-size: 0.875rem;
  }
  
  .spinner {
    width: 2.5rem;
    height: 2.5rem;
  }
}
</style>