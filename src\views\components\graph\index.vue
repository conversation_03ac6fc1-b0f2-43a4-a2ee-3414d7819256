<!-- px转化为rem需手动计算，逻辑暂时还没写 -->
<template>
  <div class="scroll-wrapper" :style="styles" ref="viewport" v-loading="loading"
  element-loading-text="正在生成中...">
    <div
      class="random-container"
      ref="container"
      :style="{
        width: `${containerWidth}px`,
        height: `${containerHeight}px`,
        transform: `translate(${offset.x}px, ${offset.y}px)`
      }"
      @mousedown="startDrag"
      @touchstart.passive="startDrag"
    >
      <div
        v-for="(icon, index) in icons"
        :key="icon.id || index"
        class="random-icon"
        @click="handlePossibleClick(icon, positions[index]?.x, positions[index]?.y)"
        @mousedown="startDrag"
        @touchstart.passive="startDrag"
        :style="{
          left: `${positions[index]?.x}px`,
          top: `${positions[index]?.y}px`
        }"
      >
        <div class="icon" v-if="!isSecond">
          <img :src="getUrl(icon)" alt="">
          <p>{{ icon.name }}</p>
        </div>
        <el-popover placement="right" popper-class="el-popover-graph" trigger="click">
          <template #reference>
            <div v-if="isSecond" class="icon">
              <img :src="getUrl(icon)" alt="">
              <p>{{ icon.name }}</p>
            </div>
          </template>
          <div class="popover-content" :class="icon.status == 1 ? 'green-bg' : icon.status == 2 ? 'yellow-bg' : icon.status == 3 ? 'red-bg' : 'gray-bg'">
            <div class="popover-content-top">
              <div>{{ icon.name }}</div>
              <span>掌握度仅计算最近练习的10道题</span>
            </div>
            <div class="popover-content-middle">
              <div class="popover-content-middle-item">
                <span>{{ icon.total?Number(icon.total).toFixed(0):0 }}</span>
                <div>已做题</div>
              </div>
              <div class="popover-content-middle-item">
                <span>{{ icon.correct?Number(icon.correct).toFixed(0):0 }}</span>
                <div>做对</div>
              </div>
              <div class="popover-content-middle-item">
                <span>{{ icon.mistake?Number(icon.mistake).toFixed(0):0 }}</span>
                <div>做错</div>
              </div>
              <div class="popover-content-middle-item">
                <span>{{ icon.correctRate?(icon.correctRate * 100).toFixed(0):0 }}</span>
                <div>掌握度(%)</div>
              </div>
            </div>
            <div class="popover-content-bottom">
              <div class="underline-text" v-if="icon.status !=undefined && [1,2,3].includes(icon.status)" @click="goDetail(icon.id)">查看答题 > </div>
              <!-- <div class="popover-content-bottom-btn" @click="persicionLearn(icon)">
                <div v-if="step == 1"> 弱项检测 </div>
                <div v-if="step == 2">
                  <img src="@/assets/img/percision/play.png" alt="">
                  针对学习
                </div>
                <div v-if="step == 4"> 阶段测试 </div>
                <div v-if="step == 5"> 错题消化 </div>
              </div> -->
            </div>
          </div>
        </el-popover>
      </div>
    </div>
  </div>

</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, PropType, reactive, watch } from 'vue'
import { useRouter } from 'vue-router'
import { dataEncrypt } from "@/utils/secret"
import { detailsToPointApi } from '@/api/training'
const router = useRouter()
interface Icon {
  id?: number | string;
  icon: string;
  total: number;
  mistake: number;
  correct: number;
  correctRate: number;
  name: string;
  status?: number;
}

const props = defineProps({
  icons: {
    type: Array as PropType<Icon[]>,
    required: true
  },
  sourceId: {
    type: String,
    default: ""
  },
  isSecond: {
    type: Boolean,
    default: false
  },
  step: {
    type: Number,
    default: 1
  },
  iconSize: {
    type: Number,
    default: 40
  },
  padding: {
    type: Number,
    default: 20
  },
  styles: {
    type: String,
    default: "width: 66.125rem;height: 30.3125rem"
  }
})
watch(
  () => [...props.icons],
  () => {
    nextTick(()=>{
      init()
    })
  },
  { immediate: true, deep: true }
)
const emit = defineEmits(['startLearn'])

const viewport = ref<HTMLElement>()
const container = ref<HTMLElement>()
const positions = ref<Array<{x: number, y: number}>>([])
const isDragging = ref(false)
const offset = ref({ x: 0, y: 0 })
const startDragPos = ref({ x: 0, y: 0 })
const startMousePos = ref({ x: 0, y: 0 })
const isClick = ref(false); // 判断是否是点击而非拖动
const loading = ref(false)
// 图标实际占位尺寸
const iconWidth = 224
const iconHeight = 89

const dialogState = reactive({
  visible: false,
  status: 1,
  x: 0,// 弹窗位置
  y: 0
})

const getUrl = (item: Icon) => {
  let url = item.icon
  if (item.status == 1) {
    url = '_grasp'
  } else if (item.status == 2) {
    url = '_generally'
  } else if (item.status == 3) {
    url = '_ungrasp'
  } else {
    url = '_untest'
  }
  return new URL(`../../../assets/img/percision/${url}.png`, import.meta.url).href
}

// 检测两个矩形是否重叠
const checkOverlap = (rect1: {x: number, y: number}, rect2: {x: number, y: number}) => {
  return !(
    rect1.x + iconWidth < rect2.x ||
    rect1.x > rect2.x + iconWidth ||
    rect1.y + iconHeight < rect2.y ||
    rect1.y > rect2.y + iconHeight
  )
}

// 检查新位置是否与已有位置重叠
const isValidPosition = (newPos: {x: number, y: number}, existingPositions: Array<{x: number, y: number}>) => {
  for (const pos of existingPositions) {
    if (checkOverlap(newPos, pos)) {
      return false
    }
  }
  return true
}
// 点击节点
const handleClick = (item: Icon, x: number, y: number) => {
  dialogState.visible = true
  dialogState.status = Number(item.status)
  dialogState.x = x
  dialogState.y = y
}
const persicionLearn = (icon:any) => {
  emit('startLearn',icon)
}
const goDetail = (pointId) => {
  // 生成诊断报告弹窗暂未加上
  loading.value = true
  detailsToPointApi({pointId: pointId}).then((res: any) => {
        if (res.code == 200) {
            const data = res.data
            localStorage.setItem('diagnosticReport', JSON.stringify(data))
            loading.value = false
            router.push({
                path: '/ai_percision/knowledge_graph_detail/training_report',
                query: {
                    data: dataEncrypt({
                        sourceId: props.sourceId,
                        showStep: '0',
                        isShow:1
                    })
                }
            })
        }
    }).catch((error) => {
      loading.value = false
    })
}
// 获取训练报告数据
const getTringDetail = (trainingId: string) => {

}

const generateRandomPositions = () => {
  if (!viewport.value) return
  const vw = viewport.value.clientWidth
  const vh = viewport.value.clientHeight
  const newPositions: Array<{x: number, y: number}> = []
  const maxAttempts = 100 // 每个图标最多尝试100次寻找不重叠位置

  for (let i = 0; i < props.icons.length; i++) {
    let attempts = 0
    let positionFound = false
    let newPos

    while (attempts < maxAttempts && !positionFound) {
      newPos = {
        x: Math.random() * (containerWidth.value - iconWidth),
        y: Math.random() * (containerHeight.value - iconHeight)
      }

      if (isValidPosition(newPos, newPositions)) {
        positionFound = true
        newPositions.push(newPos)
      }
      attempts++
    }

    // 如果尝试多次仍找不到不重叠位置，强制放置并记录警告
    if (!positionFound) {
      // console.warn(`无法为图标 ${i} 找到不重叠位置，强制放置`)
      newPositions.push({
        x: Math.random() * (containerWidth.value - iconWidth),
        y: Math.random() * (containerHeight.value - iconHeight)
      })
    }
  }
  positions.value = newPositions

  // 初始居中显示
  offset.value = {
    x: (vw - containerWidth.value) / 2,
    y: (vh - containerHeight.value) / 2
  }
}

const startDrag = (event: MouseEvent | TouchEvent) => {
  isDragging.value = false; // 先设为 false，等待移动检测

  // 记录初始拖动位置
  startDragPos.value = { ...offset.value }

  // 获取鼠标/触摸初始位置
  let clientX: number;
  let clientY: number;

  if ('touches' in event && event.touches[0]) { // 判断是否为 TouchEvent
    clientX = event.touches[0].clientX;
    clientY = event.touches[0].clientY;
  } else if ('clientX' in event && 'clientY' in event) { // 判断是否为 MouseEvent
    clientX = event.clientX;
    clientY = event.clientY;
  } else {
    return; // 如果事件类型不符合预期，直接返回
  }
  startMousePos.value = { x: clientX, y: clientY }
  isClick.value = true; // 假设是点击，直到检测到移动

  // 阻止默认行为和冒泡
  event.preventDefault()
  event.stopPropagation()

  // 添加事件监听
  document.addEventListener('mousemove', handleDragMove)
  document.addEventListener('touchmove', handleDragMove, { passive: false })
  document.addEventListener('mouseup', handleDragEnd)
  document.addEventListener('touchend', handleDragEnd)
}
const handleDragMove = (event: MouseEvent | TouchEvent) => {
  let clientX: number;
  let clientY: number;

  if ('touches' in event && event.touches[0]) { // 判断是否为 TouchEvent
    clientX = event.touches[0].clientX;
    clientY = event.touches[0].clientY;
  } else if ('clientX' in event && 'clientY' in event) { // 判断是否为 MouseEvent
    clientX = event.clientX;
    clientY = event.clientY;
  } else {
    return; // 如果事件类型不符合预期，直接返回
  }

  // 如果移动距离超过 .3125rem，视为拖动而非点击
  const moveX = Math.abs(clientX - startMousePos.value.x);
  const moveY = Math.abs(clientY - startMousePos.value.y);

  if (moveX > 5 || moveY > 5) {
    isClick.value = false; // 不是点击，是拖动
    isDragging.value = true;
  }

  if (isDragging.value) {
    const dx = clientX - startMousePos.value.x;
    const dy = clientY - startMousePos.value.y;
    offset.value = {
      x: startDragPos.value.x + dx,
      y: startDragPos.value.y + dy,
    };
    event.preventDefault();
  }
};

const handleDragEnd = () => {
  document.removeEventListener('mousemove', handleDragMove);
  document.removeEventListener('touchmove', handleDragMove);
  document.removeEventListener('mouseup', handleDragEnd);
  document.removeEventListener('touchend', handleDragEnd);

  // if (isClick.value) {

  // }
  isDragging.value = false;
};
const handlePossibleClick = (icon: Icon, x?: number, y?: number) => {
  if (x === undefined || y === undefined) {
    console.warn('Position is undefined for icon:', icon);
    return;
  }
  if (isClick.value) {
    handleClick(icon, x, y); // 只有是点击时才触发
  }
};
const handleDrag = (event: MouseEvent | TouchEvent) => {
  if (!isDragging.value) return

  let clientX: number;
  let clientY: number;

  if ('touches' in event && event.touches[0]) { // 判断是否为 TouchEvent
    clientX = event.touches[0].clientX;
    clientY = event.touches[0].clientY;
  } else if ('clientX' in event && 'clientY' in event) { // 判断是否为 MouseEvent
    clientX = event.clientX;
    clientY = event.clientY;
  } else {
    return; // 如果事件类型不符合预期，直接返回
  }

  // 计算移动距离
  const dx = clientX - startMousePos.value.x
  const dy = clientY - startMousePos.value.y

  // 更新偏移量
  offset.value = {
    x: startDragPos.value.x + dx,
    y: startDragPos.value.y + dy
  }

  event.preventDefault()
}

const endDrag = () => {
  isDragging.value = false
  isClick.value = false;   // 确保重置点击状态

  // 移除事件监听
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('touchmove', handleDrag)
  document.removeEventListener('mouseup', endDrag)
  document.removeEventListener('touchend', endDrag)
}

// 计算容器大小
const containerWidth = ref(0)
const containerHeight = ref(0)

const calculateContainerSize = () => {
  if (!viewport.value) return

  // 计算需要的行数和列数
  const iconArea = iconWidth * iconHeight
  const totalArea = props.icons.length * iconArea * 2 // 乘以2以确保有足够空间
  const aspectRatio = iconWidth / iconHeight

  // 计算理想容器尺寸
  let width = Math.sqrt(totalArea * aspectRatio)
  let height = totalArea / width

  // 确保不小于视口大小
  containerWidth.value = Math.max(viewport.value.clientWidth, width)
  containerHeight.value = Math.max(viewport.value.clientHeight, height)
}

const init = () => {
  calculateContainerSize()
  generateRandomPositions()
}

onMounted(() => {
  // nextTick(() => {
  //   init()
  // })
  window.addEventListener('resize', init)
})
</script>

<style lang="scss" scoped>
.scroll-wrapper {
  overflow: hidden;
  position: relative;
  cursor: grab;
  user-select: none;
}

.scroll-wrapper:active {
  cursor: grabbing;
}

.random-container {
  position: absolute;
  will-change: transform;
}

.random-icon {
  position: absolute;
  width: 14rem;
  height: 5.5625rem;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  user-select: none;
  pointer-events: auto;
  transition: transform 0.2s ease, opacity 0.2s ease;
}

.random-icon:hover {
  transform: scale(1.1) !important;
  opacity: 1 !important;
}

.icon {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  img {
    width: 2.5rem;
    height: 2.5rem;
  }

  p {
    font-size: .875rem;
    color: #2a2b2a;
    max-width: 14rem;
    text-align: center;
    margin-top: .3125rem;
  }
}

</style>
<style lang="scss">
  .el-popover-graph {
    width: 34.8125rem!important;
    height: 23.1875rem;
    border-radius: 1.25rem 1.25rem 1.25rem 1.25rem!important;
    padding: 0!important;
    .green-bg {
      background-image: url(@/assets/img/percision/cantraled.png)!important;
    }
    .yellow-bg {
      background-image: url(@/assets/img/percision/generally.png)!important;
    }
    .red-bg {
      background-image: url(@/assets/img/percision/uncantral.png)!important;
    }
    .gray-bg {
      background-image: url(@/assets/img/percision/untest.png)!important;
    }
    .popover-content{
      background-size: cover!important;
      background-repeat: no-repeat;
      padding-top: 4.25rem;
      &-top {
        margin: 0 2.5rem 2.25rem 2.5rem;
        div {
          color: #2a2b2a;
          font-size: 1.25rem;
          font-weight: 700;
          margin-bottom: .625rem;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          vertical-align: bottom;
        }
        span {
          color: #666666;
          font-size: .75rem;
          font-weight: 400;
        }
      }
      &-middle {
        display: flex;
        justify-content: space-between;
        margin: 0rem 2.5rem 2.25rem 3.75rem;
        &-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          span {
            color: #2a2b2a;
            font-size: 3.125rem;
            font-weight: 700;
          }
          div {
            color: #2a2b2a;
            font-size: .875rem;
            font-weight: 400;
            margin-top: .3125rem;
          }
        }
      }
      &-bottom {
        display: flex;
        justify-content: center;
        align-items: center;
        .underline-text {
          text-decoration: underline;
          color: #666666;
          cursor: pointer;
        }
        &-btn {
          div{
            margin-left: 3.125rem;
            width: 7rem;
            cursor: pointer;
            height: 2.125rem;
            line-height: 2.125rem;
            text-align: center;
            background: url(@/assets/img/percision/learn-btn-bg.png);
            background-size: 7rem 2.125rem;
            background-repeat: no-repeat;
            color: #ffffff;
            display: flex;
            align-items: center;
            justify-content: center;
            img {
              width: 1.25rem;
              height: 1.25rem;
              margin-right: .3125rem;
            }
          }
        }
      }
    }
  }
</style>
