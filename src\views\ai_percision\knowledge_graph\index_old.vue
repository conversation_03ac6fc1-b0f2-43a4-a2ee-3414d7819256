<template>
  <div class="stap">
      <img src="@/assets/img/note/stap.png" class="stap_lt" />
      <img src="@/assets/img/note/stap.png" class="stap_rt" />
    </div>
  <div class="graph-content">

    <div class="left">
      <subjectSelect :selected="subject" @setSubject="setSubject" />
    </div>
     <!-- v-loading="loading" -->
    <div class="right" :class="isWen?'bg-notext':(pageStatus ? '' : 'test-bgimg')">
      <div v-if="!isWen">
        <div class="book-vision mgL">
          <img src="@/assets/img/percision/switch.png" />
          <div class="book-vision-text">
            <span>当前教材：</span>
            {{ bookVersionName }}
          </div>
        </div>
        <div class="cascader-box">
          <el-cascader v-model="selectchapterId" :popper-append-to-body="false" placeholder="请选择" ref="cascaderRef" :options="options" :props="props" @change="handleChange" />
        </div>
        <div v-if="pageStatus">
          <div class="graph-box">
            <graph :icons="testList" styles="width: 66.125rem;height: 30.3125rem" :icon-size="50" />
          </div>
          <div class="start-learn">
            <img @click="goNext('/ai_percision/knowledge_graph_detail')" src="@/assets/img/percision/start_learn.png" alt="">
            <el-divider border-style="dashed" />
            <div class="lefend-box">
              <div class="lefend-box-tip">图例</div>
              <div class="lefend-box-item">
                掌握<span class="green"></span>
              </div>
              <div class="lefend-box-item">
                一般<span class="origin"></span>
              </div>
              <div class="lefend-box-item">
                未掌握<span class="red"></span>
              </div>
              <div class="lefend-box-item">
                未测<span class="grey"></span>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="test-box">
          <div class="test-wrap" v-for="item in testList2">
            <div class="test-box-item">
              <div class="test-box-item-img">
                <span class="red-text" v-if="item.score&&item.score!='0'">{{item.score}}分</span>
              </div>
              <div class="test-box-item-info">
                <div class="test-box-item-info-title">
                  {{item.title}}
                </div>
                <div class="test-box-item-info-data">
                  <div>更新时间：{{item.reportDate}}</div>
                  <div>浏览：{{ item.viewCount }}</div>
                </div>
              </div>
              <div class="test-box-item-btn">
                <div class="test-box-item-btn-it btn" @click="handleDownload(item)">
                  <img src="@/assets/img/percision/download.png" alt=""> 下载
                </div>
                <div class="test-box-item-btn-it blue-text" @click="testDetail(item)">
                  查看详情>
                </div>
              </div>
            </div>
            <div class="hui-line"></div>
          </div>
          <img @click="goNext('/ai_percision/knowledge_graph_detail')" class="learn-img" src="@/assets/img/percision/start_learn.png" alt="">
          <div class="pagination-box" v-if="testList2.length">
            <Pagination
              :total="pageData.total"
              :current="pageData.current"
              @currentSizeChange="currentSizeChange"
              @pageClick="pageClick"/>
          </div>
        </div>
      </div>
      <div v-else>
        <div class="book-vision mg25">
          <div class="book-vision-text">
            <span>当前教材：</span>
            {{ bookVersionName }}
          </div>
        </div>
        <div class="model-box">
          <div class="model-box-item">
            <div class="model-box-item-title">知识点扫雷</div>
            <div class="model-box-item-tip">当前教材：{{ bookVersionName }}</div>
            <div class="model-box-item-imgs">
              <img src="@/assets/img/percision/saolei1.png" />
              <img src="@/assets/img/percision/saolei2.png" />
              <img src="@/assets/img/percision/saolei3.png" />
              <img src="@/assets/img/percision/saolei4.png" />
            </div>
            <div class="model-box-item-btn" @click="goNext('/ai_percision/minesweeper')">开始</div>
          </div>
          <div class="model-box-item">
            <div class="model-box-item-title">去练习</div>
            <div class="model-box-item-tip">弱项练习大闯关</div>
            <div class="model-box-item-img">
              <img src="@/assets/img/percision/practice-img.png" />
            </div>
            <div class="model-box-item-btn" @click="goNext('/ai_percision/to_practice')">开始</div>
          </div>
          <div class="model-box-item">
            <div class="model-box-item-title">去测评</div>
            <div class="model-box-item-tip">单元测试/期中期末测试</div>
            <div class="model-box-item-img">
              <img src="@/assets/img/percision/assess-img.png" />
            </div>
            <div class="model-box-item-btn" @click="goNext('/ai_percision/go_evaluate')">开始</div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 购买会员弹窗 -->
  <buyVip :show="showVip"  @close="quitHide"></buyVip>
  <!-- 下载试卷 -->
  <downloadTrestDialog v-if="dialogVisible" ref="downloadTrestDialogRef" :paper-detail="dowmloadData" />
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'
import subjectSelect from "@/views/components/subjectSelect/index.vue"
import { getChapterListApi, getpointListApi } from "@/api/book"
import { getChapterReportListApi } from "@/api/report"
import graph from "@/views/components/graph/index.vue"
import { computed, nextTick, onMounted, reactive, ref } from 'vue'
import { useUserStore } from "@/store/modules/user"
import { storeToRefs } from 'pinia'
import { dataEncrypt } from "@/utils/secret"
import buyVip from "@/views/components/buyVip/index.vue"
import { useRouteStoreHook } from '@/store/modules/route'
import downloadTrestDialog from "@/components/TruePaperDownload/index.vue"
//tabbar选中
useRouteStoreHook().setSelectMenu('AiPercision')
const userStore = useUserStore()
const wenke = [11, 12, 14, 24, 26, 27, 28, 29, 35, 36, 37, 38, 39]

let { subjectObj, learnNow,chapterObj } = storeToRefs(userStore)
const router = useRouter()
const pageData = reactive({
  total: 0,
  current: 1,
  size: 10
})
const chapterId = ref(chapterObj.value.chapterId || "")
let selectchapterId = ref([] as any[])
const pageStatus = ref(true)
const loading = ref(true)
const showVip = ref(false)

const options = ref(userStore.chapterList || [])
const cascaderRef = ref()
const props = {
  value: 'chapterId',
  label: 'chapterName',
  children: 'children'
}
const testList = ref([] as any)
const testList2 = ref([] as any)
// p判断所选章节ID是否与章节列表匹配
const hasChapterId = hasChapterIdInTree(options.value, chapterId.value)
const subject = computed(() => {
  return subjectObj.value.subject
})

const isWen = computed(()=>{
  return wenke.includes(subjectObj.value.id)
})
const bookVersionName = computed(() => {
  return subjectObj.value.editionName + learnNow.value.gradeName + (subjectObj.value.termName?subjectObj.value.termName:"")
})
onMounted(async () => {
  //处理编辑教材版本后，有章节缓存问题
  subjectObj.value = JSON.parse(localStorage.subjectObj||'{}')
  learnNow.value = JSON.parse(localStorage.learnNow||'{}')
  chapterObj.value = JSON.parse(localStorage.chapterObj||'{}')
  chapterId.value = chapterObj.value.chapterId || ""
  if (chapterId.value == "" || !hasChapterId) {
    await getChapterList()
  } else {
    selectchapterId.value = getArrId(userStore.chapterList, chapterObj.value.chapterId).chapterIdAll
    //判断children子章节与父级id一样,(小学有)
    let val = selectchapterId.value
    if(val.length==1){
      if(chapterObj.value.chapterName.indexOf('单元测试')>-1){
        selectchapterId.value.push(val[0]+'单元测试')
      }else{
        selectchapterId.value.push(val[0]+'章节')
      }
    }
  }
  await getChapterReportList()
})
const quitHide = async () => {
  showVip.value = false
  if(chapterId.value == "" || !hasChapterId) {
    await getChapterList()
  }
  await getChapterReportList()
}
// 判断会员状态
const getIsMember = () => {
    const isVip = useUserStore().memberInfo
    if (isVip) {
      return true
    } else {
      showVip.value = true
      return false
    }
  }
// 扁平化树结构为id映射表（一次性操作，适合频繁查找）
function hasChapterIdInTree(data: any,targetChapterId: any) {
  if (!data) return false;

  const nodes = Array.isArray(data) ? data :
               (typeof data === 'object' ? [data] : []);

  return nodes.some(node => {
    if (node?.chapterId === targetChapterId) return true;
    if (node?.children) {
      return hasChapterIdInTree(node.children, targetChapterId);
    }
    return false;
  });
}
//获取章节知识点树
const getChapterList = async() => {
  loading.value = true
  try {
    const res: any = await getChapterListApi({
      bookId: subjectObj.value.bookId
    })
    loading.value = false
    if (res.code == 200) {
      // 处理单元测试章节id重复无法选中和change事件
      let list = res.data || []
      if (list.length) {
        for (let i of list) {
          for (let n of i.children) {
            if (n.chapterName == '单元测试') {
              n.chapterId+= '单元测试'
            } else if(i.chapterId == n.chapterId) {
              n.chapterId+= '章节'
            }
          }
        }
      }
      options.value = list
      userStore.setChapterList(list)
      selectchapterId.value = getLastId(res.data[0]).chapterIdAll
      chapterId.value = selectchapterId.value[selectchapterId.value.length - 1]
      const firstName = getLastId(res.data[0]).chapterNameAll
      const lastchapterName = getLastId(res.data[0]).chapterNameAll
      let fullName = ''
      firstName.map(item=>{
        fullName += (item + '/')
      })
      fullName +=lastchapterName
      userStore.setChapterId(chapterId.value, fullName)
      // nextTick(() => {

      //   //重新赋值，避免在学情切换学科后获取不到数据
      //   chapterId.value = chapterObj.value.chapterId
      // })

      pageStatus.value = getLastId(res.data[0]).chapterName != "单元测试"
    }
  } catch (error) {
    loading.value = false
    console.log(error)
  }
}
//获取试卷列表
const getChapterReportList = async () => {
  console.log(selectchapterId)
  loading.value = true
  let chapterId2 = chapterId.value
  if (chapterId2.indexOf('单元测试') > -1) {
    //单元测试
    pageStatus.value = false
    try {
      const params = {
        current: pageData.current,
        size: pageData.size,
        bookId: subjectObj.value.bookId,
        chapterId: chapterId2.replace('单元测试',''),
        type: 1
      }
      const res: any = await getChapterReportListApi(params)
      if (res.code == 200) {
        testList2.value = res.data.records || []
        pageData.total = Number(res.data.total)
      }
      loading.value = false
    } catch (error) {
      console.log(error)
      loading.value = false
    }
  } else {
    // 知识点
    pageStatus.value = true
    try {
      const res: any = await getpointListApi({
        chapterId:chapterId2.replace('章节','')
      })
      if(res.code == 200) {
        testList.value = res.data || []
      }
      loading.value = false
    } catch (error) {
      console.log(error)
      loading.value = false
    }
  }
}

const getArrId = (data: any[], targetId: string): { chapterIdAll: string[] } => {
  const path: string[] = [];

  const findPath = (nodes: any[], target: string): boolean => {
    return nodes.some(node => {
      path.push(node.chapterId);
      if (node.chapterId === target) return true;
      if (node.children && findPath(node.children, target)) return true;
      path.pop();
      return false;
    });
  };

  findPath(data, targetId);
  return { chapterIdAll: path };
};
const getLastId = (data: any) => {
  let lastIdAll = [] as any[]
  let lastNameAll = [] as any[]
  let lastchapterName = ""
  const getLastIds = (data1: any) => {
    if (data1.children && data1.children.length > 0) {
      lastIdAll.push(data1.chapterId)
      lastNameAll.push(data1.chapterName)
      return getLastIds(data1.children[0])
    } else {
      lastIdAll.push(data1.chapterId)
      lastchapterName = data1.chapterName
    }
  }
  getLastIds(data)
  return {chapterIdAll: lastIdAll,chapterNameAll: lastNameAll, chapterName: lastchapterName}
}
const handleChange = (value: any) => {
  pageStatus.value = cascaderRef.value.getCheckedNodes()[0].label != "单元测试"
  chapterId.value = value[value.length - 1]
  nextTick(() => {
    userStore.setChapterId(chapterId.value, cascaderRef.value.getCheckedNodes()[0].text)
  })
  console.log(123,chapterId.value,pageStatus.value)

  getChapterReportList()
}
const setSubject = async (data: any) => {
  await getChapterList()
  await getChapterReportList()
}
const goNext = (url: any) => {
  if (getIsMember()) {
    router.push({
      path: url
    })
  }
}
const startLearn = () => {
  router.push({
    path: '/ai_percision/knowledge_graph_detail'
  })
}
const currentSizeChange = (currentPage: number, pageSize: number) => {
  pageData.current = currentPage
  pageData.size = pageSize
  getChapterReportList()
}
const pageClick = (val: number) => {
  pageData.current = val
  getChapterReportList()
}

const testDetail = (data: any) => {
  router.push({
    name:'KnowledgeGraphTestDetail2',
    query: {
      data: dataEncrypt({
        reportId: data.id,
        pageSource: '3'
      }),
    }
  })
}
//下载试卷
const downloadTrestDialogRef = ref()
const dowmloadData = reactive({
    id: '',
    title: ''
})
const dialogVisible = ref(false)
const handleDownload = ({ id, title }: any) => {
  Object.assign(dowmloadData, {
    id: id,
    title: title
  })
  dialogVisible.value = true
  nextTick(() => {
    downloadTrestDialogRef.value.dialogShow()
  })
}
</script>
<style scoped lang="scss">
.graph-content {
  display: flex;
}
.right {
  margin-left: 1.5625rem;
  width: 60rem;
  height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-rotute-tab-height) - .75rem);
  border-radius: 1.25rem 1.25rem 0 0;
  background-image: url(@/assets/img/percision/knowledge_graph.png);
  background-size: cover;
  background-repeat: no-repeat;
  box-sizing: border-box;
}
.test-bgimg {
  background-image: url(@/assets/img/percision/unit_test.png);
}
.bg-notext {
  background-image: url(@/assets/img/percision/bg-notext.png);

}
.mgL {
  margin-left: 13.3125rem;
}
.mg25 {
  margin-left: 1.5625rem;
}
.book-vision {
  margin-top: 1.6875rem;
  display: flex;
  img {
    width: 1.5rem;
    height: 1.5rem;
    margin-right: .5rem;
  }
  .book-vision-text {
    color: #ffffff;
    font-size: 1rem;
    font-weight: 700;
  }
}
.cascader-box {
  margin-left: 2.8125rem;
  margin-top: 3.5rem;
  :deep(.el-cascader) {
    width: 66.125rem;
    height: 2.0625rem;
    .el-input__wrapper {
      border-radius: 1.375rem;
      border: .0625rem solid #00c9a3;
      box-shadow: none;
    }
  }
}
.graph-box {
  margin-left: 2.8125rem;
  // margin-top: 3.5rem;
}
.start-learn {
  display: flex;
  align-items: center;
  flex-direction: column;
  padding: 0 1.5625rem;
  img {
    width: 14.0625rem;
    height: 3.125rem;
  }
  :deep(.el-divider){
    margin-bottom: .5625rem;
  }
  .lefend-box {
    width: 66.125rem;
    height: 3.125rem;
    border-radius: .625rem;
    background: #e5f9f6;
    display: flex;
    &-tip {
      width: 3.5rem;
      height: 1.5625rem;
      line-height: 1.5625rem;
      font-size: .875rem;
      font-weight: 700;
      color: #ffffff;
      border-radius: .625rem 0 .625rem 0;
      background: linear-gradient(144.8deg, #ffce39 0%, #ff9524 100%);
      text-align: center;
    }
    &-item {
      height: 3.125rem;
      color: #666666;
      font-size: .875rem;
      margin-left: 3.8125rem;
      display: flex;
      align-items: center;
      span {
        display: inline-block;
        margin-left: .625rem;
        width: 1.5rem;
        height: .875rem;
        border-radius: .125rem;
      }
    }
  }
}
.green {
  background: #00c9a3;
}
.origin {
  background: #FFAB23;
}
.red {
  background: #E84B4B;
}
.grey {
  background: #DBD6D1;
}
.test-box {
  height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-rotute-tab-height) - 11.25rem);
  width: 67.9375rem;
  margin-left: 1.3125rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 1.25rem;
  position: relative;
  .test-wrap{
      width: calc(100% - 8px);
      .hui-line{
        width: calc(100% - 1.75rem);
        border-bottom: .0625rem dashed #EAEAEA;
        margin: 0 0 0 .875rem;
        float: left;
      }
    }
    .pagination-box{
      padding: 1.25rem 0 5.8rem;
    }
    &-item {
      width: 100%;
      height: 6.875rem;
      display: flex;
      padding: 1.25rem 1.25rem 1.25rem 1.25rem;
      box-sizing: border-box;
      &:hover {
        background: #effdfb;
      }
      &-img {
        width: 3.1875rem;
        height: 100%;
        font-size: .75rem;
        background-image: url(@/assets/img/percision/test-img.png);
        background-size: 100%;
        background-repeat: no-repeat;
        text-align: center;
        span {
          display: inline-block;
          margin-top: 1.85rem;
        }
      }
    &-info {
      margin-left: 1rem;
      width: 54.25rem;
      margin-right: 1rem;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      &-title {
        color: #2a2b2a;
        font-size: 1rem;
        font-weight: 400;
      }
      &-data {
        div {
          height: 1.75rem;
          border-radius: .875rem;
          background: #fef8e9;
          color: #ef9d19;
          display: inline-block;
          box-sizing: border-box;
          padding: .375rem .75rem;
          font-size: .75rem;
          margin-right: .625rem;
        }
      }
    }
    &-btn {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      &-it {
        width: 5.25rem;
        height: 1.875rem;
        line-height: 1.875rem;
        border-radius: .25rem;
        font-size: .875rem;
        text-align: center;
        cursor: pointer;
        img {
          width: .875rem;
          height: .875rem;
        }
      }
    }
  }
  .learn-img {
    position: fixed;
    bottom: 1.875rem;
    left: 48%;
    width: 14.0625rem;
    height: 3.125rem;
    z-index: 99;
  }
}
.btn {
  color: #ffffff;
  background: #00c9a3;
}
.grey-btn {
  background: #f5f5f5;
  color: #999999;
}
.red-text {
  color: #dd2a2a;
}
.blue-text {
  color: #009c7f;
}
.model-box {
  margin: 3.125rem 2.5rem 0 2.5rem;
  display: flex;
  justify-content: space-between;
  &-item {
    width: 21.25rem;
    height: 18.75rem;
    border-radius: 1.25rem;
    border: .125rem solid #00c9a3;
    background: #e5f9f6;
    display: flex;
    align-items: center;
    flex-direction: column;
    cursor: pointer;
    &-title {
      margin-top: 1rem;
      color: #2a2b2a;
      font-size: 1.25rem;
      font-weight: 700;
    }
    &-tip {
      color: #666666;
      font-size: .875rem;
      font-weight: 400;
      margin: 1.0625rem 0;
    }
    &-imgs {
      margin-top: 3.375rem;
      margin-bottom: 3.125rem;
      width: 13.125rem;
      display: flex;
      justify-content: space-between;
      img {
        width: 1.875rem;
        height: 1.875rem;
      }
    }
    &-img {
      img {
        width: 8.125rem;
        height: 8.125rem;
      }
    }
    &-btn {
      width: 7.625rem;
      height: 2.375rem;
      line-height: 2.375rem;
      text-align: center;
      border-radius: 1.1875rem;
      background: #00c9a3;
      color: #ffffff;
      font-size: 1rem;
    }
  }
}
/* 书签 */
.stap {
  float: left;
  width: 100%;
  position: relative;
  z-index: 20;
  margin: -2.25rem 0 -1.875rem;
  top: .5rem;
  box-sizing: border-box;
  padding: 0 1.25rem;
}

.stap_lt {
  float: left;
  width: 6.375rem;
  height: 2.625rem;
}

.stap_rt {
  float: right;
  width: 6.375rem;
  height: 2.625rem;
}
</style>
